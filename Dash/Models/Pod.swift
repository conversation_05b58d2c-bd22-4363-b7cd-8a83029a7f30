import Foundation
import FirebaseFirestore

// MARK: - Pod Status
enum PodStatus: String, Codable, CaseIterable {
    case active
    case archived
    case suspended
    
    var displayName: String {
        switch self {
        case .active: return "Active"
        case .archived: return "Archived"
        case .suspended: return "Suspended"
        }
    }
}

// MARK: - Pod Member Role
enum PodMemberRole: String, Codable, CaseIterable {
    case admin
    case member
    
    var displayName: String {
        switch self {
        case .admin: return "Admin"
        case .member: return "Member"
        }
    }
    
    var canInviteMembers: Bool {
        return self == .admin
    }
    
    var canRemoveMembers: Bool {
        return self == .admin
    }
    
    var canEditPod: Bool {
        return self == .admin
    }
}

// MARK: - Pod Member
struct PodMember: Identifiable, Codable, Equatable, Hashable {
    var id: String
    let userId: String
    let displayName: String
    let email: String?
    let phoneNumber: String?
    var role: PodMemberRole
    let joinedAt: Date
    var balance: Double // Positive = owes money, Negative = is owed money
    var isActive: Bool
    
    init(id: String = UUID().uuidString, userId: String, displayName: String, email: String? = nil, phoneNumber: String? = nil, role: PodMemberRole = .member, joinedAt: Date = Date(), balance: Double = 0.0, isActive: Bool = true) {
        self.id = id
        self.userId = userId
        self.displayName = displayName
        self.email = email
        self.phoneNumber = phoneNumber
        self.role = role
        self.joinedAt = joinedAt
        self.balance = balance
        self.isActive = isActive
    }
    
    enum CodingKeys: String, CodingKey {
        case id, userId, displayName, email, phoneNumber, role, joinedAt, balance, isActive
    }
}

// MARK: - Pod Transaction Type
enum PodTransactionType: String, Codable, CaseIterable {
    case expense
    case payment
    case settlement
    
    var displayName: String {
        switch self {
        case .expense: return "Expense"
        case .payment: return "Payment"
        case .settlement: return "Settlement"
        }
    }
    
    var icon: String {
        switch self {
        case .expense: return "minus.circle"
        case .payment: return "plus.circle"
        case .settlement: return "equal.circle"
        }
    }
}

// MARK: - Pod Transaction
struct PodTransaction: Identifiable, Codable, Equatable {
    @DocumentID var id: String?
    let podId: String
    let createdBy: String
    let createdByName: String
    let type: PodTransactionType
    let amount: Double
    let description: String
    let category: String?
    let date: Date
    var splitAmong: [String] // User IDs who should split this transaction
    var paidBy: String // User ID who paid for this transaction
    var paidByName: String
    var settlements: [String: Double] // [UserID: Amount] - tracks who has paid their share
    var isSettled: Bool
    let createdAt: Date
    var notes: String?
    var attachments: [String]? // URLs to receipts/images
    
    init(podId: String, createdBy: String, createdByName: String, type: PodTransactionType, amount: Double, description: String, category: String? = nil, date: Date = Date(), splitAmong: [String], paidBy: String, paidByName: String, settlements: [String: Double] = [:], isSettled: Bool = false, notes: String? = nil, attachments: [String]? = nil) {
        self.podId = podId
        self.createdBy = createdBy
        self.createdByName = createdByName
        self.type = type
        self.amount = amount
        self.description = description
        self.category = category
        self.date = date
        self.splitAmong = splitAmong
        self.paidBy = paidBy
        self.paidByName = paidByName
        self.settlements = settlements
        self.isSettled = isSettled
        self.createdAt = Date()
        self.notes = notes
        self.attachments = attachments
    }
    
    enum CodingKeys: String, CodingKey {
        case id, podId, createdBy, createdByName, type, amount, description, category, date
        case splitAmong, paidBy, paidByName, settlements, isSettled, createdAt, notes, attachments
    }
    
    // Computed properties
    var amountPerPerson: Double {
        guard !splitAmong.isEmpty else { return 0 }
        return amount / Double(splitAmong.count)
    }
    
    var totalSettled: Double {
        return settlements.values.reduce(0, +)
    }
    
    var remainingAmount: Double {
        return amount - totalSettled
    }
}

// MARK: - Payment Request Status
enum PaymentRequestStatus: String, Codable, CaseIterable {
    case pending
    case paid
    case declined
    case expired
    
    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .paid: return "Paid"
        case .declined: return "Declined"
        case .expired: return "Expired"
        }
    }
}

// MARK: - Payment Request
struct PaymentRequest: Identifiable, Codable, Equatable {
    @DocumentID var id: String?
    let podId: String
    let fromUserId: String
    let fromUserName: String
    let toUserId: String
    let toUserName: String
    let amount: Double
    let description: String
    let transactionId: String? // Reference to the pod transaction this request is for
    var status: PaymentRequestStatus
    let createdAt: Date
    var respondedAt: Date?
    var expiresAt: Date
    var notes: String?
    
    init(podId: String, fromUserId: String, fromUserName: String, toUserId: String, toUserName: String, amount: Double, description: String, transactionId: String? = nil, status: PaymentRequestStatus = .pending, expiresAt: Date = Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date(), notes: String? = nil) {
        self.podId = podId
        self.fromUserId = fromUserId
        self.fromUserName = fromUserName
        self.toUserId = toUserId
        self.toUserName = toUserName
        self.amount = amount
        self.description = description
        self.transactionId = transactionId
        self.status = status
        self.createdAt = Date()
        self.expiresAt = expiresAt
        self.notes = notes
    }
    
    enum CodingKeys: String, CodingKey {
        case id, podId, fromUserId, fromUserName, toUserId, toUserName, amount, description
        case transactionId, status, createdAt, respondedAt, expiresAt, notes
    }
    
    var isExpired: Bool {
        return Date() > expiresAt && status == .pending
    }
}

// MARK: - Pod Invitation Status
enum PodInvitationStatus: String, Codable, CaseIterable {
    case pending
    case accepted
    case declined
    case expired
    
    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .accepted: return "Accepted"
        case .declined: return "Declined"
        case .expired: return "Expired"
        }
    }
}

// MARK: - Pod Invitation
struct PodInvitation: Identifiable, Codable, Equatable {
    @DocumentID var id: String?
    let podId: String
    let podName: String
    let invitedBy: String
    let invitedByName: String
    let invitedEmail: String?
    let invitedPhoneNumber: String?
    let invitedUserId: String? // If user exists in system
    var status: PodInvitationStatus
    let createdAt: Date
    var respondedAt: Date?
    var expiresAt: Date
    let inviteCode: String // Unique code for joining
    
    init(podId: String, podName: String, invitedBy: String, invitedByName: String, invitedEmail: String? = nil, invitedPhoneNumber: String? = nil, invitedUserId: String? = nil, status: PodInvitationStatus = .pending, expiresAt: Date = Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date()) {
        self.podId = podId
        self.podName = podName
        self.invitedBy = invitedBy
        self.invitedByName = invitedByName
        self.invitedEmail = invitedEmail
        self.invitedPhoneNumber = invitedPhoneNumber
        self.invitedUserId = invitedUserId
        self.status = status
        self.createdAt = Date()
        self.expiresAt = expiresAt
        self.inviteCode = UUID().uuidString.replacingOccurrences(of: "-", with: "").prefix(8).uppercased()
    }
    
    enum CodingKeys: String, CodingKey {
        case id, podId, podName, invitedBy, invitedByName, invitedEmail, invitedPhoneNumber
        case invitedUserId, status, createdAt, respondedAt, expiresAt, inviteCode
    }
    
    var isExpired: Bool {
        return Date() > expiresAt && status == .pending
    }
}

// MARK: - Main Pod Model
struct Pod: Identifiable, Codable, Equatable {
    @DocumentID var id: String?
    let name: String
    let description: String?
    let createdBy: String
    let createdByName: String
    var members: [PodMember]
    var status: PodStatus
    let createdAt: Date
    var updatedAt: Date
    var totalExpenses: Double
    var totalPayments: Double
    var currency: String
    var isPrivate: Bool
    var maxMembers: Int
    var tags: [String]
    var imageUrl: String?
    
    init(name: String, description: String? = nil, createdBy: String, createdByName: String, currency: String = "USD", isPrivate: Bool = true, maxMembers: Int = 50, tags: [String] = [], imageUrl: String? = nil) {
        self.name = name
        self.description = description
        self.createdBy = createdBy
        self.createdByName = createdByName
        self.members = [PodMember(userId: createdBy, displayName: createdByName, role: .admin)]
        self.status = .active
        self.createdAt = Date()
        self.updatedAt = Date()
        self.totalExpenses = 0.0
        self.totalPayments = 0.0
        self.currency = currency
        self.isPrivate = isPrivate
        self.maxMembers = maxMembers
        self.tags = tags
        self.imageUrl = imageUrl
    }
    
    enum CodingKeys: String, CodingKey {
        case id, name, description, createdBy, createdByName, members, status
        case createdAt, updatedAt, totalExpenses, totalPayments, currency
        case isPrivate, maxMembers, tags, imageUrl
    }
    
    // Computed properties
    var netBalance: Double {
        return totalPayments - totalExpenses
    }
    
    var activeMemberCount: Int {
        return members.filter { $0.isActive }.count
    }
    
    var adminMembers: [PodMember] {
        return members.filter { $0.role == .admin && $0.isActive }
    }
    
    var regularMembers: [PodMember] {
        return members.filter { $0.role == .member && $0.isActive }
    }
    
    func memberBalance(for userId: String) -> Double {
        return members.first { $0.userId == userId }?.balance ?? 0.0
    }
    
    func isAdmin(userId: String) -> Bool {
        return members.first { $0.userId == userId }?.role == .admin
    }
    
    func isMember(userId: String) -> Bool {
        return members.contains { $0.userId == userId && $0.isActive }
    }
    
    mutating func addMember(_ member: PodMember) {
        if !members.contains(where: { $0.userId == member.userId }) {
            members.append(member)
            updatedAt = Date()
        }
    }
    
    mutating func removeMember(userId: String) {
        if let index = members.firstIndex(where: { $0.userId == userId }) {
            members[index].isActive = false
            updatedAt = Date()
        }
    }
    
    mutating func updateMemberBalance(userId: String, newBalance: Double) {
        if let index = members.firstIndex(where: { $0.userId == userId }) {
            members[index].balance = newBalance
            updatedAt = Date()
        }
    }
}
