import Foundation
import FirebaseFirestore

enum SplitStatus: String, Codable {
    case pending
    case complete
    case cancelled
}

struct Split: Identifiable, Codable, Equatable {
    static func == (lhs: Split, rhs: Split) -> Bool {
        lhs.id == rhs.id
    }
    var id: String?
    let creatorId: String
    let totalAmount: Double
    let currency: String
    var numberOfParticipants: Int
    var participants: [Participant] // Array of user IDs
    var paidParticipants: [String: Double] // [UserID: AmountPaid]
    let createdAt: Date
    var status: SplitStatus
    
    enum CodingKeys: String, CodingKey {
        case id
        case creatorId
        case totalAmount
        case currency
        case numberOfParticipants
        case participants
        case paidParticipants
        case createdAt
        case status
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // The @DocumentID is not available, so we decode the id manually.
        // Firestore's `data(as:)` method will automatically map the document ID to this property.
        id = try container.decodeIfPresent(String.self, forKey: .id)
        creatorId = try container.decode(String.self, forKey: .creatorId)
        totalAmount = try container.decode(Double.self, forKey: .totalAmount)
        currency = try container.decode(String.self, forKey: .currency)
        numberOfParticipants = try container.decode(Int.self, forKey: .numberOfParticipants)
        participants = try container.decode([Participant].self, forKey: .participants)
        paidParticipants = try container.decode([String: Double].self, forKey: .paidParticipants)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        status = try container.decode(SplitStatus.self, forKey: .status)
        
        print("Split decoded - ID from container: \(id ?? "nil")")
    }
    
    // Add a default init to satisfy the protocol requirements and allow for easy instantiation.
    init(id: String?, creatorId: String, totalAmount: Double, currency: String, numberOfParticipants: Int, participants: [Participant], paidParticipants: [String: Double], createdAt: Date, status: SplitStatus) {
        self.id = id
        self.creatorId = creatorId
        self.totalAmount = totalAmount
        self.currency = currency
        self.numberOfParticipants = numberOfParticipants
        self.participants = participants
        self.paidParticipants = paidParticipants
        self.createdAt = createdAt
        self.status = status
    }
}
