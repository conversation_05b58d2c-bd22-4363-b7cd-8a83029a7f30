import Foundation
import FirebaseFirestore

enum SecurityLevel: String, Codable, CaseIterable {
    case basic = "basic"
    case enhanced = "enhanced"
    case maximum = "maximum"

    var displayName: String {
        switch self {
        case .basic: return "Basic"
        case .enhanced: return "Enhanced"
        case .maximum: return "Maximum"
        }
    }
}

// MARK: - Subscription Tier Enum
enum SubscriptionTier: String, Codable, CaseIterable {
    case classic = "classic"
    case premium = "premium"
    case reserve = "reserve"

    var displayName: String {
        switch self {
        case .classic: return "Classic"
        case .premium: return "Premium"
        case .reserve: return "Reserve"
        }
    }

    var monthlyPrice: Double {
        switch self {
        case .classic: return 0.0
        case .premium: return 9.99
        case .reserve: return 19.99
        }
    }

    var features: [String] {
        switch self {
        case .classic:
            return [
                "Basic money transfers",
                "Split bills with friends",
                "Virtual card access",
                "Standard support"
            ]
        case .premium:
            return [
                "All Classic features",
                "Priority support",
                "Advanced analytics",
                "Custom card designs",
                "Higher transaction limits"
            ]
        case .reserve:
            return [
                "All Premium features",
                "Dedicated account manager",
                "Investment opportunities",
                "Exclusive events access",
                "Premium card materials"
            ]
        }
    }
}

// MARK: - Subscription Status Enum
enum SubscriptionStatus: String, Codable, CaseIterable {
    case active = "active"
    case expired = "expired"
    case cancelled = "cancelled"
    case pending = "pending"

    var displayName: String {
        switch self {
        case .active: return "Active"
        case .expired: return "Expired"
        case .cancelled: return "Cancelled"
        case .pending: return "Pending"
        }
    }
}

// MARK: - Subscription Model
struct Subscription: Codable, Equatable {
    var tier: SubscriptionTier
    var status: SubscriptionStatus
    var startDate: Date
    var endDate: Date
    var autoRenew: Bool
    var lastPaymentDate: Date?
    var nextPaymentDate: Date?
    var priceAtPurchase: Double

    init(tier: SubscriptionTier, startDate: Date = Date(), autoRenew: Bool = true) {
        self.tier = tier
        self.status = .active
        self.startDate = startDate
        self.endDate = Calendar.current.date(byAdding: .day, value: 30, to: startDate) ?? startDate
        self.autoRenew = autoRenew
        self.priceAtPurchase = tier.monthlyPrice

        if tier != .classic {
            self.lastPaymentDate = startDate
            self.nextPaymentDate = self.endDate
        }
    }

    var isActive: Bool {
        return status == .active && Date() <= endDate
    }

    var daysRemaining: Int {
        let calendar = Calendar.current
        let now = Date()
        if now > endDate {
            return 0
        }
        return calendar.dateComponents([.day], from: now, to: endDate).day ?? 0
    }
}

struct TrustedDevice: Codable, Equatable {
    let deviceId: String
    let deviceName: String
    let addedDate: Date
    let lastUsed: Date
    
    init(deviceId: String, deviceName: String) {
        self.deviceId = deviceId
        self.deviceName = deviceName
        self.addedDate = Date()
        self.lastUsed = Date()
    }
}

struct User: Identifiable, Codable, Equatable {
    var id: String?
    var uid: String?
    var email: String?
    var displayName: String?
    var balance: Double?
    var phoneNumber: String?
    
    // Enhanced Security Fields
    var twoFactorEnabled: Bool = false
    var twoFactorSetupDate: Date?
    var trustedDevices: [TrustedDevice] = []
    var lastSecurityAudit: Date?
    var securityLevel: SecurityLevel = .basic
    var failedLoginAttempts: Int = 0
    var lastFailedLoginAttempt: Date?
    var accountLockedUntil: Date?
    var biometricEnabled: Bool = false
    var sessionTimeout: TimeInterval = 900 // 15 minutes default
    var requireTwoFactorForTransactions: Bool = false
    var backupCodesUsed: [String] = []
    var lastPasswordChange: Date?
    var securityNotificationsEnabled: Bool = true

    // Subscription Fields
    var subscription: Subscription = Subscription(tier: .classic)
    
    // Custom initializer to handle missing fields gracefully
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Required fields
        id = try container.decodeIfPresent(String.self, forKey: .id)
        uid = try container.decodeIfPresent(String.self, forKey: .uid)
        email = try container.decodeIfPresent(String.self, forKey: .email)
        displayName = try container.decodeIfPresent(String.self, forKey: .displayName)
        balance = try container.decodeIfPresent(Double.self, forKey: .balance) ?? 0.0
        phoneNumber = try container.decodeIfPresent(String.self, forKey: .phoneNumber)
        
        // Security fields with defaults
        twoFactorEnabled = try container.decodeIfPresent(Bool.self, forKey: .twoFactorEnabled) ?? false
        twoFactorSetupDate = try container.decodeIfPresent(Date.self, forKey: .twoFactorSetupDate)
        trustedDevices = try container.decodeIfPresent([TrustedDevice].self, forKey: .trustedDevices) ?? []
        lastSecurityAudit = try container.decodeIfPresent(Date.self, forKey: .lastSecurityAudit)
        securityLevel = try container.decodeIfPresent(SecurityLevel.self, forKey: .securityLevel) ?? .basic
        failedLoginAttempts = try container.decodeIfPresent(Int.self, forKey: .failedLoginAttempts) ?? 0
        lastFailedLoginAttempt = try container.decodeIfPresent(Date.self, forKey: .lastFailedLoginAttempt)
        accountLockedUntil = try container.decodeIfPresent(Date.self, forKey: .accountLockedUntil)
        biometricEnabled = try container.decodeIfPresent(Bool.self, forKey: .biometricEnabled) ?? false
        sessionTimeout = try container.decodeIfPresent(TimeInterval.self, forKey: .sessionTimeout) ?? 900
        requireTwoFactorForTransactions = try container.decodeIfPresent(Bool.self, forKey: .requireTwoFactorForTransactions) ?? false
        backupCodesUsed = try container.decodeIfPresent([String].self, forKey: .backupCodesUsed) ?? []
        lastPasswordChange = try container.decodeIfPresent(Date.self, forKey: .lastPasswordChange)
        securityNotificationsEnabled = try container.decodeIfPresent(Bool.self, forKey: .securityNotificationsEnabled) ?? true

        // Subscription field with default
        subscription = try container.decodeIfPresent(Subscription.self, forKey: .subscription) ?? Subscription(tier: .classic)
    }
    
    // Default initializer
    init(id: String? = nil, uid: String? = nil, email: String? = nil, displayName: String? = nil, balance: Double? = nil, phoneNumber: String? = nil) {
        self.id = id
        self.uid = uid
        self.email = email
        self.displayName = displayName
        self.balance = balance ?? 0.0
        self.phoneNumber = phoneNumber
        
        // Set defaults for security fields
        self.twoFactorEnabled = false
        self.twoFactorSetupDate = nil
        self.trustedDevices = []
        self.lastSecurityAudit = nil
        self.securityLevel = .basic
        self.failedLoginAttempts = 0
        self.lastFailedLoginAttempt = nil
        self.accountLockedUntil = nil
        self.biometricEnabled = false
        self.sessionTimeout = 900
        self.requireTwoFactorForTransactions = false
        self.backupCodesUsed = []
        self.lastPasswordChange = nil
        self.securityNotificationsEnabled = true

        // Set default subscription
        self.subscription = Subscription(tier: .classic)
    }
    
    // CodingKeys enum
    enum CodingKeys: String, CodingKey {
        case id, uid, email, displayName, balance, phoneNumber
        case twoFactorEnabled, twoFactorSetupDate, trustedDevices, lastSecurityAudit
        case securityLevel, failedLoginAttempts, lastFailedLoginAttempt, accountLockedUntil
        case biometricEnabled, sessionTimeout, requireTwoFactorForTransactions
        case backupCodesUsed, lastPasswordChange, securityNotificationsEnabled
        case subscription
    }
    
    // Computed Properties
    var initial: String {
        return String(displayName?.first ?? email?.first ?? "U").uppercased()
    }
    
    var isAccountLocked: Bool {
        guard let lockedUntil = accountLockedUntil else { return false }
        return Date() < lockedUntil
    }
    
    var shouldPromptFor2FA: Bool {
        return !twoFactorEnabled && (twoFactorSetupDate == nil ||
               Date().timeIntervalSince(twoFactorSetupDate ?? Date()) > 86400 * 7) // 7 days
    }

    var currentTier: SubscriptionTier {
        return subscription.isActive ? subscription.tier : .classic
    }
    
    var securityScore: Int {
        var score = 0
        if twoFactorEnabled { score += 30 }
        if biometricEnabled { score += 20 }
        if !trustedDevices.isEmpty { score += 15 }
        if phoneNumber != nil && !phoneNumber!.isEmpty { score += 10 }
        if securityLevel == .enhanced { score += 15 }
        if securityLevel == .maximum { score += 25 }
        if requireTwoFactorForTransactions { score += 10 }
        return min(score, 100)
    }
}
