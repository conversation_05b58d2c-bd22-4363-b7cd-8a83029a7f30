import SwiftUI

// MARK: - Typography System
extension Font {
    static let theme = TypographyTheme()
}

struct TypographyTheme {
    // Display Fonts (Large headings)
    let displayLarge = Font.system(size: 57, weight: .regular, design: .rounded)
    let displayMedium = Font.system(size: 45, weight: .regular, design: .rounded)
    let displaySmall = Font.system(size: 36, weight: .regular, design: .rounded)
    
    // Headline Fonts
    let headlineLarge = Font.system(size: 32, weight: .semibold, design: .rounded)
    let headlineMedium = Font.system(size: 28, weight: .semibold, design: .rounded)
    let headlineSmall = Font.system(size: 24, weight: .semibold, design: .rounded)
    
    // Title Fonts
    let titleLarge = Font.system(size: 22, weight: .medium, design: .rounded)
    let titleMedium = Font.system(size: 16, weight: .medium, design: .rounded)
    let titleSmall = Font.system(size: 14, weight: .medium, design: .rounded)
    
    // Body Fonts
    let bodyLarge = Font.system(size: 16, weight: .regular, design: .default)
    let bodyMedium = Font.system(size: 14, weight: .regular, design: .default)
    let bodySmall = Font.system(size: 12, weight: .regular, design: .default)
    
    // Label Fonts
    let labelLarge = Font.system(size: 14, weight: .medium, design: .default)
    let labelMedium = Font.system(size: 12, weight: .medium, design: .default)
    let labelSmall = Font.system(size: 11, weight: .medium, design: .default)
    
    // Monospace (for numbers, codes)
    let monoLarge = Font.system(size: 16, weight: .regular, design: .monospaced)
    let monoMedium = Font.system(size: 14, weight: .regular, design: .monospaced)
    let monoSmall = Font.system(size: 12, weight: .regular, design: .monospaced)
}

// MARK: - Spacing System
struct Spacing {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 20
    static let xxl: CGFloat = 24
    static let xxxl: CGFloat = 32
    static let xxxxl: CGFloat = 40
    static let xxxxxl: CGFloat = 48
    static let xxxxxxl: CGFloat = 64
}

// MARK: - Border Radius System
struct BorderRadius {
    static let xs: CGFloat = 4
    static let sm: CGFloat = 8
    static let md: CGFloat = 12
    static let lg: CGFloat = 16
    static let xl: CGFloat = 20
    static let xxl: CGFloat = 24
    static let xxxl: CGFloat = 32
    static let full: CGFloat = 9999
}

// MARK: - Shadow System
struct Shadows {
    static let none = Shadow(color: .clear, radius: 0, x: 0, y: 0)
    
    static let xs = Shadow(
        color: Color.black.opacity(0.05),
        radius: 1,
        x: 0,
        y: 1
    )
    
    static let sm = Shadow(
        color: Color.black.opacity(0.1),
        radius: 3,
        x: 0,
        y: 1
    )
    
    static let md = Shadow(
        color: Color.black.opacity(0.1),
        radius: 6,
        x: 0,
        y: 4
    )
    
    static let lg = Shadow(
        color: Color.black.opacity(0.1),
        radius: 15,
        x: 0,
        y: 10
    )
    
    static let xl = Shadow(
        color: Color.black.opacity(0.15),
        radius: 25,
        x: 0,
        y: 20
    )
}

struct Shadow {
    let color: Color
    let radius: CGFloat
    let x: CGFloat
    let y: CGFloat
}

// MARK: - Animation System
struct Animations {
    static let fast = Animation.easeInOut(duration: 0.2)
    static let medium = Animation.easeInOut(duration: 0.3)
    static let slow = Animation.easeInOut(duration: 0.5)
    static let spring = Animation.spring(response: 0.5, dampingFraction: 0.8)
    static let bouncy = Animation.spring(response: 0.3, dampingFraction: 0.6)
}

// MARK: - View Modifiers
struct ModernCardModifier: ViewModifier {
    let shadow: Shadow
    let cornerRadius: CGFloat
    
    init(shadow: Shadow = Shadows.sm, cornerRadius: CGFloat = BorderRadius.lg) {
        self.shadow = shadow
        self.cornerRadius = cornerRadius
    }
    
    func body(content: Content) -> some View {
        content
            .background(Color.theme.surface)
            .cornerRadius(cornerRadius)
            .shadow(
                color: shadow.color,
                radius: shadow.radius,
                x: shadow.x,
                y: shadow.y
            )
    }
}

struct ModernButtonModifier: ViewModifier {
    let style: ButtonStyle
    let size: ButtonSize
    let isDisabled: Bool
    
    enum ButtonStyle {
        case primary, secondary, tertiary, destructive
    }
    
    enum ButtonSize {
        case small, medium, large
    }
    
    init(style: ButtonStyle = .primary, size: ButtonSize = .medium, isDisabled: Bool = false) {
        self.style = style
        self.size = size
        self.isDisabled = isDisabled
    }
    
    func body(content: Content) -> some View {
        content
            .font(fontForSize)
            .foregroundColor(foregroundColor)
            .padding(.horizontal, horizontalPadding)
            .padding(.vertical, verticalPadding)
            .background(backgroundColor)
            .cornerRadius(BorderRadius.md)
            .opacity(isDisabled ? 0.6 : 1.0)
            .scaleEffect(isDisabled ? 0.98 : 1.0)
            .animation(Animations.fast, value: isDisabled)
    }
    
    private var fontForSize: Font {
        switch size {
        case .small: return Font.theme.labelMedium
        case .medium: return Font.theme.titleMedium
        case .large: return Font.theme.titleLarge
        }
    }
    
    private var horizontalPadding: CGFloat {
        switch size {
        case .small: return Spacing.md
        case .medium: return Spacing.xl
        case .large: return Spacing.xxl
        }
    }
    
    private var verticalPadding: CGFloat {
        switch size {
        case .small: return Spacing.sm
        case .medium: return Spacing.md
        case .large: return Spacing.lg
        }
    }
    
    private var foregroundColor: Color {
        switch style {
        case .primary: return Color.theme.textInverse
        case .secondary: return Color.theme.primary
        case .tertiary: return Color.theme.textPrimary
        case .destructive: return Color.theme.textInverse
        }
    }
    
    private var backgroundColor: Color {
        switch style {
        case .primary: return Color.theme.primary
        case .secondary: return Color.theme.primaryLight
        case .tertiary: return Color.theme.neutral100
        case .destructive: return Color.theme.error
        }
    }
}

struct ModernTextFieldModifier: ViewModifier {
    let isFocused: Bool
    let hasError: Bool
    
    init(isFocused: Bool = false, hasError: Bool = false) {
        self.isFocused = isFocused
        self.hasError = hasError
    }
    
    func body(content: Content) -> some View {
        content
            .font(Font.theme.bodyLarge)
            .padding(Spacing.lg)
            .background(Color.theme.surfaceSecondary)
            .cornerRadius(BorderRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: BorderRadius.md)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .animation(Animations.fast, value: isFocused)
            .animation(Animations.fast, value: hasError)
    }
    
    private var borderColor: Color {
        if hasError {
            return Color.theme.error
        } else if isFocused {
            return Color.theme.primary
        } else {
            return Color.clear
        }
    }
    
    private var borderWidth: CGFloat {
        (isFocused || hasError) ? 2 : 0
    }
}

// MARK: - View Extensions
extension View {
    func modernCard(shadow: Shadow = Shadows.sm, cornerRadius: CGFloat = BorderRadius.lg) -> some View {
        modifier(ModernCardModifier(shadow: shadow, cornerRadius: cornerRadius))
    }
    
    func modernButton(
        style: ModernButtonModifier.ButtonStyle = .primary,
        size: ModernButtonModifier.ButtonSize = .medium,
        isDisabled: Bool = false
    ) -> some View {
        modifier(ModernButtonModifier(style: style, size: size, isDisabled: isDisabled))
    }
    
    func modernTextField(isFocused: Bool = false, hasError: Bool = false) -> some View {
        modifier(ModernTextFieldModifier(isFocused: isFocused, hasError: hasError))
    }
    
    func hapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle = .medium) -> some View {
        onTapGesture {
            let impactFeedback = UIImpactFeedbackGenerator(style: style)
            impactFeedback.impactOccurred()
        }
    }
}
