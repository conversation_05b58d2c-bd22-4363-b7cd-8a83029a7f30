import SwiftUI

extension Color {
    static let theme = ColorTheme()
}

struct ColorTheme {
    // MARK: - Primary Colors
    let accent = Color("AccentColor")
    let background = Color("BackgroundColor")
    let secondaryText = Color("SecondaryTextColor")

    // MARK: - Modern Color Palette
    // Primary Brand Colors
    let primary = Color(red: 0.0, green: 0.48, blue: 1.0) // #007AFF - iOS Blue
    let primaryDark = Color(red: 0.0, green: 0.34, blue: 0.8) // #0056CC
    let primaryLight = Color(red: 0.89, green: 0.95, blue: 1.0) // #E3F2FD

    // Neutral Colors (Dynamic for light/dark mode)
    let neutral50 = Color(.systemGray6)
    let neutral100 = Color(.systemGray5)
    let neutral200 = Color(.systemGray4)
    let neutral300 = Color(.systemGray3)
    let neutral400 = Color(.systemGray2)
    let neutral500 = Color(.systemGray)
    let neutral600 = Color(.systemGray2)
    let neutral700 = Color(.systemGray3)
    let neutral800 = Color(.systemGray4)
    let neutral900 = Color(.systemGray5)

    // Semantic Colors
    let success = Color(red: 0.20, green: 0.78, blue: 0.35) // #34C759
    let warning = Color(red: 1.0, green: 0.58, blue: 0.0) // #FF9500
    let error = Color(red: 1.0, green: 0.23, blue: 0.19) // #FF3B30
    let info = Color(red: 0.35, green: 0.34, blue: 0.84) // #5856D6

    // Surface Colors (Dynamic for light/dark mode)
    let surface = Color(.systemBackground)
    let surfaceSecondary = Color(.secondarySystemBackground)
    let surfaceTertiary = Color(.tertiarySystemBackground)

    // Text Colors (Dynamic for light/dark mode)
    let textPrimary = Color(.label)
    let textSecondary = Color(.secondaryLabel)
    let textTertiary = Color(.tertiaryLabel)
    let textInverse = Color(.systemBackground)

    // Card Gradients (Adaptive for light/dark mode)
    var cardGradient1: LinearGradient {
        LinearGradient(
            colors: [primary, info],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    var cardGradient2: LinearGradient {
        LinearGradient(
            colors: [Color(.systemGray), Color(.systemGray2)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    var cardGradient3: LinearGradient {
        LinearGradient(
            colors: [success, primary],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}