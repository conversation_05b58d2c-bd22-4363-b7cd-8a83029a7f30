import SwiftUI

struct AuthView: View {
    @State private var email = ""
    @State private var password = ""
    @State private var isShowingSignUp = false
    @State private var isShowingForgotPassword = false
    @State private var showErrorAlert = false
    @State private var showSuccessAlert = false
    @State private var isShowingVerification = false
    @EnvironmentObject var viewModel: AuthViewModel
    @Environment(\.dismiss) var dismiss

    var body: some View {
        ZStack {
            // Modern gradient background
            LinearGradient(
                colors: [Color.theme.surfaceSecondary, Color.theme.surface],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea(.all)

            content
        }
        .sheet(isPresented: $isShowingSignUp) {
            SignUpView()
        }
        .sheet(isPresented: $isShowingForgotPassword) {
            ForgotPasswordView()
        }
        .onChange(of: viewModel.errorMessage) { _, newValue in
            if newValue != nil {
                showErrorAlert = true
            }
        }
        .onChange(of: viewModel.successMessage) { _, newValue in
            if newValue != nil {
                showSuccessAlert = true
            }
        }
        .alert("Authentication Error", isPresented: $showErrorAlert) {
            Button("OK") {
                viewModel.errorMessage = nil
            }
        } message: {
            Text(viewModel.errorMessage ?? "An unknown error occurred.")
        }
        .alert("Success", isPresented: $showSuccessAlert) {
            Button("OK") {
                viewModel.successMessage = nil
            }
        } message: {
            Text(viewModel.successMessage ?? "")
        }
        .onAppear {
            // Clear any existing error messages when the auth view appears
            viewModel.clearErrorState()
        }
    }

    private var content: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: Spacing.xl) {
                Spacer(minLength: 60)

                // Header with logo
                VStack(spacing: Spacing.lg) {
                    ZStack {
                        Circle()
                            .fill(Color.white)
                            .frame(width: 80, height: 80)

                        Image("DashLogo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 50, height: 50)
                            .foregroundColor(Color.theme.success)
                    }

                    VStack(spacing: Spacing.sm) {
                        Text("Welcome Back")
                            .font(Font.theme.displaySmall)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)

                        Text("Sign in to your Dash account")
                            .font(Font.theme.bodyLarge)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }

                // Login Form Card
                ModernCard(padding: Spacing.xl) {
                    VStack(spacing: Spacing.lg) {
                        AuthTextField(
                            title: "Email",
                            text: $email,
                            placeholder: "Enter your email address"
                        )
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)

                        AuthTextField(
                            title: "Password",
                            text: $password,
                            placeholder: "Enter your password",
                            isSecure: true
                        )

                        Button(action: {
                            viewModel.signIn(withEmail: email, password: password)
                        }) {
                            HStack {
                                if viewModel.isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                } else {
                                    Text("Sign In")
                                        .font(Font.theme.titleLarge)
                                        .fontWeight(.semibold)
                                }
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, Spacing.md)
                            .background(Color.theme.success)
                            .cornerRadius(BorderRadius.lg)
                        }
                        .disabled(email.isEmpty || password.isEmpty)

                        Button(action: {
                            isShowingForgotPassword.toggle()
                        }) {
                            Text("Forgot Password?")
                                .font(Font.theme.bodyMedium)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.success)
                        }
                    }
                }

                // Divider
                HStack(spacing: Spacing.md) {
                    Rectangle()
                        .fill(Color.theme.neutral300)
                        .frame(height: 1)

                    Text("or")
                        .font(Font.theme.labelMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .padding(.horizontal, Spacing.sm)

                    Rectangle()
                        .fill(Color.theme.neutral300)
                        .frame(height: 1)
                }

                // Google Sign In
                Button(action: {
                    viewModel.signInWithGoogle()
                }) {
                    HStack(spacing: Spacing.sm) {
                        Image(systemName: "globe")
                            .font(.system(size: 18, weight: .medium))

                        Text("Continue with Google")
                            .font(Font.theme.titleLarge)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(Color.theme.success)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, Spacing.md)
                    .background(Color.theme.success.opacity(0.1))
                    .cornerRadius(BorderRadius.lg)
                }

                Spacer(minLength: 40)

                // Sign Up Link
                Button(action: {
                    isShowingSignUp.toggle()
                }) {
                    HStack(spacing: 4) {
                        Text("Don't have an account?")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)

                        Text("Sign Up")
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.success)
                    }
                }
                .padding(.bottom, Spacing.lg)
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
}

// MARK: - Auth Text Field Component
struct AuthTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    let isSecure: Bool
    @State private var isFocused = false

    init(title: String, text: Binding<String>, placeholder: String, isSecure: Bool = false) {
        self.title = title
        self._text = text
        self.placeholder = placeholder
        self.isSecure = isSecure
    }

    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text(title)
                .font(Font.theme.labelMedium)
                .fontWeight(.medium)
                .foregroundColor(Color.theme.textPrimary)

            Group {
                if isSecure {
                    SecureField(placeholder, text: $text, onCommit: {
                        isFocused = false
                    })
                    .font(Font.theme.bodyLarge)
                    .onTapGesture {
                        isFocused = true
                    }
                } else {
                    TextField(placeholder, text: $text, onEditingChanged: { editing in
                        isFocused = editing
                    })
                    .font(Font.theme.bodyLarge)
                }
            }
            .padding(Spacing.lg)
            .background(Color.theme.surfaceSecondary)
            .cornerRadius(BorderRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: BorderRadius.md)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .animation(Animations.fast, value: isFocused)
        }
    }

    private var borderColor: Color {
        isFocused ? Color.theme.success : Color.clear
    }

    private var borderWidth: CGFloat {
        isFocused ? 2 : 0
    }
}

struct AuthView_Previews: PreviewProvider {
    static var previews: some View {
        AuthView()
            .environmentObject(AuthViewModel.shared)
    }
}
