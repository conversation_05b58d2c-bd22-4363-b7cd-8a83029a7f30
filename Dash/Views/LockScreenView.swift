import SwiftUI
import LocalAuthentication

struct LockScreenView: View {
    @EnvironmentObject var securityViewModel: SecurityViewModel
    @State private var pin: String = ""
    @State private var errorMessage: String?
    @FocusState private var isPinFieldFocused: Bool
    
    var body: some View {
        VStack(spacing: 30) {
            Text("Enter PIN")
                .font(.largeTitle)
                .fontWeight(.bold)

            PINInputView(pin: $pin, isFocused: $isPinFieldFocused)
                .onChange(of: pin) { newValue in
                    if newValue.count == 4 { // PIN length
                        authenticate()
                    }
                }

            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .font(.caption)
            }
            
            if securityViewModel.failedPinAttempts > 0 {
                Text("Attempts remaining: \(securityViewModel.getRemainingAttempts())")
                    .foregroundColor(.orange)
                    .font(.caption)
            }
            
            if securityViewModel.isLockedOut {
                VStack(spacing: 10) {
                    Text("Account Locked")
                        .foregroundColor(.red)
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    Text("Too many failed attempts. Your PIN has been reset and you will be logged out.")
                        .foregroundColor(.red)
                        .font(.caption)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
            } else {
                // Only show biometric button if not locked out
                Button(action: {
                    print("DEBUG: Manual biometric authentication requested")
                    authenticateWithBiometrics()
                }) {
                    HStack {
                        Image(systemName: getBiometricIcon())
                            .font(.title2)
                        Text(getBiometricText())
                            .font(.subheadline)
                    }
                    .foregroundColor(.blue)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(10)
                }
                .disabled(securityViewModel.isLockedOut)
            }
        }
        .onAppear {
            print("DEBUG: LockScreenView appeared")
            // Try biometric authentication first, then focus PIN field
            authenticateWithBiometrics()
        }
        .onChange(of: securityViewModel.isLockedOut) { isLockedOut in
            if isLockedOut {
                // Dismiss keyboard when user gets locked out
                isPinFieldFocused = false
                print("DEBUG: User locked out, dismissing keyboard")
            }
        }
        .onDisappear {
            // Always dismiss keyboard when leaving the lock screen
            isPinFieldFocused = false
            print("DEBUG: LockScreenView disappeared, dismissing keyboard")
        }
    }
    
    private func authenticate() {
        if securityViewModel.authenticateWithPin(pin) {
            // PIN is correct, dismiss the lock screen
            securityViewModel.isPromptingForPin = false
        } else {
            errorMessage = "Incorrect PIN"
            pin = ""
            // Briefly unfocus and then refocus to allow UI to update and re-trigger keyboard
            isPinFieldFocused = false
            DispatchQueue.main.async {
                isPinFieldFocused = true
            }
        }
    }
    
    private func authenticateWithBiometrics() {
        let context = LAContext()
        var error: NSError?
        
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            print("DEBUG: Biometric authentication not available: \(error?.localizedDescription ?? "Unknown error")")
            // Focus PIN field if biometrics not available
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isPinFieldFocused = true
            }
            return
        }
        
        let reason = "Use Face ID or Touch ID to unlock your account"
        context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, authenticationError in
            DispatchQueue.main.async {
                if success {
                    print("DEBUG: Biometric authentication successful")
                    self.securityViewModel.isAuthenticated = true
                    self.securityViewModel.isPromptingForPin = false
                    self.securityViewModel.requiresReauthentication = false
                } else {
                    print("DEBUG: Biometric authentication failed: \(authenticationError?.localizedDescription ?? "Unknown error")")
                    // Focus PIN field after biometric failure
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        self.isPinFieldFocused = true
                    }
                }
            }
        }
    }
    
    private func getBiometricIcon() -> String {
        let context = LAContext()
        switch context.biometryType {
        case .faceID:
            return "faceid"
        case .touchID:
            return "touchid"
        default:
            return "lock.shield"
        }
    }
    
    private func getBiometricText() -> String {
        let context = LAContext()
        switch context.biometryType {
        case .faceID:
            return "Use Face ID"
        case .touchID:
            return "Use Touch ID"
        default:
            return "Use Biometrics"
        }
    }
}

struct LockScreenView_Previews: PreviewProvider {
    static var previews: some View {
        LockScreenView()
            .environmentObject(SecurityViewModel())
    }
}
