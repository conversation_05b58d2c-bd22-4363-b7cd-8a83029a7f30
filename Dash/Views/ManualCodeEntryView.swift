import SwiftUI

struct ManualCodeEntryView: View {
    @Binding var splitCode: String
    @Environment(\.presentationMode) var presentationMode
    @ObservedObject private var splitViewModel = SplitViewModel.shared
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var showingSuccessAlert = false
    @State private var joinedSplit: Split?
    @State private var foundSplit: Split?
    @State private var showingJoinView = false
    @State private var showingSplitStatus = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: Spacing.lg) {
                // Header
                VStack(spacing: Spacing.sm) {
                    Image(systemName: "qrcode.viewfinder")
                        .font(.system(size: 48, weight: .medium))
                        .foregroundColor(Color.theme.primary)
                    
                    Text("Enter Dash Code")
                        .font(Font.theme.titleLarge)
                        .fontWeight(.bold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("Enter the split code to join manually")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, Spacing.xl)
                
                // Code Input Section
                VStack(spacing: Spacing.md) {
                    ModernCard(padding: Spacing.lg) {
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Split Code")
                                .font(Font.theme.labelMedium)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.textSecondary)
                            
                            TextField("Enter split code here", text: $splitCode)
                                .font(Font.theme.bodyLarge)
                                .textFieldStyle(PlainTextFieldStyle())
                                .autocapitalization(.allCharacters)
                                .disableAutocorrection(true)
                                .padding(.vertical, Spacing.sm)
                                .padding(.horizontal, Spacing.md)
                                .background(Color.theme.surfaceSecondary)
                                .cornerRadius(BorderRadius.md)
                        }
                    }
                    
                    if let errorMessage = errorMessage {
                        HStack(spacing: Spacing.sm) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.system(size: 14))
                                .foregroundColor(Color.theme.error)
                            
                            Text(errorMessage)
                                .font(Font.theme.labelMedium)
                                .foregroundColor(Color.theme.error)
                        }
                        .padding(.horizontal, Spacing.md)
                    }
                }
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: Spacing.md) {
                    ModernButton(
                        "Find Split",
                        icon: isLoading ? nil : "magnifyingglass",
                        style: .primary,
                        size: .large,
                        isLoading: isLoading
                    ) {
                        findSplit()
                    }
                    .disabled(splitCode.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isLoading)
                    
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.textSecondary)
                }
                .padding(.bottom, Spacing.xl)
            }
            .padding(.horizontal, Spacing.lg)
            .navigationTitle("")
            .navigationBarHidden(true)
            .fullScreenCover(isPresented: $showingJoinView) {
                if let split = foundSplit {
                    if showingSplitStatus {
                        SplitStatusView()
                    } else {
                        JoinSplitView(split: split, onJoinSuccess: {
                            print("ManualCodeEntryView: User successfully joined split, showing status")
                            showingSplitStatus = true
                        })
                    }
                }
            }
        }
    }
    
    private func findSplit() {
        let trimmedCode = splitCode.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedCode.isEmpty else { return }
        
        isLoading = true
        errorMessage = nil
        
        // First, try to fetch the split to see if it exists
        splitViewModel.fetchSplit(splitId: trimmedCode) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let split):
                    // Split exists, now show join view
                    self.foundSplit = split
                    self.showingJoinView = true
                    self.isLoading = false
                    
                case .failure(let error):
                    self.isLoading = false
                    self.errorMessage = "Split not found. Please check the code and try again."
                    print("Error fetching split: \(error.localizedDescription)")
                }
            }
        }
    }
}

struct ManualCodeEntryView_Previews: PreviewProvider {
    static var previews: some View {
        ManualCodeEntryView(splitCode: .constant(""))
    }
}
