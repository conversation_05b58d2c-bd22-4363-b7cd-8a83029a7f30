import SwiftUI

struct CustomTextFieldModifier: ViewModifier {
    @State private var isFocused = false
    let hasError: Bool

    init(hasError: Bool = false) {
        self.hasError = hasError
    }

    func body(content: Content) -> some View {
        content
            .font(Font.theme.bodyLarge)
            .foregroundColor(Color.theme.textPrimary)
            .padding(Spacing.lg)
            .background(Color.theme.surfaceSecondary)
            .cornerRadius(BorderRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: BorderRadius.md)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .onTapGesture {
                isFocused = true
            }
            .animation(Animations.fast, value: isFocused)
            .animation(Animations.fast, value: hasError)
    }

    private var borderColor: Color {
        if hasError {
            return Color.theme.error
        } else if isFocused {
            return Color.theme.primary
        } else {
            return Color.clear
        }
    }

    private var borderWidth: CGFloat {
        (isFocused || hasError) ? 2 : 0
    }
}