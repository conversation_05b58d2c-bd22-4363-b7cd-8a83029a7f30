import SwiftUI

struct JoinSplitView: View {
    let split: Split
    let onJoinSuccess: () -> Void
    @StateObject private var viewModel = SplitViewModel.shared
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var errorMessage: String?
    @State private var isShowingError = false
    @State private var updatedSplit: Split?
    @State private var customAmount: String = ""
    @State private var useCustomAmount = false

    private var suggestedAmount: Double {
        guard split.numberOfParticipants > 0 else { return 0 }
        return split.totalAmount / Double(split.numberOfParticipants)
    }
    
    private var amountToPay: Double {
        if useCustomAmount, let amount = Double(customAmount), amount > 0 {
            return amount
        }
        return suggestedAmount
    }
    
    private var totalAlreadyAssigned: Double {
        split.participants.reduce(0) { $0 + $1.share }
    }
    
    private var remainingAmount: Double {
        split.totalAmount - totalAlreadyAssigned
    }

    var body: some View {
        ZStack {
            Color.theme.background.ignoresSafeArea()

            VStack(spacing: 0) {
                // Modern Header
                ModernHeader(
                    title: "Join Split",
                    subtitle: "Choose your contribution",
                    onDismiss: { presentationMode.wrappedValue.dismiss() }
                )

                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.xxl) {
                        // Split Summary Card
                        ModernCard(padding: Spacing.lg) {
                            VStack(spacing: Spacing.lg) {
                                // Main split info
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("Total Amount")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)

                                        Text("$\(split.totalAmount, specifier: "%.2f")")
                                            .font(Font.theme.titleLarge)
                                            .fontWeight(.bold)
                                            .foregroundColor(Color.theme.textPrimary)
                                    }

                                    Spacer()

                                    VStack(alignment: .trailing, spacing: 4) {
                                        Text("Participants")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)

                                        Text("\(split.participants.count + 1)/\(split.numberOfParticipants)")
                                            .font(Font.theme.titleLarge)
                                            .fontWeight(.bold)
                                            .foregroundColor(Color.theme.primary)
                                    }
                                }

                                if remainingAmount != split.totalAmount {
                                    Divider()
                                        .background(Color.theme.textTertiary.opacity(0.2))

                                    VStack(spacing: Spacing.sm) {
                                        HStack {
                                            Text("Already Assigned")
                                                .font(Font.theme.bodyMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                            Spacer()
                                            Text("$\(totalAlreadyAssigned, specifier: "%.2f")")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.semibold)
                                                .foregroundColor(Color.theme.textPrimary)
                                        }

                                        HStack {
                                            Text("Remaining")
                                                .font(Font.theme.bodyMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                            Spacer()
                                            Text("$\(remainingAmount, specifier: "%.2f")")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.semibold)
                                                .foregroundColor(remainingAmount > 0 ? Color.theme.warning : Color.theme.error)
                                        }
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.top, Spacing.lg)


                        // Amount Selection
                        VStack(alignment: .leading, spacing: Spacing.md) {
                            Text("Choose Your Amount")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)

                            VStack(spacing: Spacing.md) {
                                // Suggested Amount Option
                                Button(action: {
                                    useCustomAmount = false
                                    customAmount = String(format: "%.2f", suggestedAmount)
                                }) {
                                    ModernCard(
                                        padding: Spacing.lg,
                                        cornerRadius: BorderRadius.md
                                    ) {
                                        HStack(spacing: Spacing.md) {
                                            // Selection indicator
                                            ZStack {
                                                Circle()
                                                    .stroke(
                                                        !useCustomAmount ? Color.theme.primary : Color.theme.textTertiary.opacity(0.3),
                                                        lineWidth: 2
                                                    )
                                                    .frame(width: 20, height: 20)

                                                if !useCustomAmount {
                                                    Circle()
                                                        .fill(Color.theme.primary)
                                                        .frame(width: 12, height: 12)
                                                }
                                            }

                                            VStack(alignment: .leading, spacing: 4) {
                                                Text("Equal Split")
                                                    .font(Font.theme.bodyLarge)
                                                    .fontWeight(.semibold)
                                                    .foregroundColor(Color.theme.textPrimary)

                                                Text("Split evenly among all participants")
                                                    .font(Font.theme.bodySmall)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }

                                            Spacer()

                                            Text("$\(suggestedAmount, specifier: "%.2f")")
                                                .font(Font.theme.titleMedium)
                                                .fontWeight(.bold)
                                                .foregroundColor(Color.theme.textPrimary)
                                        }
                                    }
                                    .overlay(
                                        RoundedRectangle(cornerRadius: BorderRadius.md)
                                            .stroke(
                                                !useCustomAmount ? Color.theme.primary : Color.clear,
                                                lineWidth: 2
                                            )
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())

                                // Custom Amount Option
                                Button(action: {
                                    useCustomAmount = true
                                }) {
                                    ModernCard(
                                        padding: Spacing.lg,
                                        cornerRadius: BorderRadius.md
                                    ) {
                                        HStack(spacing: Spacing.md) {
                                            // Selection indicator
                                            ZStack {
                                                Circle()
                                                    .stroke(
                                                        useCustomAmount ? Color.theme.primary : Color.theme.textTertiary.opacity(0.3),
                                                        lineWidth: 2
                                                    )
                                                    .frame(width: 20, height: 20)

                                                if useCustomAmount {
                                                    Circle()
                                                        .fill(Color.theme.primary)
                                                        .frame(width: 12, height: 12)
                                                }
                                            }

                                            VStack(alignment: .leading, spacing: 4) {
                                                Text("Custom Amount")
                                                    .font(Font.theme.bodyLarge)
                                                    .fontWeight(.semibold)
                                                    .foregroundColor(Color.theme.textPrimary)

                                                Text("Choose your own contribution")
                                                    .font(Font.theme.bodySmall)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }

                                            Spacer()

                                            if useCustomAmount {
                                                Text("$\(amountToPay, specifier: "%.2f")")
                                                    .font(Font.theme.titleMedium)
                                                    .fontWeight(.bold)
                                                    .foregroundColor(Color.theme.textPrimary)
                                            }
                                        }
                                    }
                                    .overlay(
                                        RoundedRectangle(cornerRadius: BorderRadius.md)
                                            .stroke(
                                                useCustomAmount ? Color.theme.primary : Color.clear,
                                                lineWidth: 2
                                            )
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())

                                if useCustomAmount {
                                    ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.md) {
                                        VStack(spacing: Spacing.md) {
                                            HStack(spacing: Spacing.sm) {
                                                Text("$")
                                                    .font(Font.theme.titleMedium)
                                                    .fontWeight(.semibold)
                                                    .foregroundColor(Color.theme.textPrimary)

                                                TextField("0.00", text: $customAmount)
                                                    .keyboardType(.decimalPad)
                                                    .font(Font.theme.titleMedium)
                                                    .fontWeight(.semibold)
                                                    .multilineTextAlignment(.leading)
                                                    .foregroundColor(Color.theme.textPrimary)
                                                    .textFieldStyle(PlainTextFieldStyle())
                                            }
                                            .padding(.horizontal, Spacing.md)
                                            .padding(.vertical, Spacing.sm)
                                            .background(Color.theme.surfaceSecondary)
                                            .cornerRadius(BorderRadius.sm)

                                            if let amount = Double(customAmount), amount > remainingAmount && remainingAmount > 0 {
                                                HStack(spacing: Spacing.xs) {
                                                    Image(systemName: "exclamationmark.triangle.fill")
                                                        .font(.system(size: 14, weight: .medium))
                                                        .foregroundColor(Color.theme.warning)

                                                    Text("This exceeds the remaining amount of $\(remainingAmount, specifier: "%.2f")")
                                                        .font(Font.theme.bodySmall)
                                                        .foregroundColor(Color.theme.warning)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, Spacing.lg)

                        // Bottom spacing
                        Color.clear.frame(height: 120)
                    }
                }

                // Bottom Action Area
                VStack(spacing: Spacing.md) {
                    // Amount summary
                    VStack(spacing: 4) {
                        Text("Your contribution")
                            .font(Font.theme.bodySmall)
                            .foregroundColor(Color.theme.textSecondary)

                        Text("$\(amountToPay, specifier: "%.2f")")
                            .font(Font.theme.titleLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)

                        Text("This will charge your Dash balance immediately")
                            .font(Font.theme.labelSmall)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal, Spacing.lg)

                    // Join Button
                    ModernButton(
                        "Join & Pay Split",
                        icon: "checkmark.circle.fill",
                        style: .primary,
                        size: .large,
                        isDisabled: amountToPay <= 0
                    ) {
                        joinSplit()
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.bottom, Spacing.lg)
                }
                .background(
                    Color.theme.background
                        .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
                )
            }
        }
        .alert(isPresented: $isShowingError) {
            Alert(title: Text("Error"), message: Text(errorMessage ?? "An unknown error occurred."), dismissButton: .default(Text("OK")))
        }
        .onAppear {
            print("JoinSplitView: onAppear called with split ID: \(split.id ?? "N/A")")
            print("JoinSplitView: Total amount: \(split.totalAmount), Participants: \(split.numberOfParticipants), Suggested amount: \(suggestedAmount)")
            customAmount = String(format: "%.2f", suggestedAmount)
            viewModel.split = split
        }
    }

    private func joinSplit() {
        guard let splitId = split.id else {
            errorMessage = "Split ID is missing."
            isShowingError = true
            return
        }

        print("JoinSplitView: Attempting to join split with ID: \(splitId)")
        viewModel.joinAndPayForSplit(splitId: splitId, amount: amountToPay) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let updatedSplit):
                    print("JoinSplitView: Successfully joined split. Participants: \(updatedSplit.participants.count)")
                    print("JoinSplitView: Calling onJoinSuccess callback")
                    // Call the success callback to show status view
                    self.onJoinSuccess()
                case .failure(let error):
                    print("JoinSplitView: Failed to join split: \(error.localizedDescription)")
                    self.errorMessage = error.localizedDescription
                    self.isShowingError = true
                }
            }
        }
    }
}
