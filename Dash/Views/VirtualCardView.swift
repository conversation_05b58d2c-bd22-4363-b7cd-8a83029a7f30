import SwiftUI

struct VirtualCardView: View {
    var balance: Double

    var body: some View {
        cardFront
            .frame(height: 220)
    }

    private var cardFront: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header
            HStack {
                HStack(spacing: Spacing.sm) {
                    // Dash Logo
                    Image("DashLogo")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 32, height: 32)
                        .foregroundColor(.black)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("DASH")
                            .font(Font.theme.titleMedium)
                            .fontWeight(.bold)
                            .foregroundColor(.black)

                        Text("CLASSIC")
                            .font(Font.theme.labelSmall)
                            .foregroundColor(.black.opacity(0.6))
                    }
                }

                Spacer()

                Image(systemName: "contactless.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.black)
            }
            .padding(.top, Spacing.xl)
            .padding(.horizontal, Spacing.xl)

            Spacer()

            // Balance Section
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text("Available Balance")
                    .font(Font.theme.labelMedium)
                    .foregroundColor(.black.opacity(0.6))

                Text(balance, format: .currency(code: "AUD"))
                    .font(Font.theme.titleLarge)
                    .fontWeight(.bold)
                    .foregroundColor(.black)
            }
            .padding(.horizontal, Spacing.xl)
            .padding(.bottom, Spacing.md)

            // Card Number
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text("**** **** **** 1234")
                    .font(Font.theme.monoLarge)
                    .fontWeight(.medium)
                    .foregroundColor(.black)
                    .tracking(2)

                HStack {
                    Text("VALID THRU")
                        .font(Font.theme.labelSmall)
                        .foregroundColor(.black.opacity(0.6))

                    Text("06/26")
                        .font(Font.theme.monoMedium)
                        .foregroundColor(.black)

                    Spacer()

                    MastercardLogoView()
                }
            }
            .padding(.horizontal, Spacing.xl)
            .padding(.bottom, Spacing.xl)
        }
        .background(Color.white)
        .cornerRadius(BorderRadius.xl)
        .overlay(
            RoundedRectangle(cornerRadius: BorderRadius.xl)
                .stroke(Color.black.opacity(0.1), lineWidth: 1)
        )
        .shadow(
            color: Color.black.opacity(0.15),
            radius: 25,
            x: 0,
            y: 15
        )
    }
}

// MARK: - Mastercard Logo Component
struct MastercardLogoView: View {
    var body: some View {
        HStack(spacing: -6) {
            // Red circle (left)
            Circle()
                .fill(Color(red: 0.92, green: 0.16, blue: 0.22)) // Mastercard red #EB001B
                .frame(width: 18, height: 18)

            // Orange circle (right, overlapping)
            Circle()
                .fill(Color(red: 1.0, green: 0.62, blue: 0.0)) // Mastercard orange #FF5F00
                .frame(width: 18, height: 18)
        }
        .frame(width: 30, height: 18)
    }
}

struct VirtualCardView_Previews: PreviewProvider {
    static var previews: some View {
        VirtualCardView(balance: 150.21)
    }
}
