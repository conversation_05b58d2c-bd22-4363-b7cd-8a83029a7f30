import SwiftUI

struct SubscriptionView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var subscriptionService = SubscriptionService.shared
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTier: SubscriptionTier = .classic
    @State private var showingUpgradeConfirmation = false
    
    private var currentUser: User? {
        authViewModel.currentUser
    }
    
    private var currentSubscription: Subscription? {
        currentUser?.subscription
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.surfaceSecondary.ignoresSafeArea(.all)
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.xl) {
                        // Current Subscription Status
                        if let subscription = currentSubscription {
                            currentSubscriptionCard(subscription)
                        }
                        
                        // Tier Selection
                        tierSelectionSection
                        
                        // Selected Tier Details
                        selectedTierDetailsCard
                        
                        // Upgrade/Downgrade Button
                        if selectedTier != currentUser?.currentTier {
                            upgradeButton
                        }
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.sm)
                }
            }
            .navigationTitle("Subscription")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .onAppear {
                selectedTier = currentUser?.currentTier ?? .classic
            }
        }
        .alert("Upgrade Subscription", isPresented: $showingUpgradeConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Confirm") {
                upgradeSubscription()
            }
        } message: {
            Text("Are you sure you want to upgrade to \(selectedTier.displayName) for $\(String(format: "%.2f", selectedTier.monthlyPrice))/month?")
        }
        .alert("Error", isPresented: .constant(subscriptionService.errorMessage != nil)) {
            Button("OK") {
                subscriptionService.clearMessages()
            }
        } message: {
            Text(subscriptionService.errorMessage ?? "")
        }
        .alert("Success", isPresented: .constant(subscriptionService.successMessage != nil)) {
            Button("OK") {
                subscriptionService.clearMessages()
                dismiss()
            }
        } message: {
            Text(subscriptionService.successMessage ?? "")
        }
    }
    
    private func currentSubscriptionCard(_ subscription: Subscription) -> some View {
        ModernCard(padding: Spacing.lg) {
            VStack(alignment: .leading, spacing: Spacing.md) {
                HStack {
                    Text("Current Plan")
                        .font(Font.theme.titleMedium)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Spacer()
                    
                    Text(subscription.status.displayName)
                        .font(Font.theme.labelMedium)
                        .fontWeight(.medium)
                        .foregroundColor(subscription.isActive ? Color.theme.success : Color.theme.warning)
                        .padding(.horizontal, Spacing.sm)
                        .padding(.vertical, Spacing.xs)
                        .background(
                            (subscription.isActive ? Color.theme.success : Color.theme.warning)
                                .opacity(0.1)
                        )
                        .cornerRadius(BorderRadius.sm)
                }
                
                HStack {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        Text(subscription.tier.displayName)
                            .font(Font.theme.headlineSmall)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        if subscription.tier != .classic {
                            Text("$\(String(format: "%.2f", subscription.priceAtPurchase))/month")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                        } else {
                            Text("Free")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.success)
                        }
                    }
                    
                    Spacer()
                    
                    if subscription.isActive && subscription.tier != .classic {
                        VStack(alignment: .trailing, spacing: Spacing.xs) {
                            Text("\(subscription.daysRemaining) days left")
                                .font(Font.theme.labelMedium)
                                .foregroundColor(Color.theme.textSecondary)
                            
                            Text("Auto-renew: \(subscription.autoRenew ? "On" : "Off")")
                                .font(Font.theme.labelSmall)
                                .foregroundColor(subscription.autoRenew ? Color.theme.success : Color.theme.warning)
                        }
                    }
                }
            }
        }
    }
    
    private var tierSelectionSection: some View {
        VStack(alignment: .leading, spacing: Spacing.md) {
            Text("Choose Your Plan")
                .font(Font.theme.titleMedium)
                .fontWeight(.semibold)
                .foregroundColor(Color.theme.textPrimary)
            
            VStack(spacing: Spacing.sm) {
                ForEach(SubscriptionTier.allCases, id: \.self) { tier in
                    tierSelectionCard(tier)
                }
            }
        }
    }
    
    private func tierSelectionCard(_ tier: SubscriptionTier) -> some View {
        Button(action: {
            selectedTier = tier
        }) {
            ModernCard(
                padding: Spacing.lg,
                shadow: selectedTier == tier ? Shadows.md : Shadows.sm
            ) {
                HStack(spacing: Spacing.md) {
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        Text(tier.displayName)
                            .font(Font.theme.titleMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        if tier == .classic {
                            Text("Free")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.success)
                        } else {
                            Text("$\(String(format: "%.2f", tier.monthlyPrice))/month")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                        }
                    }
                    
                    Spacer()
                    
                    ZStack {
                        Circle()
                            .stroke(
                                selectedTier == tier ? Color.theme.primary : Color.theme.neutral300,
                                lineWidth: 2
                            )
                            .frame(width: 24, height: 24)
                        
                        if selectedTier == tier {
                            Circle()
                                .fill(Color.theme.primary)
                                .frame(width: 12, height: 12)
                        }
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var selectedTierDetailsCard: some View {
        ModernCard(padding: Spacing.lg) {
            VStack(alignment: .leading, spacing: Spacing.md) {
                Text("\(selectedTier.displayName) Features")
                    .font(Font.theme.titleMedium)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.theme.textPrimary)
                
                VStack(alignment: .leading, spacing: Spacing.sm) {
                    ForEach(selectedTier.features, id: \.self) { feature in
                        HStack(spacing: Spacing.sm) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.theme.success)
                            
                            Text(feature)
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Spacer()
                        }
                    }
                }
            }
        }
    }
    
    private var upgradeButton: some View {
        ModernButton(
            selectedTier.monthlyPrice > (currentUser?.currentTier.monthlyPrice ?? 0) ? "Upgrade to \(selectedTier.displayName)" : "Switch to \(selectedTier.displayName)",
            icon: "arrow.up.circle.fill",
            style: .primary,
            size: .large,
            isLoading: subscriptionService.isLoading
        ) {
            if selectedTier.monthlyPrice > 0 {
                showingUpgradeConfirmation = true
            } else {
                upgradeSubscription()
            }
        }
    }
    
    private func upgradeSubscription() {
        guard let userId = currentUser?.uid else { return }

        Task {
            do {
                try await subscriptionService.upgradeSubscription(for: userId, to: selectedTier)

                // Refresh user data to reflect changes
                authViewModel.refreshCurrentUser()

                // Dismiss the view after successful upgrade
                dismiss()
            } catch {
                // Error is already handled by the subscription service
                print("Subscription upgrade failed: \(error)")
            }
        }
    }
}

struct SubscriptionView_Previews: PreviewProvider {
    static var previews: some View {
        SubscriptionView()
            .environmentObject(AuthViewModel.shared)
    }
}
