import SwiftUI

struct TransactionsListView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var transactionService = TransactionService.shared
    @Binding var searchText: String
    
    var filteredTransactions: [Transaction] {
        // Show all transactions (including pending split transactions) and apply search filter
        let allTransactions = transactionService.transactions
        
        if searchText.isEmpty {
            return allTransactions
        } else {
            return allTransactions.filter { 
                $0.name.lowercased().contains(searchText.lowercased()) 
            }
        }
    }
    
    var body: some View {
        Group {
            if transactionService.isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text("Loading transactions...")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
                .padding()
            } else if filteredTransactions.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "list.bullet.rectangle")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)
                    Text("No transactions yet")
                        .font(.headline)
                        .foregroundColor(.gray)
                    Text("Your recent activity will appear here")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, minHeight: 200)
                .padding()
            } else {
                ScrollView {
                    VStack(spacing: 0) {
                        Text("Showing \(filteredTransactions.count) transactions")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.bottom, 8)
                        
                        LazyVStack(spacing: 0) {
                            ForEach(filteredTransactions) { transaction in
                                TransactionRow(transaction: transaction)
                                    .padding(.horizontal)
                                    .padding(.vertical, 4)
                                
                                if transaction.id != filteredTransactions.last?.id {
                                    Divider()
                                        .padding(.horizontal)
                                }
                            }
                        }
                    }
                }
                .refreshable {
                    print("TransactionsListView: Pull to refresh")
                    transactionService.loadTransactions()
                }
            }
        }
        .onAppear {
            print("TransactionsListView: View appeared - loading transactions")
            transactionService.loadTransactions()
        }
        .onChange(of: authViewModel.currentUser?.uid) { userId in
            print("TransactionsListView: User changed to: \(userId ?? "nil")")
            if userId != nil {
                transactionService.loadTransactions()
            } else {
                transactionService.stopListening()
            }
        }
    }
}
