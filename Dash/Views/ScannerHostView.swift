import SwiftUI
import AVFoundation
import FirebaseAuth
import Firebase

struct ScannerHostView: View {
    @Environment(\.presentationMode) var presentationMode
    @State var scannedCode: String?
    @State private var errorMessage: String?
    @State private var isShowingScanner = true
    @State private var fetchedSplit: Split?
    @State private var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined

    var body: some View {
        ZStack {
            // Background
            Color.black.ignoresSafeArea()

            switch cameraPermissionStatus {
            case .authorized:
                if isShowingScanner {
                    VStack(spacing: 0) {
                        // Header
                        VStack(spacing: Spacing.md) {
                            HStack {
                                Button(action: {
                                    presentationMode.wrappedValue.dismiss()
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(Color.black.opacity(0.3))
                                            .frame(width: 44, height: 44)

                                        Image(systemName: "xmark")
                                            .font(.system(size: 18, weight: .semibold))
                                            .foregroundColor(.white)
                                    }
                                }

                                Spacer()
                            }
                            .padding(.horizontal, Spacing.lg)
                            .padding(.top, Spacing.md)

                            VStack(spacing: Spacing.sm) {
                                Text("Scan QR Code")
                                    .font(Font.theme.titleLarge)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)

                                Text("Point your camera at a QR code to join a split")
                                    .font(Font.theme.bodyMedium)
                                    .foregroundColor(.white.opacity(0.8))
                                    .multilineTextAlignment(.center)
                            }
                            .padding(.horizontal, Spacing.lg)
                        }
                        .padding(.bottom, Spacing.xl)

                        // Scanner
                        QRCodeScannerView(onCodeScanned: { code in
                            self.scannedCode = code
                            self.isShowingScanner = false
                            fetchAndJoinSplit(splitId: code)
                        })
                        .frame(maxWidth: .infinity, maxHeight: .infinity)

                        Spacer()
                    }
                } else {
                    processingView
                }
            case .denied, .restricted:
                permissionDeniedView
            case .notDetermined:
                VStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)

                    Text("Requesting camera access...")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.top, Spacing.md)
                }
                .onAppear(perform: requestCameraPermission)
            @unknown default:
                VStack {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 48, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))

                    Text("An unexpected error occurred")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.top, Spacing.md)
                }
            }
        }
        .sheet(item: $fetchedSplit) { split in
            AwaitingUsersView(split: split)
        }
        .onAppear {
            if let code = scannedCode, !code.isEmpty {
                // If a code is passed in, skip scanning and go straight to processing
                isShowingScanner = false
                fetchAndJoinSplit(splitId: code)
            } else {
                checkCameraPermission()
            }
        }
    }
    
    private var processingView: some View {
        VStack(spacing: Spacing.lg) {
            // Exit button
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    ZStack {
                        Circle()
                            .fill(Color.black.opacity(0.3))
                            .frame(width: 44, height: 44)

                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }

                Spacer()
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, Spacing.md)

            Spacer()

            if errorMessage == nil {
                VStack(spacing: Spacing.lg) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)

                    Text("Processing Split ID...")
                        .font(Font.theme.titleMedium)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text("Please wait while we fetch the split details")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                }
            } else {
                VStack(spacing: Spacing.lg) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 48, weight: .medium))
                        .foregroundColor(.red)

                    Text("Error")
                        .font(Font.theme.titleMedium)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text(errorMessage ?? "An unknown error occurred.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, Spacing.lg)

                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("Close")
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, Spacing.xl)
                            .padding(.vertical, Spacing.md)
                            .background(Color.red)
                            .cornerRadius(BorderRadius.lg)
                    }
                }
            }

            Spacer()
        }
    }
    
    private var permissionDeniedView: some View {
        VStack(spacing: Spacing.lg) {
            // Exit button
            HStack {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    ZStack {
                        Circle()
                            .fill(Color.black.opacity(0.3))
                            .frame(width: 44, height: 44)

                        Image(systemName: "xmark")
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)
                    }
                }

                Spacer()
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.top, Spacing.md)

            Spacer()

            VStack(spacing: Spacing.lg) {
                Image(systemName: "camera.fill")
                    .font(.system(size: 64, weight: .medium))
                    .foregroundColor(.white.opacity(0.6))

                VStack(spacing: Spacing.sm) {
                    Text("Camera Access Required")
                        .font(Font.theme.titleLarge)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Text("Please enable camera access in Settings to scan QR codes and join splits.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, Spacing.lg)
                }

                VStack(spacing: Spacing.md) {
                    Button(action: {
                        if let url = URL(string: UIApplication.openSettingsURLString) {
                            UIApplication.shared.open(url)
                        }
                    }) {
                        Text("Open Settings")
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, Spacing.md)
                            .background(Color.theme.success)
                            .cornerRadius(BorderRadius.lg)
                    }

                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("Cancel")
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.medium)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                .padding(.horizontal, Spacing.lg)
            }

            Spacer()
        }
    }

    private func checkCameraPermission() {
        cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
    }
    
    private func requestCameraPermission() {
        AVCaptureDevice.requestAccess(for: .video) { granted in
            DispatchQueue.main.async {
                self.cameraPermissionStatus = granted ? .authorized : .denied
            }
        }
    }

    private func fetchAndJoinSplit(splitId: String) {
        let actualSplitId: String
        if let url = URL(string: splitId) {
            actualSplitId = url.lastPathComponent
        } else {
            actualSplitId = splitId
        }
        
        guard let userId = Auth.auth().currentUser?.uid, !userId.isEmpty else {
            errorMessage = "You must be logged in to join a split."
            return
        }

        let db = Firestore.firestore()
        db.collection("splits").document(actualSplitId).getDocument { (document, error) in
            if let document = document, document.exists {
                do {
                    var split = try document.data(as: Split.self)
                    
                    // Check if user is already a participant
                    if !split.participants.contains(where: { $0.id == userId }) {
                        db.collection("users").document(userId).getDocument { (userDoc, err) in
                            if let userDoc = userDoc, userDoc.exists {
                                do {
                                    if let data = userDoc.data() {
                                        let displayName = data["displayName"] as? String ?? "Unknown"
                                        let newParticipant = Participant(id: userId, name: displayName, share: 0)
                                        split.participants.append(newParticipant)
                                        
                                        // Update the split in Firestore
                                        do {
                                            try db.collection("splits").document(actualSplitId).setData(from: split) { err in
                                                if let err = err {
                                                    self.errorMessage = "Error joining split: \(err.localizedDescription)"
                                                } else {
                                                    self.fetchedSplit = split
                                                }
                                            }
                                        } catch {
                                            self.errorMessage = "Failed to update split."
                                        }
                                    } else {
                                        self.errorMessage = "Failed to decode user data."
                                    }
                                }
                            } else {
                                self.errorMessage = "User not found."
                            }
                        }
                    } else {
                        self.fetchedSplit = split
                    }
                } catch {
                    self.errorMessage = "Failed to read split data."
                }
            } else {
                self.errorMessage = "Split not found."
            }
        }
    }
}