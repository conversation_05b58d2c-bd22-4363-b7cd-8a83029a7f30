import SwiftUI

struct AddPodTransactionView: View {
    let pod: Pod
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var transactionType: PodTransactionType = .expense
    @State private var amount = ""
    @State private var description = ""
    @State private var category = "General"
    @State private var selectedDate = Date()
    @State private var paidBy: String = ""
    @State private var splitAmong: Set<String> = []
    @State private var notes = ""
    @State private var isCreating = false
    
    private let categories = ["Food", "Transportation", "Accommodation", "Entertainment", "Shopping", "Utilities", "Bills", "General"]
    
    private var isFormValid: Bool {
        !amount.isEmpty &&
        !description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !paidBy.isEmpty &&
        !splitAmong.isEmpty &&
        Double(amount) != nil &&
        Double(amount)! > 0
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.lg) {
                        // Header
                        VStack(spacing: Spacing.md) {
                            ZStack {
                                Circle()
                                    .fill(Color.theme.warning.opacity(0.1))
                                    .frame(width: 60, height: 60)
                                
                                Image(systemName: "plus.circle.fill")
                                    .font(.system(size: 24, weight: .medium))
                                    .foregroundColor(Color.theme.warning)
                            }
                            
                            Text("Add \(transactionType.displayName)")
                                .font(Font.theme.titleLarge)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                        }
                        .padding(.top, Spacing.lg)
                        
                        // Form
                        VStack(spacing: Spacing.lg) {
                            // Transaction Type
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Transaction Type")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)

                                ModernCard(padding: Spacing.lg) {
                                    Picker("Transaction Type", selection: $transactionType) {
                                        ForEach(PodTransactionType.allCases, id: \.self) { type in
                                            Text(type.displayName).tag(type)
                                        }
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                }
                            }

                            // Amount
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Amount")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)

                                ModernTextField("0.00", text: $amount, placeholder: "Enter amount")
                                    .keyboardType(.decimalPad)
                            }
                            
                            // Description
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Description")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernTextField("What was this for?", text: $description, placeholder: "e.g., Dinner at restaurant")
                            }
                            
                            // Category
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Category")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    Picker("Category", selection: $category) {
                                        ForEach(categories, id: \.self) { category in
                                            Text(category).tag(category)
                                        }
                                    }
                                    .pickerStyle(MenuPickerStyle())
                                }
                            }
                            
                            // Date
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Date")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    DatePicker("Transaction Date", selection: $selectedDate, displayedComponents: .date)
                                        .datePickerStyle(CompactDatePickerStyle())
                                }
                            }
                            
                            // Paid By
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Paid By")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    Picker("Paid By", selection: $paidBy) {
                                        ForEach(pod.members.filter { $0.isActive }) { member in
                                            Text(member.displayName).tag(member.userId)
                                        }
                                    }
                                    .pickerStyle(MenuPickerStyle())
                                }
                            }
                            
                            // Split Among
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Split Among")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    VStack(spacing: Spacing.sm) {
                                        ForEach(pod.members.filter { $0.isActive }) { member in
                                            HStack {
                                                Button(action: {
                                                    if splitAmong.contains(member.userId) {
                                                        splitAmong.remove(member.userId)
                                                    } else {
                                                        splitAmong.insert(member.userId)
                                                    }
                                                }) {
                                                    HStack {
                                                        Image(systemName: splitAmong.contains(member.userId) ? "checkmark.square.fill" : "square")
                                                            .foregroundColor(splitAmong.contains(member.userId) ? Color.theme.primary : Color.theme.textTertiary)
                                                        
                                                        Text(member.displayName)
                                                            .font(Font.theme.bodyMedium)
                                                            .foregroundColor(Color.theme.textPrimary)
                                                        
                                                        Spacer()
                                                    }
                                                }
                                                .buttonStyle(PlainButtonStyle())
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // Notes (Optional)
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Notes (Optional)")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernTextField("Additional notes...", text: $notes, placeholder: "Any additional details")
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                        
                        // Add Button
                        ModernButton(
                            "Add Expense",
                            icon: "plus",
                            isLoading: isCreating,
                            isDisabled: !isFormValid
                        ) {
                            addTransaction()
                        }
                        .padding(.horizontal, Spacing.lg)
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                }
            }
            .navigationTitle("Add \(transactionType.displayName)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .onAppear {
                // Set default values
                if let currentUserId = authViewModel.currentUser?.uid {
                    paidBy = currentUserId
                    // By default, split among all active members (most common use case)
                    splitAmong = Set(pod.members.filter { $0.isActive }.map { $0.userId })
                    print("🔵 TRANSACTION_DEBUG: Default split among all members: \(splitAmong)")
                }
            }
            .alert("Success", isPresented: .constant(podViewModel.successMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                    dismiss()
                }
            } message: {
                Text(podViewModel.successMessage ?? "")
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
        }
    }
    
    private func addTransaction() {
        guard let podId = pod.id,
              let currentUser = authViewModel.currentUser,
              let currentUserId = currentUser.uid,
              let currentUserName = currentUser.displayName,
              let amountValue = Double(amount) else {
            return
        }
        
        // Find the name of the person who paid
        let paidByMember = pod.members.first { $0.userId == paidBy }
        let paidByName = paidByMember?.displayName ?? "Unknown"
        
        isCreating = true
        
        let transaction = PodTransaction(
            podId: podId,
            createdBy: currentUserId,
            createdByName: currentUserName,
            type: transactionType,
            amount: amountValue,
            description: description.trimmingCharacters(in: .whitespacesAndNewlines),
            category: category,
            date: selectedDate,
            splitAmong: Array(splitAmong),
            paidBy: paidBy,
            paidByName: paidByName,
            notes: notes.isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines)
        )

        print("🔵 TRANSACTION_DEBUG: Creating transaction:")
        print("🔵 TRANSACTION_DEBUG: Pod ID: \(podId)")
        print("🔵 TRANSACTION_DEBUG: Type: \(transactionType)")
        print("🔵 TRANSACTION_DEBUG: Amount: \(amountValue)")
        print("🔵 TRANSACTION_DEBUG: Paid by: \(paidBy) (\(paidByName))")
        print("🔵 TRANSACTION_DEBUG: Split among: \(Array(splitAmong))")
        print("🔵 TRANSACTION_DEBUG: Description: \(description)")
        
        Task {
            let success = await podViewModel.addTransaction(transaction)
            
            await MainActor.run {
                isCreating = false
                if success {
                    // Refresh pod data to show updated balances
                    if let userId = authViewModel.currentUser?.uid {
                        podViewModel.fetchUserPods(userId: userId)
                    }
                    dismiss()
                } else {
                    // Error is handled by the alert
                }
            }
        }
    }
}

#Preview {
    AddPodTransactionView(pod: Pod(name: "Test Pod", createdBy: "user1", createdByName: "John Doe"))
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
