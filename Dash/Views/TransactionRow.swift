import SwiftUI

struct TransactionRow: View {
    let transaction: Transaction
    @State private var showingDetail = false
    
    private var displayName: String {
        if transaction.type == .transfer {
            // For transfers, show the other party's name based on transaction direction
            if transaction.name == "Money Sent" {
                return transaction.recipientName ?? "Unknown Recipient"
            } else if transaction.name == "Money Received" {
                return transaction.senderName ?? "Unknown Sender"
            }
        }
        return transaction.name
    }
    
    private var displayDetail: String {
        if transaction.type == .transfer {
            // Use the transaction name to determine the detail
            if transaction.name == "Money Sent" {
                return "Money Sent"
            } else if transaction.name == "Money Received" {
                return "Money Received"
            }
        }
        return transaction.detail
    }
    
    private var iconName: String {
        switch transaction.type {
        case .income:
            return "arrow.down.circle.fill"
        case .expense:
            return "arrow.up.circle.fill"
        case .transfer:
            if transaction.name == "Money Sent" {
                return "arrow.up.right.circle.fill"
            } else {
                return "arrow.down.left.circle.fill"
            }
        }
    }
    
    private var iconColor: Color {
        switch transaction.type {
        case .income:
            return .green
        case .expense:
            return .red
        case .transfer:
            if transaction.name == "Money Sent" {
                return .green
            } else {
                return .green
            }
        }
    }

    private var formattedAmount: Double {
        if transaction.type == .transfer && transaction.name == "Money Sent" {
            return -transaction.amount // Show negative for sent money
        }
        return transaction.amount // Show positive for received money and other types
    }

    var body: some View {
        Button(action: {
            showingDetail = true
        }) {
            HStack(spacing: 16) {
                // Modern icon with gradient background
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 48, height: 48)
                    
                    Image(systemName: iconName)
                        .font(.title2)
                        .foregroundColor(iconColor)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(displayName)
                            .font(.system(.body, design: .rounded, weight: .medium))
                            .foregroundColor(.primary)
                        
                        if transaction.status == .pending {
                            Text("PENDING")
                                .font(.caption2)
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 3)
                                .background(Color.orange)
                                .cornerRadius(8)
                        }
                    }
                    
                    HStack(spacing: 4) {
                        Text(displayDetail)
                            .font(.system(.caption, design: .rounded))
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                        
                        if transaction.status == .pending {
                            Text("• Balance not affected")
                                .font(.caption2)
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(formattedAmount, format: .currency(code: "AUD"))
                        .font(.system(.body, design: .rounded, weight: .semibold))
                        .foregroundColor({
                            if transaction.type == .income {
                                return .green
                            } else if transaction.type == .transfer {
                                // For transfers, check the transaction name to determine direction
                                if transaction.name == "Money Sent" {
                                    return .primary // Money sent - white
                                } else if transaction.name == "Money Received" {
                                    return .green // Money received - green
                                } else {
                                    return .primary // Default - white
                                }
                            } else {
                                return .primary // Default - white
                            }
                        }())
                    
                    Text(transaction.date.dateValue(), style: .time)
                        .font(.system(.caption2, design: .rounded))
                        .foregroundColor(.secondary)
                }
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary.opacity(0.6))
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            .opacity(transaction.status == .pending ? 0.8 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDetail) {
            TransactionDetailView(transaction: transaction)
        }
    }
}
