import SwiftUI

struct ForgotPasswordView: View {
    @State private var email = ""
    @State private var showAlert = false
    @EnvironmentObject var viewModel: AuthViewModel
    @Environment(\.dismiss) var dismiss

    var body: some View {
        ZStack {
            Color.theme.background.edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 20) {
                Spacer()
                
                Text("Reset Password")
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(Color.theme.accent)
                    .padding(.bottom, 30)

                Text("Enter the email address associated with your account and we'll send you a link to reset your password.")
                    .font(.subheadline)
                    .foregroundColor(Color.theme.secondaryText)
                    .padding(.horizontal, 32)
                    .multilineTextAlignment(.center)

                VStack(spacing: 15) {
                    TextField("Email", text: $email)
                        .modifier(CustomTextFieldModifier())
                        .keyboardType(.emailAddress)
                        .autocapitalization(.none)
                }
                .padding(.horizontal, 32)

                <PERSON><PERSON>(action: {
                    if !email.isEmpty {
                        viewModel.resetPassword(withEmail: email)
                    } else {
                        viewModel.errorMessage = "Please enter your email address."
                    }
                }) {
                    Text("Send Reset Link")
                        .font(.headline)
                        .foregroundColor(Color.theme.background)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.theme.accent)
                        .cornerRadius(16)
                }
                .padding(.horizontal, 32)
                
                Spacer()
                Spacer()
                
                Button {
                    dismiss()
                } label: {
                    Text("Back to Sign In").bold()
                }
                .font(.subheadline)
                .foregroundColor(Color.theme.accent)
                .padding(.bottom, 20)
            }
            .onChange(of: viewModel.successMessage) { _, newValue in
                if newValue != nil {
                    showAlert = true
                }
            }
            .onChange(of: viewModel.errorMessage) { _, newValue in
                if newValue != nil {
                    showAlert = true
                }
            }
            .alert(isPresented: $showAlert) {
                let title = viewModel.successMessage != nil ? "Success" : "Error"
                let message = viewModel.successMessage ?? viewModel.errorMessage ?? "An unknown error occurred."
                
                return Alert(
                    title: Text(title),
                    message: Text(message),
                    dismissButton: .default(Text("OK")) {
                        if viewModel.successMessage != nil {
                            viewModel.successMessage = nil
                            dismiss()
                        } else {
                            viewModel.errorMessage = nil
                        }
                    }
                )
            }
        }
    }
}

struct ForgotPasswordView_Previews: PreviewProvider {
    static var previews: some View {
        ForgotPasswordView()
            .environmentObject(AuthViewModel.shared)
    }
} 