import SwiftUI

struct EmailVerificationView: View {
    @EnvironmentObject var viewModel: AuthViewModel
    @Environment(\.dismiss) var dismiss
    @State private var isResending = false
    @State private var showErrorAlert = false
    @State private var showSuccessAlert = false
    
    var body: some View {
        ZStack {
            // Modern gradient background
            LinearGradient(
                colors: [Color.theme.surfaceSecondary, Color.theme.surface],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea(.all)
            
            ScrollView {
                VStack(spacing: Spacing.xl) {
                    Spacer()
                    
                    // Email verification icon
                    VStack(spacing: Spacing.lg) {
                        Image(systemName: "envelope.badge.shield.half.filled")
                            .font(.system(size: 80))
                            .foregroundColor(Color.theme.success)
                            .symbolRenderingMode(.hierarchical)
                        
                        Text("Verify Your Email")
                            .font(Font.theme.headlineLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                            .multilineTextAlignment(.center)
                    }
                    
                    // Instructions
                    VStack(spacing: Spacing.md) {
                        Text("We've sent a verification email to:")
                            .font(Font.theme.bodyLarge)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                        
                        Text(viewModel.userSession?.email ?? "your email")
                            .font(Font.theme.titleMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.textPrimary)
                            .multilineTextAlignment(.center)
                        
                        Text("Please check your inbox and click the verification link to continue.")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }
                    
                    // Action buttons
                    VStack(spacing: Spacing.md) {
                        // Check verification status button
                        ModernButton(
                            "I've Verified My Email",
                            icon: "checkmark.circle",
                            style: .success,
                            size: .medium,
                            isLoading: viewModel.isLoading
                        ) {
                            viewModel.checkEmailVerificationStatus()
                        }
                        
                        // Resend verification email button
                        ModernButton(
                            "Resend Verification Email",
                            icon: "arrow.clockwise",
                            style: .secondary,
                            size: .medium,
                            isLoading: isResending
                        ) {
                            resendVerificationEmail()
                        }
                        
                        // Sign out button
                        Button(action: {
                            viewModel.signOut()
                        }) {
                            Text("Sign Out")
                                .font(Font.theme.bodyMedium)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.textSecondary)
                        }
                        .padding(.top, Spacing.md)
                    }
                    .padding(.horizontal, Spacing.xl)
                    
                    // Error/Success messages
                    if let errorMessage = viewModel.errorMessage {
                        Text(errorMessage)
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.error)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }
                    
                    if let successMessage = viewModel.successMessage {
                        Text(successMessage)
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.success)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }
                    
                    Spacer()
                    
                    // Help text
                    VStack(spacing: Spacing.sm) {
                        Text("Didn't receive the email?")
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                        
                        Text("Check your spam folder or try resending the verification email.")
                            .font(Font.theme.labelSmall)
                            .foregroundColor(Color.theme.textTertiary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal, Spacing.xl)
                }
                .padding(.vertical, Spacing.xl)
            }
        }
        .onChange(of: viewModel.errorMessage) { _, newValue in
            if newValue != nil {
                showErrorAlert = true
            }
        }
        .onChange(of: viewModel.successMessage) { _, newValue in
            if newValue != nil {
                showSuccessAlert = true
            }
        }
        .alert("Error", isPresented: $showErrorAlert) {
            Button("OK") {
                viewModel.errorMessage = nil
            }
        } message: {
            Text(viewModel.errorMessage ?? "An unknown error occurred.")
        }
        .alert("Success", isPresented: $showSuccessAlert) {
            Button("OK") {
                viewModel.successMessage = nil
            }
        } message: {
            Text(viewModel.successMessage ?? "")
        }
    }
    
    private func resendVerificationEmail() {
        isResending = true
        viewModel.sendEmailVerification { success in
            DispatchQueue.main.async {
                isResending = false
                if !success {
                    // Error message is already set in the viewModel
                    showErrorAlert = true
                } else {
                    showSuccessAlert = true
                }
            }
        }
    }
}

struct EmailVerificationView_Previews: PreviewProvider {
    static var previews: some View {
        EmailVerificationView()
            .environmentObject(AuthViewModel.shared)
    }
}
