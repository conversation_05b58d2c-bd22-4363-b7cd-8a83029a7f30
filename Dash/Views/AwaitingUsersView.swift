import SwiftUI

struct AwaitingUsersView: View {
    @StateObject private var viewModel = SplitViewModel.shared
    @State var split: Split
    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        VStack {
            Text("Waiting for others to join...")
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding()

            Text("\(split.participants.count) of \(split.numberOfParticipants) have joined")
                .font(.headline)
                .padding()

            List(split.participants, id: \.id) { participant in
                Text(participant.name)
            }

            if split.participants.count == split.numberOfParticipants {
                Text("All participants have joined!")
                    .font(.headline)
                    .foregroundColor(.green)
            }
            
            Spacer()

            Button("Cancel Split") {
                viewModel.reset()
                presentationMode.wrappedValue.dismiss()
            }
            .padding()
            .foregroundColor(.red)
        }
        .onAppear {
            if let splitId = split.id {
                viewModel.listenToSplit(splitId: splitId)
            }
        }
        .onReceive(viewModel.$split) { updatedSplit in
            if let updatedSplit = updatedSplit {
                self.split = updatedSplit
            }
        }
        .navigationBarHidden(true)
    }
}
