import SwiftUI

struct SplitSummaryView: View {
    @ObservedObject private var viewModel = SplitViewModel.shared
    let splitId: String
    @EnvironmentObject var authViewModel: AuthViewModel

    var body: some View {
        VStack {
            if let split = viewModel.split {
                if split.creatorId == authViewModel.currentUser?.uid {
                    // Creator's view
                    Text("Adjust the split")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding()
                    
                    List($viewModel.participantsForEditing) { $participant in
                        HStack {
                            Text(participant.name)
                            Spacer()
                            TextField("Amount", value: $participant.share, format: .currency(code: "AUD"))
                                .keyboardType(.decimalPad)
                                .multilineTextAlignment(.trailing)
                        }
                    }
                    
                    Button(action: {
                        viewModel.finalizeSplit(splitId: splitId, participants: viewModel.participantsForEditing)
                    }) {
                        Text("Finalize Split")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .padding()
                    
                } else {
                    // Participant's view
                    Text("Split Summary")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding()
                    
                    List(split.participants) { participant in
                        HStack {
                            Text(participant.name)
                            Spacer()
                            Text(participant.share, format: .currency(code: "AUD"))
                        }
                    }
                }
            } else {
                ProgressView()
            }
        }
        .onAppear {
            viewModel.listenToSplit(splitId: splitId)
        }
    }
}