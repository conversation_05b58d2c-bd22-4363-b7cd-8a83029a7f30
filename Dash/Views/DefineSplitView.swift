import SwiftUI

struct DefineSplitView: View {
    let totalAmount: Double
    let numberOfParticipants: Int
    @State private var participants: [Participant] = []
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var splitViewModel = SplitViewModel.shared
    @State private var showSplitDetail = false
    @State private var qrCodeImage: UIImage?
    private let qrCodeService = QRCodeService()
    
    private var suggestedAmount: Double {
        totalAmount / Double(numberOfParticipants)
    }
    
    private var totalAssigned: Double {
        participants.reduce(0) { $0 + $1.share }
    }
    
    private var remainingAmount: Double {
        totalAmount - totalAssigned
    }
    
    private var isValidSplit: Bool {
        abs(remainingAmount) < 0.01 && participants.count >= 1
    }

    var body: some View {
        ZStack {
            Color.theme.background.ignoresSafeArea()

            VStack(spacing: 0) {
                // Modern Header
                ModernHeader(
                    title: "Split Details",
                    onDismiss: { presentationMode.wrappedValue.dismiss() }
                )

                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.xxl) {
                        // Amount Summary Card
                        ModernCard(padding: Spacing.lg) {
                            VStack(spacing: Spacing.md) {
                                // Total amount
                                VStack(spacing: 4) {
                                    Text("Total Amount")
                                        .font(Font.theme.bodySmall)
                                        .foregroundColor(Color.theme.textSecondary)

                                    Text("$\(totalAmount, specifier: "%.2f")")
                                        .font(Font.theme.titleLarge)
                                        .fontWeight(.bold)
                                        .foregroundColor(Color.theme.textPrimary)
                                }

                                Divider()
                                    .background(Color.theme.textTertiary.opacity(0.2))

                                // Split details
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("Participants")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)

                                        Text("\(numberOfParticipants) people")
                                            .font(Font.theme.bodyLarge)
                                            .fontWeight(.semibold)
                                            .foregroundColor(Color.theme.textPrimary)
                                    }

                                    Spacer()

                                    VStack(alignment: .trailing, spacing: 4) {
                                        Text("Suggested per person")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)

                                        Text("$\(suggestedAmount, specifier: "%.2f")")
                                            .font(Font.theme.bodyLarge)
                                            .fontWeight(.semibold)
                                            .foregroundColor(Color.theme.primary)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.top, Spacing.lg)

                        // Participants Section
                        VStack(alignment: .leading, spacing: Spacing.md) {
                            HStack {
                                Text("Participants")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)

                                Spacer()

                                if let split = splitViewModel.split {
                                    Text("\(split.participants.count) joined")
                                        .font(Font.theme.bodySmall)
                                        .foregroundColor(Color.theme.textSecondary)
                                }
                            }

                            if let split = splitViewModel.split {
                                VStack(spacing: Spacing.sm) {
                                    ForEach(split.participants.indices, id: \.self) { index in
                                        ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                                            HStack(spacing: Spacing.md) {
                                                // Avatar placeholder
                                                ZStack {
                                                    Circle()
                                                        .fill(Color.theme.primary.opacity(0.1))
                                                        .frame(width: 40, height: 40)

                                                    Text(String(split.participants[index].name.prefix(1).uppercased()))
                                                        .font(Font.theme.bodyMedium)
                                                        .fontWeight(.semibold)
                                                        .foregroundColor(Color.theme.primary)
                                                }

                                                VStack(alignment: .leading, spacing: 2) {
                                                    Text(split.participants[index].name)
                                                        .font(Font.theme.bodyLarge)
                                                        .fontWeight(.medium)
                                                        .foregroundColor(Color.theme.textPrimary)

                                                    HStack(spacing: Spacing.xs) {
                                                        Circle()
                                                            .fill(split.participants[index].hasPaid ? Color.theme.success : Color.theme.warning)
                                                            .frame(width: 6, height: 6)

                                                        Text(split.participants[index].hasPaid ? "Paid" : "Pending")
                                                            .font(Font.theme.labelSmall)
                                                            .foregroundColor(split.participants[index].hasPaid ? Color.theme.success : Color.theme.warning)
                                                    }
                                                }

                                                Spacer()

                                                Text("$\(split.participants[index].share, specifier: "%.2f")")
                                                    .font(Font.theme.bodyLarge)
                                                    .fontWeight(.semibold)
                                                    .foregroundColor(Color.theme.textPrimary)
                                            }
                                        }
                                    }
                                }
                            } else if participants.count > 0 {
                                // Show local participant before split is created
                                ParticipantRowView(
                                    participant: $participants[0],
                                    suggestedAmount: suggestedAmount,
                                    onRemove: {}
                                )
                            }
                        }
                        .padding(.horizontal, Spacing.lg)

                        // QR Code Section (show after split is created)
                        if let split = splitViewModel.split {
                            VStack(alignment: .leading, spacing: Spacing.md) {
                                Text("Share with Others")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)

                                ModernCard(padding: Spacing.lg) {
                                    VStack(spacing: Spacing.lg) {
                                        Text("Others can scan this QR code to join the split")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                            .multilineTextAlignment(.center)

                                        if let image = qrCodeImage {
                                            Image(uiImage: image)
                                                .resizable()
                                                .interpolation(.none)
                                                .scaledToFit()
                                                .frame(width: 180, height: 180)
                                                .background(Color.theme.surface)
                                                .cornerRadius(BorderRadius.lg)
                                                .shadow(color: Color.theme.textTertiary.opacity(0.1), radius: 4, x: 0, y: 2)
                                        } else {
                                            VStack(spacing: Spacing.md) {
                                                ProgressView()
                                                    .scaleEffect(1.2)

                                                Text("Generating QR Code...")
                                                    .font(Font.theme.bodyMedium)
                                                    .foregroundColor(Color.theme.textSecondary)

                                                if let splitId = split.id {
                                                    Text("Split ID: \(splitId)")
                                                        .font(Font.theme.bodySmall)
                                                        .foregroundColor(Color.theme.textTertiary)
                                                        .padding(.horizontal, Spacing.sm)
                                                        .padding(.vertical, Spacing.xs)
                                                        .background(Color.theme.surfaceSecondary)
                                                        .cornerRadius(BorderRadius.sm)
                                                }
                                            }
                                            .frame(width: 180, height: 180)
                                            .background(Color.theme.surfaceSecondary)
                                            .cornerRadius(BorderRadius.lg)
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)
                        }

                        // Bottom spacing
                        Color.clear.frame(height: 120)
                    }
                }

                // Bottom Action Area
                VStack(spacing: Spacing.md) {
                    if splitViewModel.split == nil {
                        // Create Split Button
                        ModernButton(
                            "Create Split",
                            icon: "plus.circle.fill",
                            style: .primary,
                            size: .large
                        ) {
                            createSplit()
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.bottom, Spacing.lg)
                    } else {
                        // Split Created - Show options
                        ModernButton(
                            "View Split Details",
                            icon: "list.bullet.circle.fill",
                            style: .primary,
                            size: .large
                        ) {
                            showSplitDetail = true
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.bottom, Spacing.lg)
                    }
                }
                .background(
                    Color.theme.background
                        .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
                )
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            setupInitialParticipant()
        }
        .alert(isPresented: $showingAlert) {
            Alert(title: Text("Invalid Split"), message: Text(alertMessage), dismissButton: .default(Text("OK")))
        }
        .fullScreenCover(isPresented: $showSplitDetail, onDismiss: {
            // Check if split was abandoned when user dismisses the detail view
            splitViewModel.cancelCurrentSplitIfAbandoned()
            splitViewModel.reset()
            presentationMode.wrappedValue.dismiss()
        }) {
            SplitDetailView()
        }
        .onChange(of: splitViewModel.split) { newSplit in
            if let splitId = newSplit?.id {
                generateQR(splitId: splitId)
            }
            // Don't automatically show split detail - let user stay on this screen with QR code
        }
        .onDisappear {
            // If user dismisses DefineSplitView without finalizing, cancel any created split
            if !showSplitDetail {
                splitViewModel.cancelCurrentSplitIfAbandoned()
            }
        }
    }
    
    private func setupInitialParticipant() {
        guard let creatorId = authViewModel.currentUser?.uid,
              let creatorName = authViewModel.currentUser?.displayName else { return }
        
        let creatorParticipant = Participant(
            id: creatorId,
            name: "\(creatorName) (You)",
            share: suggestedAmount,
            hasPaid: false
        )
        participants = [creatorParticipant]
    }
    
    private func addParticipant() {
        let newParticipant = Participant(
            id: "",
            name: "Participant \(participants.count + 1)",
            share: max(0, remainingAmount > suggestedAmount ? suggestedAmount : remainingAmount),
            hasPaid: false
        )
        participants.append(newParticipant)
    }
    
    private func createSplit() {
        guard participants.count >= 1 else {
            alertMessage = "You need at least one participant (yourself) to create a split."
            showingAlert = true
            return
        }
        
        print("DefineSplitView: Creating split with \(participants.count) participants")
        print("DefineSplitView: Total amount: \(totalAmount)")
        print("DefineSplitView: Participants: \(participants.map { "\($0.name): $\($0.share)" })")
        
        // Create split without requiring amounts to add up - people will join and set amounts later
        splitViewModel.createSplitInFirestore(
            totalAmount: Decimal(totalAmount),
            numberOfParticipants: numberOfParticipants,
            participants: participants
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let split):
                    print("🎯 SPLIT CREATED - Split ID: \(split.id ?? "unknown")")
                    print("DefineSplitView: Split created successfully with ID: \(split.id ?? "unknown")")
                    // Start listening to split updates to see participants join in real-time
                    if let splitId = split.id {
                        self.splitViewModel.listenToSplit(splitId: splitId)
                    }
                case .failure(let error):
                    print("DefineSplitView: Split creation failed: \(error.localizedDescription)")
                    self.alertMessage = "Failed to create split: \(error.localizedDescription)"
                    self.showingAlert = true
                }
            }
        }
    }
    
    private func generateQR(splitId: String) {
        print("DefineSplitView: Generating QR code for split ID: \(splitId)")
        qrCodeImage = qrCodeService.generateQRCode(from: splitId)
        print("DefineSplitView: QR code generated successfully: \(qrCodeImage != nil)")
    }
}

struct ParticipantRowView: View {
    @Binding var participant: Participant
    let suggestedAmount: Double
    let onRemove: () -> Void
    @State private var amountText: String = ""
    @State private var nameText: String = ""
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    if participant.id.isEmpty {
                        TextField("Participant Name", text: $nameText)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .onChange(of: nameText) { newValue in
                                participant.name = newValue
                            }
                    } else {
                        Text(participant.name)
                            .font(.headline)
                            .foregroundColor(Color.theme.accent)
                    }
                    
                    HStack {
                        Text("Amount:")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        TextField("0.00", text: $amountText)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 80)
                            .onChange(of: amountText) { newValue in
                                if let amount = Double(newValue) {
                                    participant.share = amount
                                }
                            }
                        
                        Button(action: {
                            amountText = String(format: "%.2f", suggestedAmount)
                            participant.share = suggestedAmount
                        }) {
                            Text("Suggest")
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.theme.accent.opacity(0.2))
                                .cornerRadius(6)
                        }
                    }
                }
                
                Spacer()
                
                VStack {
                    Text("$\(participant.share, specifier: "%.2f")")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(Color.theme.accent)
                    
                    if participant.id.isEmpty {
                        Button(action: onRemove) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.theme.accent.opacity(0.05))
        .cornerRadius(12)
        .onAppear {
            amountText = String(format: "%.2f", participant.share)
            nameText = participant.name
        }
    }
}
