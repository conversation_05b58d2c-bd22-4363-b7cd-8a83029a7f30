import SwiftUI

struct PreferencesView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("isDarkMode") private var isDarkMode = false
    @State private var notificationsEnabled = true
    @State private var soundEnabled = true
    @State private var hapticFeedback = true
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.surfaceSecondary.ignoresSafeArea(.all)
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.lg) {
                        // Appearance Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Appearance")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernCard(padding: Spacing.lg) {
                                HStack(spacing: Spacing.md) {
                                    Image(systemName: isDarkMode ? "moon.fill" : "sun.max.fill")
                                        .font(.system(size: 20, weight: .medium))
                                        .foregroundColor(Color.theme.primary)
                                        .frame(width: 24)

                                    VStack(alignment: .leading, spacing: 2) {
                                        Text("Dark Mode")
                                            .font(Font.theme.bodyMedium)
                                            .fontWeight(.medium)
                                            .foregroundColor(Color.theme.textPrimary)
                                        
                                        Text("Switch between light and dark themes")
                                            .font(Font.theme.labelMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }

                                    Spacer()

                                    Toggle("", isOn: $isDarkMode)
                                        .labelsHidden()
                                }
                            }
                        }
                        
                        // Notifications Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Notifications")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "bell.fill")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.primary)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Push Notifications")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Receive notifications for transactions")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Toggle("", isOn: $notificationsEnabled)
                                            .labelsHidden()
                                    }
                                    
                                    Divider()
                                        .background(Color.theme.neutral200)
                                    
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "speaker.wave.2.fill")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.info)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Sound")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Play sounds for notifications")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Toggle("", isOn: $soundEnabled)
                                            .labelsHidden()
                                    }
                                    
                                    Divider()
                                        .background(Color.theme.neutral200)
                                    
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "iphone.radiowaves.left.and.right")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.success)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Haptic Feedback")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Feel vibrations for interactions")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Toggle("", isOn: $hapticFeedback)
                                            .labelsHidden()
                                    }
                                }
                            }
                        }
                        
                        // Currency & Region Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Currency & Region")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "dollarsign.circle.fill")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.success)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Currency")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Australian Dollar (AUD)")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()
                                        
                                        Image(systemName: "chevron.right")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(Color.theme.textTertiary)
                                    }
                                    
                                    Divider()
                                        .background(Color.theme.neutral200)
                                    
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "globe")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.info)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Language")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("English")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()
                                        
                                        Image(systemName: "chevron.right")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(Color.theme.textTertiary)
                                    }
                                }
                            }
                        }
                        
                        // Privacy Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Privacy")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "eye.slash.fill")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.warning)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Hide Balance")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Hide balance on app switcher")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Toggle("", isOn: .constant(false))
                                            .labelsHidden()
                                    }
                                    
                                    Divider()
                                        .background(Color.theme.neutral200)
                                    
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "chart.bar.doc.horizontal")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.primary)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Analytics")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Help improve the app")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Toggle("", isOn: .constant(true))
                                            .labelsHidden()
                                    }
                                }
                            }
                        }
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.sm)
                }
            }
            .navigationTitle("Preferences")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
        }
    }
}

struct PreferencesView_Previews: PreviewProvider {
    static var previews: some View {
        PreferencesView()
    }
}
