import SwiftUI

struct NumericKeypad: View {
    var onKeyPress: (String) -> Void
    var onDelete: () -> Void

    @State private var pressedKey: String? = nil

    let columns: [GridItem] = Array(repeating: .init(.flexible(), spacing: Spacing.lg), count: 3)

    var body: some View {
        LazyVGrid(columns: columns, spacing: Spacing.lg) {
            // Numbers 1-9
            ForEach(1...9, id: \.self) { number in
                NumericKeyButton(
                    content: .number("\(number)"),
                    isPressed: pressedKey == "\(number)",
                    action: {
                        hapticFeedback()
                        onKeyPress("\(number)")
                    }
                )
            }

            // Empty space
            Color.clear
                .frame(height: 64)

            // Zero
            NumericKeyButton(
                content: .number("0"),
                isPressed: pressedKey == "0",
                action: {
                    hapticFeedback()
                    onKeyPress("0")
                }
            )

            // Delete button
            NumericKeyButton(
                content: .delete,
                isPressed: pressedKey == "delete",
                action: {
                    hapticFeedback()
                    onDelete()
                }
            )
        }
        .padding(.horizontal, Spacing.lg)
    }

    private func hapticFeedback() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}

// MARK: - NumericKeyButton Component
struct NumericKeyButton: View {
    enum Content {
        case number(String)
        case delete
    }

    let content: Content
    let isPressed: Bool
    let action: () -> Void

    @State private var isAnimating = false

    var body: some View {
        Button(action: {
            withAnimation(Animations.fast) {
                isAnimating = true
            }
            action()

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(Animations.fast) {
                    isAnimating = false
                }
            }
        }) {
            ZStack {
                // Background with modern styling
                RoundedRectangle(cornerRadius: BorderRadius.xl)
                    .fill(backgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: BorderRadius.xl)
                            .stroke(borderColor, lineWidth: 1)
                    )
                    .shadow(
                        color: shadowColor,
                        radius: isAnimating ? 2 : 4,
                        x: 0,
                        y: isAnimating ? 1 : 2
                    )

                // Content
                Group {
                    switch content {
                    case .number(let number):
                        Text(number)
                            .font(Font.theme.headlineSmall)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)
                    case .delete:
                        Image(systemName: "delete.left")
                            .font(.system(size: 22, weight: .medium))
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }
            }
            .frame(height: 64)
            .scaleEffect(isAnimating ? 0.95 : 1.0)
            .animation(Animations.bouncy, value: isAnimating)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var backgroundColor: Color {
        if isAnimating {
            return Color.theme.surfaceTertiary
        }
        return Color.theme.surfaceSecondary
    }

    private var borderColor: Color {
        Color.theme.textTertiary.opacity(0.1)
    }

    private var shadowColor: Color {
        Color.theme.textTertiary.opacity(0.05)
    }
}