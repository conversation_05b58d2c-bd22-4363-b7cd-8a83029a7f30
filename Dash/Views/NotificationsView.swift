import SwiftUI
import UserNotifications
import FirebaseFirestore

struct NotificationsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var notificationService: NotificationService
    @Environment(\.dismiss) private var dismiss
    
    @State private var notifications: [NotificationItem] = []
    @State private var isLoading = true
    @State private var hasPermission = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    if !hasPermission {
                        // Permission Request View
                        VStack(spacing: Spacing.lg) {
                            Image(systemName: "bell.slash")
                                .font(.system(size: 48, weight: .medium))
                                .foregroundColor(Color.theme.textTertiary)
                            
                            Text("Enable Notifications")
                                .font(Font.theme.titleLarge)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Text("Stay updated with transaction alerts, payment requests, and security notifications.")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, Spacing.lg)
                            
                            ModernButton("Enable Notifications", icon: "bell", style: .primary) {
                                requestNotificationPermission()
                            }
                            .padding(.horizontal, Spacing.lg)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.top, Spacing.xl)
                        
                    } else if isLoading {
                        // Loading State
                        VStack(spacing: Spacing.lg) {
                            ProgressView()
                                .scaleEffect(1.2)
                            
                            Text("Loading notifications...")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        
                    } else if notifications.isEmpty {
                        // Empty State
                        VStack(spacing: Spacing.lg) {
                            Image(systemName: "bell")
                                .font(.system(size: 48, weight: .medium))
                                .foregroundColor(Color.theme.textTertiary)
                            
                            Text("No Notifications")
                                .font(Font.theme.titleLarge)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Text("You're all caught up! Notifications will appear here when you receive them.")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, Spacing.lg)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.top, Spacing.xl)
                        
                    } else {
                        // Notifications List
                        ScrollView(showsIndicators: false) {
                            LazyVStack(spacing: Spacing.md) {
                                ForEach(notifications) { notification in
                                    NotificationRowView(notification: notification)
                                        .padding(.horizontal, Spacing.lg)
                                }
                                
                                // Bottom spacing
                                Color.clear.frame(height: Spacing.xl)
                            }
                            .padding(.top, Spacing.md)
                        }
                    }
                }
            }
            .navigationTitle("Notifications")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
                
                if hasPermission && !notifications.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Clear All") {
                            clearAllNotifications()
                        }
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.primary)
                    }
                }
            }
        }
        .onAppear {
            checkPermissionAndLoadNotifications()
            // Mark notifications as read when user opens the view
            markAllFirestoreNotificationsAsRead()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // Refresh notifications when app comes to foreground
            if hasPermission {
                loadNotifications()
            }
        }
    }
    
    private func checkPermissionAndLoadNotifications() {
        hasPermission = notificationService.hasPermission
        
        if hasPermission {
            loadNotifications()
        } else {
            isLoading = false
        }
    }
    
    private func requestNotificationPermission() {
        Task {
            let granted = await notificationService.requestPermission()
            await MainActor.run {
                hasPermission = granted
                if granted {
                    loadNotifications()
                }
            }
        }
    }
    
    private func loadNotifications() {
        isLoading = true
        print("NotificationsView: Loading notifications...")

        guard let currentUser = authViewModel.currentUser else {
            print("NotificationsView: No current user, cannot load notifications")
            isLoading = false
            return
        }

        print("NotificationsView: Loading notifications for user: \(currentUser.uid)")

        // Load notifications from Firestore
        let db = Firestore.firestore()
        db.collection("notifications")
            .whereField("userId", isEqualTo: currentUser.uid)
            .order(by: "createdAt", descending: true)
            .limit(to: 50)
            .getDocuments { querySnapshot, error in
                DispatchQueue.main.async {
                    if let error = error {
                        print("NotificationsView: Error loading Firebase notifications: \(error)")
                        // Still try to load system notifications even if Firebase fails
                        self.loadSystemNotifications { systemNotifications in
                            print("NotificationsView: Loaded \(systemNotifications.count) system notifications only")
                            self.notifications = systemNotifications.sorted { $0.date > $1.date }
                            self.isLoading = false
                        }
                        return
                    }

                    guard let documents = querySnapshot?.documents else {
                        print("NotificationsView: No Firebase documents found")
                        // Still try to load system notifications
                        self.loadSystemNotifications { systemNotifications in
                            print("NotificationsView: Loaded \(systemNotifications.count) system notifications only")
                            self.notifications = systemNotifications.sorted { $0.date > $1.date }
                            self.isLoading = false
                        }
                        return
                    }

                    print("NotificationsView: Found \(documents.count) Firebase notifications")
                    var firestoreNotifications: [NotificationItem] = []

                    for document in documents {
                        let data = document.data()
                        print("NotificationsView: Processing Firebase notification: \(document.documentID)")

                        guard let title = data["title"] as? String,
                              let message = data["message"] as? String else {
                            print("NotificationsView: Invalid notification data for \(document.documentID)")
                            continue
                        }

                        let type = data["type"] as? String ?? "general"
                        let isRead = data["isRead"] as? Bool ?? false
                        let createdAt = data["createdAt"] as? Timestamp
                        let date = createdAt?.dateValue() ?? Date()

                        firestoreNotifications.append(NotificationItem(
                            id: document.documentID,
                            title: title,
                            body: message,
                            date: date,
                            type: type,
                            isRead: isRead
                        ))
                    }

                    // Also get system notifications
                    self.loadSystemNotifications { systemNotifications in
                        print("NotificationsView: Loaded \(firestoreNotifications.count) Firebase + \(systemNotifications.count) system notifications")
                        // Combine and sort by date
                        self.notifications = (firestoreNotifications + systemNotifications)
                            .sorted { $0.date > $1.date }
                        self.isLoading = false
                    }
                }
            }
    }

    private func loadSystemNotifications(completion: @escaping ([NotificationItem]) -> Void) {
        print("NotificationsView: Loading system notifications...")
        var systemNotifications: [NotificationItem] = []

        // Get pending notifications from the system
        UNUserNotificationCenter.current().getPendingNotificationRequests { requests in
            print("NotificationsView: Found \(requests.count) pending notifications")
            let pendingNotifications = requests.map { request in
                print("NotificationsView: Pending notification - \(request.identifier): \(request.content.title)")
                return NotificationItem(
                    id: request.identifier,
                    title: request.content.title,
                    body: request.content.body,
                    date: Date(), // For pending notifications, we'll use current date
                    type: request.content.userInfo["type"] as? String ?? "general",
                    isRead: false
                )
            }
            systemNotifications.append(contentsOf: pendingNotifications)

            // Also get delivered notifications
            UNUserNotificationCenter.current().getDeliveredNotifications { notifications in
                print("NotificationsView: Found \(notifications.count) delivered notifications")
                let deliveredNotifications = notifications.map { notification in
                    print("NotificationsView: Delivered notification - \(notification.request.identifier): \(notification.request.content.title)")
                    return NotificationItem(
                        id: notification.request.identifier,
                        title: notification.request.content.title,
                        body: notification.request.content.body,
                        date: notification.date,
                        type: notification.request.content.userInfo["type"] as? String ?? "general",
                        isRead: true
                    )
                }
                systemNotifications.append(contentsOf: deliveredNotifications)

                print("NotificationsView: Total system notifications: \(systemNotifications.count)")
                DispatchQueue.main.async {
                    completion(systemNotifications)
                }
            }
        }
    }
    
    private func clearAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
        notificationService.clearBadge()

        // Mark all Firestore notifications as read
        markAllFirestoreNotificationsAsRead()

        // Clear the locally tracked shown notifications so they can be shown again if re-sent
        notificationService.clearShownNotifications()

        notifications.removeAll()
        notificationService.updateNotificationCount()
    }

    private func markAllFirestoreNotificationsAsRead() {
        guard let currentUser = authViewModel.currentUser else { return }

        let db = Firestore.firestore()
        db.collection("notifications")
            .whereField("userId", isEqualTo: currentUser.uid)
            .whereField("isRead", isEqualTo: false)
            .getDocuments { querySnapshot, error in
                if let error = error {
                    print("NotificationsView: Error getting notifications to mark as read: \(error)")
                    return
                }

                guard let documents = querySnapshot?.documents else { return }

                let batch = db.batch()
                for document in documents {
                    batch.updateData(["isRead": true], forDocument: document.reference)
                }

                batch.commit { error in
                    if let error = error {
                        print("NotificationsView: Error marking notifications as read: \(error)")
                    } else {
                        print("NotificationsView: Successfully marked \(documents.count) notifications as read")
                    }
                }
            }
    }
}

// MARK: - Notification Item Model
struct NotificationItem: Identifiable {
    let id: String
    let title: String
    let body: String
    let date: Date
    let type: String
    let isRead: Bool
}

// MARK: - Notification Row View
struct NotificationRowView: View {
    let notification: NotificationItem
    
    var body: some View {
        ModernCard(padding: Spacing.lg) {
            HStack(spacing: Spacing.md) {
                // Icon
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: iconName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(iconColor)
                }
                
                // Content
                VStack(alignment: .leading, spacing: 4) {
                    Text(notification.title)
                        .font(Font.theme.bodyMedium)
                        .fontWeight(.medium)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text(notification.body)
                        .font(Font.theme.labelMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .lineLimit(2)
                    
                    Text(notification.date.formatted(date: .abbreviated, time: .shortened))
                        .font(Font.theme.labelSmall)
                        .foregroundColor(Color.theme.textTertiary)
                }
                
                Spacer()
                
                // Unread indicator
                if !notification.isRead {
                    Circle()
                        .fill(Color.theme.primary)
                        .frame(width: 8, height: 8)
                }
            }
        }
    }
    
    private var iconName: String {
        switch notification.type {
        case "money_received", "money_sent":
            return "dollarsign.circle"
        case "money_requested", "request_paid":
            return "hand.raised"
        case "split_created", "split_joined", "split_completed":
            return "divide.circle"
        case "security_alert", "account_activity":
            return "shield"
        case "2fa_setup_prompt", "2fa_enabled", "2fa_disabled":
            return "lock.shield"
        default:
            return "bell"
        }
    }
    
    private var iconColor: Color {
        switch notification.type {
        case "money_received":
            return Color.theme.success
        case "money_sent":
            return Color.theme.primary
        case "money_requested", "request_paid":
            return Color.theme.warning
        case "split_created", "split_joined", "split_completed":
            return Color.theme.info
        case "security_alert":
            return Color.theme.error
        case "account_activity", "2fa_setup_prompt", "2fa_enabled", "2fa_disabled":
            return Color.theme.info
        default:
            return Color.theme.textSecondary
        }
    }
}

#Preview {
    NotificationsView()
        .environmentObject(AuthViewModel.shared)
        .environmentObject(NotificationService.shared)
}
