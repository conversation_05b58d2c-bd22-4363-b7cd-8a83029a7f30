import SwiftUI

struct CreateSplitView: View {
    @State private var amount: String = "0"
    @State private var numberOfParticipants: Int = 2
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var splitViewModel = SplitViewModel.shared
    @State private var showSplitDetail = false

    var body: some View {
        ZStack {
            Color.theme.background.ignoresSafeArea()

            VStack(spacing: 0) {
                // Modern Header
                ModernHeader(
                    title: "Create Split",
                    onDismiss: { presentationMode.wrappedValue.dismiss() }
                )

                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.xxxl) {
                        // Amount Display Section
                        VStack(spacing: Spacing.lg) {
                            Text("Total amount to split")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                                .padding(.top, Spacing.xxl)

                            // Large amount display
                            Text("$\(amount)")
                                .font(Font.theme.displaySmall)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                                .minimumScaleFactor(0.5)
                                .lineLimit(1)
                        }
                        .padding(.horizontal, Spacing.lg)

                        // Participants Section
                        ModernCard(padding: Spacing.lg) {
                            VStack(spacing: Spacing.md) {
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("Number of people")
                                            .font(Font.theme.bodyLarge)
                                            .fontWeight(.semibold)
                                            .foregroundColor(Color.theme.textPrimary)

                                        Text("Including yourself")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }

                                    Spacer()

                                    // Modern stepper
                                    HStack(spacing: Spacing.md) {
                                        Button(action: {
                                            if numberOfParticipants > 2 {
                                                numberOfParticipants -= 1
                                            }
                                        }) {
                                            Image(systemName: "minus")
                                                .font(.system(size: 16, weight: .semibold))
                                                .foregroundColor(numberOfParticipants > 2 ? Color.theme.primary : Color.theme.textTertiary)
                                                .frame(width: 32, height: 32)
                                                .background(Color.theme.surfaceSecondary)
                                                .clipShape(Circle())
                                        }
                                        .disabled(numberOfParticipants <= 2)

                                        Text("\(numberOfParticipants)")
                                            .font(Font.theme.bodyLarge)
                                            .fontWeight(.semibold)
                                            .foregroundColor(Color.theme.textPrimary)
                                            .frame(minWidth: 32)

                                        Button(action: {
                                            if numberOfParticipants < 100 {
                                                numberOfParticipants += 1
                                            }
                                        }) {
                                            Image(systemName: "plus")
                                                .font(.system(size: 16, weight: .semibold))
                                                .foregroundColor(numberOfParticipants < 100 ? Color.theme.primary : Color.theme.textTertiary)
                                                .frame(width: 32, height: 32)
                                                .background(Color.theme.surfaceSecondary)
                                                .clipShape(Circle())
                                        }
                                        .disabled(numberOfParticipants >= 100)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, Spacing.lg)

                        // Numeric Keypad
                        NumericKeypad(onKeyPress: appendAmount, onDelete: {
                            if amount.count > 1 {
                                amount.removeLast()
                            } else {
                                amount = "0"
                            }
                        })
                        .padding(.top, Spacing.xl)

                        // Bottom spacing for button
                        Color.clear.frame(height: 120)
                    }
                }

                // Bottom Action Area
                VStack(spacing: Spacing.md) {
                    // Continue Button
                    ModernButton(
                        "Continue",
                        icon: "arrow.right.circle.fill",
                        style: .primary,
                        size: .large,
                        isDisabled: amount == "0"
                    ) {
                        createSplitAndShowDetails()
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.bottom, Spacing.lg)
                }
                .background(
                    Color.theme.background
                        .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
                )
            }
        }
        .navigationBarHidden(true)
        .fullScreenCover(isPresented: $showSplitDetail, onDismiss: {
            // Check if split was abandoned when user dismisses the detail view
            splitViewModel.cancelCurrentSplitIfAbandoned()
            splitViewModel.reset()
            // Dismiss the CreateSplitView sheet as well to return to dashboard
            presentationMode.wrappedValue.dismiss()
        }) {
            SplitDetailView()
        }
        .onChange(of: splitViewModel.split) { newSplit in
            if newSplit != nil {
                showSplitDetail = true
            }
        }
        .onDisappear {
            // If user dismisses CreateSplitView without going to SplitDetailView, cancel any created split
            if !showSplitDetail {
                splitViewModel.cancelCurrentSplitIfAbandoned()
            }
        }
    }

    func appendAmount(_ number: String) {
        if amount == "0" {
            amount = number
        } else {
            amount += number
        }
    }

    private func createSplitAndShowDetails() {
        guard let creatorId = authViewModel.currentUser?.uid,
              let creatorName = authViewModel.currentUser?.displayName else {
            print("CreateSplitView: User not authenticated")
            return
        }

        let totalAmount = Double(amount) ?? 0

        // Create initial participant (creator)
        let creatorParticipant = Participant(
            id: creatorId,
            name: "\(creatorName) (You)",
            share: totalAmount / Double(numberOfParticipants), // Suggested amount
            hasPaid: false
        )

        print("CreateSplitView: Creating split with total: \(totalAmount), participants: \(numberOfParticipants)")

        splitViewModel.createSplitInFirestore(
            totalAmount: Decimal(totalAmount),
            numberOfParticipants: numberOfParticipants,
            participants: [creatorParticipant]
        ) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let split):
                    print("🎯 SPLIT CREATED - Split ID: \(split.id ?? "unknown")")
                    print("CreateSplitView: Split created successfully with ID: \(split.id ?? "unknown")")
                    // The onChange modifier will trigger showSplitDetail = true
                case .failure(let error):
                    print("CreateSplitView: Split creation failed: \(error.localizedDescription)")
                    // Could show an alert here if needed
                }
            }
        }
    }
}
