import SwiftUI

struct CreatePodView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var podName = ""
    @State private var podDescription = ""
    @State private var isPrivate = true
    @State private var maxMembers = 10
    @State private var selectedTags: Set<String> = []
    @State private var isCreating = false
    
    private let availableTags = ["Travel", "Food", "Utilities", "Rent", "Entertainment", "Shopping", "Bills", "Events", "Family", "Friends"]
    
    private var isFormValid: Bool {
        !podName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.xl) {
                        // Header
                        VStack(spacing: Spacing.md) {
                            ZStack {
                                Circle()
                                    .fill(Color.theme.info.opacity(0.1))
                                    .frame(width: 80, height: 80)
                                
                                Image(systemName: "person.3.fill")
                                    .font(.system(size: 32, weight: .medium))
                                    .foregroundColor(Color.theme.info)
                            }
                            
                            VStack(spacing: Spacing.sm) {
                                Text("Create New Pod")
                                    .font(Font.theme.titleLarge)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                Text("Set up a group to share expenses with friends and family")
                                    .font(Font.theme.bodyMedium)
                                    .foregroundColor(Color.theme.textSecondary)
                                    .multilineTextAlignment(.center)
                            }
                        }
                        .padding(.top, Spacing.lg)
                        
                        // Form
                        VStack(spacing: Spacing.lg) {
                            // Pod Name
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Pod Name")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernTextField("Enter pod name", text: $podName, placeholder: "e.g., Weekend Trip, House Bills")
                            }
                            
                            // Pod Description
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Description (Optional)")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernTextField("Enter description", text: $podDescription, placeholder: "What's this pod for?")
                            }
                            
                            // Privacy Setting
                            ModernCard(padding: Spacing.lg) {
                                VStack(alignment: .leading, spacing: Spacing.md) {
                                    Text("Privacy")
                                        .font(Font.theme.labelLarge)
                                        .fontWeight(.medium)
                                        .foregroundColor(Color.theme.textPrimary)
                                    
                                    VStack(spacing: Spacing.sm) {
                                        PrivacyOptionView(
                                            title: "Private",
                                            description: "Only invited members can join",
                                            icon: "lock.fill",
                                            isSelected: isPrivate
                                        ) {
                                            isPrivate = true
                                        }
                                        
                                        PrivacyOptionView(
                                            title: "Public",
                                            description: "Anyone with the link can join",
                                            icon: "globe",
                                            isSelected: !isPrivate
                                        ) {
                                            isPrivate = false
                                        }
                                    }
                                }
                            }
                            
                            // Max Members
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Maximum Members")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    HStack {
                                        Text("\(maxMembers) members")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                        
                                        Spacer()
                                        
                                        Stepper("", value: $maxMembers, in: 2...50)
                                            .labelsHidden()
                                    }
                                }
                            }
                            
                            // Tags
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Tags (Optional)")
                                    .font(Font.theme.labelLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: Spacing.sm) {
                                        ForEach(availableTags, id: \.self) { tag in
                                            TagButton(
                                                title: tag,
                                                isSelected: selectedTags.contains(tag)
                                            ) {
                                                if selectedTags.contains(tag) {
                                                    selectedTags.remove(tag)
                                                } else {
                                                    selectedTags.insert(tag)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                        
                        // Create Button
                        VStack(spacing: Spacing.md) {
                            ModernButton(
                                "Create Pod",
                                icon: "plus",
                                isLoading: isCreating,
                                isDisabled: !isFormValid
                            ) {
                                createPod()
                            }
                            .padding(.horizontal, Spacing.lg)
                            
                            Text("You can invite members after creating the pod")
                                .font(Font.theme.bodySmall)
                                .foregroundColor(Color.theme.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                }
            }
            .navigationTitle("New Pod")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .alert("Success", isPresented: .constant(podViewModel.successMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                    dismiss()
                }
            } message: {
                Text(podViewModel.successMessage ?? "")
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
        }
    }
    
    private func createPod() {
        print("🟡 CREATE_POD_DEBUG: createPod() called")

        guard let user = authViewModel.currentUser,
              let userId = user.uid,
              let userName = user.displayName else {
            print("🔴 CREATE_POD_DEBUG: User validation failed")
            print("🔴 CREATE_POD_DEBUG: Current user: \(String(describing: authViewModel.currentUser))")
            return
        }

        print("🟡 CREATE_POD_DEBUG: User validation passed")
        print("🟡 CREATE_POD_DEBUG: User ID: \(userId)")
        print("🟡 CREATE_POD_DEBUG: User name: \(userName)")

        isCreating = true

        let pod = Pod(
            name: podName.trimmingCharacters(in: .whitespacesAndNewlines),
            description: podDescription.isEmpty ? nil : podDescription.trimmingCharacters(in: .whitespacesAndNewlines),
            createdBy: userId,
            createdByName: userName,
            isPrivate: isPrivate,
            maxMembers: maxMembers,
            tags: Array(selectedTags)
        )

        print("🟡 CREATE_POD_DEBUG: Pod object created, calling podViewModel.createPod()")

        Task {
            let success = await podViewModel.createPod(pod)

            await MainActor.run {
                isCreating = false
                print("🟡 CREATE_POD_DEBUG: Pod creation completed, success: \(success)")
                if success {
                    print("🟢 CREATE_POD_DEBUG: Pod creation successful")
                    // Success is handled by the alert
                } else {
                    print("🔴 CREATE_POD_DEBUG: Pod creation failed")
                    // Error is handled by the alert
                }
            }
        }
    }
}

struct PrivacyOptionView: View {
    let title: String
    let description: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isSelected ? Color.theme.primary : Color.theme.textSecondary)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(Font.theme.bodyMedium)
                        .fontWeight(.medium)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text(description)
                        .font(Font.theme.bodySmall)
                        .foregroundColor(Color.theme.textSecondary)
                }
                
                Spacer()
                
                ZStack {
                    Circle()
                        .stroke(isSelected ? Color.theme.primary : Color.theme.textTertiary, lineWidth: 2)
                        .frame(width: 20, height: 20)
                    
                    if isSelected {
                        Circle()
                            .fill(Color.theme.primary)
                            .frame(width: 12, height: 12)
                    }
                }
            }
            .padding(.vertical, Spacing.sm)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct TagButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(Font.theme.labelMedium)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? Color.theme.surface : Color.theme.textSecondary)
                .padding(.horizontal, Spacing.md)
                .padding(.vertical, Spacing.sm)
                .background(
                    RoundedRectangle(cornerRadius: BorderRadius.md)
                        .fill(isSelected ? Color.theme.primary : Color.theme.surface)
                        .overlay(
                            RoundedRectangle(cornerRadius: BorderRadius.md)
                                .stroke(isSelected ? Color.clear : Color.theme.neutral300, lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    CreatePodView()
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
