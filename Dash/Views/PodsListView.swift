import SwiftUI

struct PodsListView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var isShowingCreatePod = false
    @State private var selectedPod: Pod?
    @State private var searchText = ""
    
    var filteredPods: [Pod] {
        if searchText.isEmpty {
            return podViewModel.pods.filter { $0.status == .active }
        } else {
            return podViewModel.pods.filter { pod in
                pod.status == .active &&
                (pod.name.localizedCaseInsensitiveContains(searchText) ||
                 pod.description?.localizedCaseInsensitiveContains(searchText) == true)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Search Bar
                    if !podViewModel.pods.isEmpty {
                        ModernTextField("Search pods...", text: $searchText, placeholder: "Search by name or description")
                            .padding(.horizontal, Spacing.lg)
                            .padding(.top, Spacing.md)
                    }
                    
                    if podViewModel.isLoading {
                        // Loading State
                        VStack(spacing: Spacing.lg) {
                            ProgressView()
                                .scaleEffect(1.2)

                            Text("Loading your pods...")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else if let errorMessage = podViewModel.errorMessage {
                        // Error State
                        VStack(spacing: Spacing.lg) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.system(size: 32, weight: .medium))
                                .foregroundColor(Color.theme.error)

                            Text("Error Loading Pods")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.textPrimary)

                            Text(errorMessage)
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, Spacing.lg)

                            ModernButton("Try Again", style: .secondary) {
                                if let userId = authViewModel.currentUser?.uid {
                                    podViewModel.fetchUserPods(userId: userId)
                                }
                            }
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else if filteredPods.isEmpty {
                        // Empty State
                        VStack(spacing: Spacing.xl) {
                            Spacer()
                            
                            ZStack {
                                Circle()
                                    .fill(Color.theme.info.opacity(0.1))
                                    .frame(width: 80, height: 80)
                                
                                Image(systemName: "person.3.fill")
                                    .font(.system(size: 32, weight: .medium))
                                    .foregroundColor(Color.theme.info)
                            }
                            
                            VStack(spacing: Spacing.md) {
                                Text(searchText.isEmpty ? "No Pods Yet" : "No Matching Pods")
                                    .font(Font.theme.titleLarge)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                Text(searchText.isEmpty ? 
                                     "Create your first pod to start sharing expenses with friends and family" :
                                     "Try adjusting your search terms")
                                    .font(Font.theme.bodyMedium)
                                    .foregroundColor(Color.theme.textSecondary)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal, Spacing.xl)
                            }
                            
                            if searchText.isEmpty {
                                ModernButton("Create Your First Pod", icon: "plus") {
                                    isShowingCreatePod = true
                                }
                                .padding(.horizontal, Spacing.xl)
                            }
                            
                            Spacer()
                        }
                    } else {
                        // Pods List
                        ScrollView(showsIndicators: false) {
                            LazyVStack(spacing: Spacing.md) {
                                ForEach(filteredPods) { pod in
                                    PodRowView(pod: pod) {
                                        selectedPod = pod
                                    }
                                    .padding(.horizontal, Spacing.lg)
                                }
                                
                                // Bottom spacing
                                Color.clear.frame(height: Spacing.xl)
                            }
                            .padding(.top, Spacing.md)
                        }
                    }
                }
            }
            .navigationTitle("My Pods")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        isShowingCreatePod = true
                    }) {
                        Image(systemName: "plus")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(Color.theme.primary)
                    }
                }
            }
            .sheet(isPresented: $isShowingCreatePod) {
                CreatePodView()
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .sheet(item: $selectedPod) { pod in
                PodDetailView(pod: pod)
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
            .onAppear {
                // Clear any previous error messages when the view appears
                podViewModel.clearMessages()
                print("🟡 PODS_LIST_DEBUG: PodsListView appeared, cleared error messages")
            }
        }
    }
}

struct PodRowView: View {
    let pod: Pod
    let action: () -> Void
    @EnvironmentObject var authViewModel: AuthViewModel
    
    private var userBalance: Double {
        guard let userId = authViewModel.currentUser?.uid else { return 0 }
        return pod.memberBalance(for: userId)
    }
    
    private var balanceText: String {
        if userBalance == 0 {
            return "Settled up"
        } else if userBalance > 0 {
            return "You owe $\(String(format: "%.2f", userBalance))"
        } else {
            return "You're owed $\(String(format: "%.2f", abs(userBalance)))"
        }
    }
    
    private var balanceColor: Color {
        if userBalance == 0 {
            return Color.theme.success
        } else if userBalance > 0 {
            return Color.theme.warning
        } else {
            return Color.theme.info
        }
    }
    
    var body: some View {
        Button(action: action) {
            ModernCard(padding: Spacing.lg) {
                HStack(spacing: Spacing.md) {
                    // Pod Icon
                    ZStack {
                        Circle()
                            .fill(Color.theme.info.opacity(0.1))
                            .frame(width: 50, height: 50)
                        
                        if let imageUrl = pod.imageUrl, !imageUrl.isEmpty {
                            // TODO: Add AsyncImage for pod image
                            Image(systemName: "person.3.fill")
                                .font(.system(size: 20, weight: .medium))
                                .foregroundColor(Color.theme.info)
                        } else {
                            Text(String(pod.name.prefix(2)).uppercased())
                                .font(Font.theme.labelLarge)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.info)
                        }
                    }
                    
                    // Pod Details
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(pod.name)
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Spacer()
                            
                            Image(systemName: "chevron.right")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color.theme.textTertiary)
                        }
                        
                        HStack {
                            Text("\(pod.activeMemberCount) members")
                                .font(Font.theme.bodySmall)
                                .foregroundColor(Color.theme.textSecondary)
                            
                            if pod.totalExpenses > 0 {
                                Text("•")
                                    .font(Font.theme.bodySmall)
                                    .foregroundColor(Color.theme.textTertiary)
                                
                                Text("$\(String(format: "%.2f", pod.totalExpenses)) total")
                                    .font(Font.theme.bodySmall)
                                    .foregroundColor(Color.theme.textSecondary)
                            }
                            
                            Spacer()
                        }
                        
                        Text(balanceText)
                            .font(Font.theme.labelMedium)
                            .fontWeight(.medium)
                            .foregroundColor(balanceColor)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    PodsListView()
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
