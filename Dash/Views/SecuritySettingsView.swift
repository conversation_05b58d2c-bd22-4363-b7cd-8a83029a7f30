import SwiftUI

struct SecuritySettingsView: View {
    @EnvironmentObject var viewModel: AuthViewModel
    @EnvironmentObject var securityViewModel: SecurityViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var isShowingPinSetup = false
    @State private var isShowingTwoFactorSetup = false
    @State private var isShowingTwoFactorManagement = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.surfaceSecondary.ignoresSafeArea(.all)
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.lg) {
                        // PIN Security Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("PIN Security")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            if !securityViewModel.isPinSet {
                                Button(action: { isShowingPinSetup = true }) {
                                    ModernCard(padding: Spacing.lg) {
                                        HStack(spacing: Spacing.md) {
                                            Image(systemName: "lock.fill")
                                                .font(.system(size: 20, weight: .medium))
                                                .foregroundColor(Color.theme.info)
                                                .frame(width: 24)

                                            VStack(alignment: .leading, spacing: 2) {
                                                Text("Set up PIN")
                                                    .font(Font.theme.bodyMedium)
                                                    .fontWeight(.medium)
                                                    .foregroundColor(Color.theme.textPrimary)
                                                
                                                Text("Secure your app with a PIN")
                                                    .font(Font.theme.labelMedium)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }

                                            Spacer()

                                            Image(systemName: "chevron.right")
                                                .font(.system(size: 14, weight: .medium))
                                                .foregroundColor(Color.theme.textTertiary)
                                        }
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            } else {
                                ModernCard(padding: Spacing.lg) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "lock.fill")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.success)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("PIN Enabled")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Your app is secured with a PIN")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()
                                        
                                        Text("Active")
                                            .font(Font.theme.labelSmall)
                                            .fontWeight(.medium)
                                            .foregroundColor(Color.theme.success)
                                            .padding(.horizontal, Spacing.sm)
                                            .padding(.vertical, 4)
                                            .background(Color.theme.success.opacity(0.1))
                                            .cornerRadius(BorderRadius.xs)
                                    }
                                }
                            }
                        }
                        
                        // Two-Factor Authentication Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Two-Factor Authentication")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Button(action: {
                                if viewModel.currentUser?.twoFactorEnabled == true {
                                    isShowingTwoFactorManagement = true
                                } else {
                                    isShowingTwoFactorSetup = true
                                }
                            }) {
                                ModernCard(padding: Spacing.lg) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "shield.checkered")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(viewModel.currentUser?.twoFactorEnabled == true ? Color.theme.success : Color.theme.warning)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Two-Factor Authentication")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text(viewModel.currentUser?.twoFactorEnabled == true ? "Manage your 2FA settings" : "Add an extra layer of security")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        if viewModel.currentUser?.twoFactorEnabled == true {
                                            Text("Enabled")
                                                .font(Font.theme.labelSmall)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.success)
                                                .padding(.horizontal, Spacing.sm)
                                                .padding(.vertical, 4)
                                                .background(Color.theme.success.opacity(0.1))
                                                .cornerRadius(BorderRadius.xs)
                                        }

                                        Image(systemName: "chevron.right")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(Color.theme.textTertiary)
                                    }
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        
                        // Biometric Authentication Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Biometric Authentication")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernCard(padding: Spacing.lg) {
                                HStack(spacing: Spacing.md) {
                                    Image(systemName: "faceid")
                                        .font(.system(size: 20, weight: .medium))
                                        .foregroundColor(Color.theme.primary)
                                        .frame(width: 24)

                                    VStack(alignment: .leading, spacing: 2) {
                                        Text("Face ID / Touch ID")
                                            .font(Font.theme.bodyMedium)
                                            .fontWeight(.medium)
                                            .foregroundColor(Color.theme.textPrimary)
                                        
                                        Text("Use biometrics to unlock the app")
                                            .font(Font.theme.labelMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }

                                    Spacer()

                                    Toggle("", isOn: .constant(viewModel.currentUser?.biometricEnabled ?? false))
                                        .labelsHidden()
                                }
                            }
                        }
                        
                        // Session Security Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Session Security")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "clock")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.info)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Session Timeout")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Auto-lock after inactivity")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()
                                        
                                        Text("15 min")
                                            .font(Font.theme.labelMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }
                                    
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "shield.lefthalf.filled")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.warning)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Require 2FA for Transactions")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Extra security for money transfers")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Toggle("", isOn: .constant(viewModel.currentUser?.requireTwoFactorForTransactions ?? false))
                                            .labelsHidden()
                                    }
                                }
                            }
                        }
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.sm)
                }
            }
            .navigationTitle("Security")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .sheet(isPresented: $isShowingPinSetup) {
                PINSetupView()
                    .environmentObject(securityViewModel)
            }
            .sheet(isPresented: $isShowingTwoFactorSetup) {
                TwoFactorSetupView()
                    .environmentObject(viewModel)
            }
            .sheet(isPresented: $isShowingTwoFactorManagement) {
                TwoFactorManagementView()
                    .environmentObject(viewModel)
            }
        }
    }
}

struct SecuritySettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SecuritySettingsView()
            .environmentObject(AuthViewModel.shared)
            .environmentObject(SecurityViewModel())
    }
}
