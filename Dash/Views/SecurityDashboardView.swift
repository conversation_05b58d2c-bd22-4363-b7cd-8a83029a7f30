import SwiftUI
import Foundation
import LocalAuthentication
import FirebaseFirestore

struct SecurityDashboardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var securityViewModel = SecurityViewModel()
    @State private var showingTwoFactorSetup = false
    @State private var showingTwoFactorManagement = false
    @State private var showingBackupCodes = false
    @State private var securityThreats: [SecurityThreat] = []
    @State private var isPerformingHealthCheck = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Security Score Card
                    securityScoreCard
                    
                    // Security Status Overview
                    securityStatusSection
                    
                    // Security Threats (if any)
                    if !securityThreats.isEmpty {
                        securityThreatsSection
                    }
                    
                    // Security Settings
                    securitySettingsSection
                    
                    // Advanced Security
                    advancedSecuritySection
                    
                    // Security Actions
                    securityActionsSection
                }
                .padding()
            }
            .navigationTitle("Security")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: performSecurityHealthCheck) {
                        Image(systemName: "shield.checkered")
                    }
                }
            }
            .onAppear {
                performSecurityHealthCheck()
            }
            .sheet(isPresented: $showingTwoFactorSetup) {
                TwoFactorSetupView()
            }
            .sheet(isPresented: $showingTwoFactorManagement) {
                TwoFactorManagementView()
            }
            .sheet(isPresented: $showingBackupCodes) {
                BackupCodesView()
            }
        }
    }
    
    private var securityScoreCard: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading) {
                    Text("Security Score")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("\(authViewModel.currentUser?.securityScore ?? 0)")
                        .font(.system(size: 48, weight: .bold, design: .rounded))
                        .foregroundColor(securityScoreColor)
                }
                
                Spacer()
                
                ZStack {
                    Circle()
                        .stroke(Color.gray.opacity(0.3), lineWidth: 8)
                        .frame(width: 80, height: 80)
                    
                    Circle()
                        .trim(from: 0, to: CGFloat(authViewModel.currentUser?.securityScore ?? 0) / 100)
                        .stroke(securityScoreColor, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                        .frame(width: 80, height: 80)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1), value: authViewModel.currentUser?.securityScore)
                }
            }
            
            Text(securityScoreDescription)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.leading)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var securityStatusSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Security Status")
                .font(.headline)
            
            VStack(spacing: 12) {
                SecurityStatusRow(
                    title: "Two-Factor Authentication",
                    isEnabled: authViewModel.currentUser?.twoFactorEnabled ?? false,
                    icon: "shield.checkered",
                    action: {
                        if authViewModel.currentUser?.twoFactorEnabled == true {
                            showingTwoFactorManagement = true
                        } else {
                            // Only allow setup if 2FA is not already enabled
                            showingTwoFactorSetup = true
                        }
                    }
                )
                
                SecurityStatusRow(
                    title: "Biometric Authentication",
                    isEnabled: authViewModel.currentUser?.biometricEnabled ?? false,
                    icon: "faceid",
                    action: toggleBiometric
                )
                
                SecurityStatusRow(
                    title: "Phone Number Verified",
                    isEnabled: !(authViewModel.currentUser?.phoneNumber?.isEmpty ?? true),
                    icon: "phone.circle",
                    action: nil
                )
                
                SecurityStatusRow(
                    title: "Security Notifications",
                    isEnabled: authViewModel.currentUser?.securityNotificationsEnabled ?? true,
                    icon: "bell.circle",
                    action: toggleSecurityNotifications
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var securityThreatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                Text("Security Recommendations")
                    .font(.headline)
            }
            
            VStack(spacing: 12) {
                ForEach(securityThreats.prefix(3), id: \.type) { threat in
                    SecurityThreatRow(threat: threat)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var securitySettingsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Security Settings")
                .font(.headline)
            
            VStack(spacing: 12) {
                SecurityLevelPicker(
                    currentLevel: authViewModel.currentUser?.securityLevel ?? .basic,
                    onLevelChange: updateSecurityLevel
                )
                
                if authViewModel.currentUser?.twoFactorEnabled == true {
                    Toggle("Require 2FA for Transactions", isOn: Binding(
                        get: { authViewModel.currentUser?.requireTwoFactorForTransactions ?? false },
                        set: { newValue in
                            updateRequire2FAForTransactions(newValue)
                        }
                    ))
                    .toggleStyle(SwitchToggleStyle(tint: .blue))
                }
                
                SessionTimeoutPicker(
                    currentTimeout: authViewModel.currentUser?.sessionTimeout ?? 900,
                    onTimeoutChange: updateSessionTimeout
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var advancedSecuritySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Advanced Security")
                .font(.headline)
            
            VStack(spacing: 12) {
                NavigationLink(destination: TrustedDevicesView()) {
                    HStack {
                        Image(systemName: "laptopcomputer.and.iphone")
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        
                        VStack(alignment: .leading) {
                            Text("Trusted Devices")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            Text("\(authViewModel.currentUser?.trustedDevices.count ?? 0) devices")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())
                
                NavigationLink(destination: SecurityAuditView()) {
                    HStack {
                        Image(systemName: "doc.text.magnifyingglass")
                            .foregroundColor(.blue)
                            .frame(width: 24)
                        
                        VStack(alignment: .leading) {
                            Text("Security Audit Log")
                                .font(.subheadline)
                                .fontWeight(.medium)
                            Text("View recent security events")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                    .padding(.vertical, 8)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
    
    private var securityActionsSection: some View {
        VStack(spacing: 12) {
            Button(action: performSecurityHealthCheck) {
                HStack {
                    if isPerformingHealthCheck {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "shield.checkered")
                    }
                    Text(isPerformingHealthCheck ? "Checking..." : "Run Security Health Check")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue)
                .cornerRadius(16)
            }
            .disabled(isPerformingHealthCheck)
            
            Button(action: {
                // Export security report
            }) {
                HStack {
                    Image(systemName: "square.and.arrow.up")
                    Text("Export Security Report")
                }
                .font(.subheadline)
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(16)
            }
        }
    }
    
    // MARK: - Computed Properties
    
    private var securityScoreColor: Color {
        let score = authViewModel.currentUser?.securityScore ?? 0
        switch score {
        case 0..<40:
            return .red
        case 40..<70:
            return .orange
        case 70..<90:
            return .yellow
        default:
            return .green
        }
    }
    
    private var securityScoreDescription: String {
        let score = authViewModel.currentUser?.securityScore ?? 0
        switch score {
        case 0..<40:
            return "Your account security needs improvement. Consider enabling additional security features."
        case 40..<70:
            return "Your account has basic security. Enable more features for better protection."
        case 70..<90:
            return "Good security level. Your account is well protected."
        default:
            return "Excellent security! Your account has maximum protection."
        }
    }
    
    // MARK: - Actions
    
    private func performSecurityHealthCheck() {
        guard let user = authViewModel.currentUser else { return }
        
        isPerformingHealthCheck = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            securityThreats = SecurityAuditService.shared.performSecurityHealthCheck(for: user)
            isPerformingHealthCheck = false
            
            // Log security audit
            if let userId = authViewModel.userSession?.uid {
                SecurityAuditService.shared.logSecurityEvent(.securitySettingsChanged, for: userId, metadata: [
                    "action": "security_health_check",
                    "threats_found": securityThreats.count
                ])
            }
        }
    }
    
    private func toggleBiometric() {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        let context = LAContext()
        var error: NSError?
        
        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            let newValue = !(authViewModel.currentUser?.biometricEnabled ?? false)
            
            // Update in Firestore
            authViewModel.updateUser(field: "biometricEnabled", value: newValue)
            
            // Log security event
            SecurityAuditService.shared.logSecurityEvent(
                newValue ? .biometricEnabled : .biometricDisabled,
                for: userId
            )
            
            if newValue {
                NotificationService.shared.notifyBiometricEnabled()
            }
        }
    }
    
    private func toggleSecurityNotifications() {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        let newValue = !(authViewModel.currentUser?.securityNotificationsEnabled ?? true)
        authViewModel.updateUser(field: "securityNotificationsEnabled", value: newValue)
        
        SecurityAuditService.shared.logSecurityEvent(.securitySettingsChanged, for: userId, metadata: [
            "setting": "security_notifications",
            "enabled": newValue
        ])
    }
    
    private func updateSecurityLevel(_ level: SecurityLevel) {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        authViewModel.updateUser(field: "securityLevel", value: level.rawValue)
        
        SecurityAuditService.shared.logSecurityEvent(.securitySettingsChanged, for: userId, metadata: [
            "setting": "security_level",
            "new_level": level.rawValue
        ])
        
        NotificationService.shared.notifySecurityLevelUpgraded(to: level.rawValue.capitalized)
    }
    
    private func updateRequire2FAForTransactions(_ require: Bool) {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        authViewModel.updateUser(field: "requireTwoFactorForTransactions", value: require)
        
        SecurityAuditService.shared.logSecurityEvent(.securitySettingsChanged, for: userId, metadata: [
            "setting": "require_2fa_transactions",
            "enabled": require
        ])
    }
    
    private func updateSessionTimeout(_ timeout: TimeInterval) {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        authViewModel.updateUser(field: "sessionTimeout", value: timeout)
        
        SecurityAuditService.shared.logSecurityEvent(.securitySettingsChanged, for: userId, metadata: [
            "setting": "session_timeout",
            "timeout_minutes": Int(timeout / 60)
        ])
    }
}

// MARK: - Supporting Views

struct SecurityStatusRow: View {
    let title: String
    let isEnabled: Bool
    let icon: String
    let action: (() -> Void)?
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(isEnabled ? .green : .gray)
                .frame(width: 24)
            
            Text(title)
                .font(.subheadline)
            
            Spacer()
            
            if isEnabled {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
            } else {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.red)
            }
            
            if let action = action {
                Button(action: action) {
                    Text(isEnabled ? "Manage" : "Enable")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(8)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

struct SecurityThreatRow: View {
    let threat: SecurityThreat
    
    var body: some View {
        HStack(alignment: .top) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(severityColor)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(threat.description)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(threat.recommendation)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
    }
    
    private var severityColor: Color {
        switch threat.severity {
        case 1...3:
            return .yellow
        case 4...6:
            return .orange
        default:
            return .red
        }
    }
}

struct SecurityLevelPicker: View {
    let currentLevel: SecurityLevel
    let onLevelChange: (SecurityLevel) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Security Level")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Picker("Security Level", selection: Binding(
                get: { currentLevel },
                set: onLevelChange
            )) {
                ForEach(SecurityLevel.allCases, id: \.self) { level in
                    Text(level.rawValue.capitalized).tag(level)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
}

struct SessionTimeoutPicker: View {
    let currentTimeout: TimeInterval
    let onTimeoutChange: (TimeInterval) -> Void
    
    private let timeoutOptions: [(String, TimeInterval)] = [
        ("5 minutes", 300),
        ("15 minutes", 900),
        ("30 minutes", 1800),
        ("1 hour", 3600),
        ("Never", 0)
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Session Timeout")
                .font(.subheadline)
                .fontWeight(.medium)
            
            Picker("Session Timeout", selection: Binding(
                get: { currentTimeout },
                set: onTimeoutChange
            )) {
                ForEach(timeoutOptions, id: \.1) { option in
                    Text(option.0).tag(option.1)
                }
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
}

// MARK: - Supporting Views

struct TrustedDevicesView: View {
    @EnvironmentObject var authViewModel: AuthViewModel

    var body: some View {
        List {
            ForEach(authViewModel.currentUser?.trustedDevices ?? [], id: \.deviceId) { device in
                VStack(alignment: .leading, spacing: 4) {
                    Text(device.deviceName)
                        .font(.headline)
                    Text("Added: \(device.addedDate, style: .date)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("Last used: \(device.lastUsed, style: .relative)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 4)
            }
        }
        .navigationTitle("Trusted Devices")
        .navigationBarTitleDisplayMode(.inline)
    }
}

struct SecurityAuditView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var auditLogs: [SecurityAuditLog] = []

    var body: some View {
        List {
            ForEach(auditLogs, id: \.id) { log in
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(log.event.replacingOccurrences(of: "_", with: " ").capitalized)
                            .font(.headline)
                        Spacer()
                        Image(systemName: log.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(log.success ? .green : .red)
                    }

                    Text(log.timestamp, style: .date)
                        .font(.caption)
                        .foregroundColor(.secondary)

                    if log.riskScore > 5 {
                        Text("Risk Score: \(log.riskScore)")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                .padding(.vertical, 4)
            }
        }
        .navigationTitle("Security Audit")
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            // Load audit logs from SecurityAuditService
            // This would typically fetch from Firestore
        }
    }
}

// MARK: - Extensions for AuthViewModel

extension AuthViewModel {
    func updateUser(field: String, value: Any) {
        guard let uid = userSession?.uid else { return }
        
        let db = Firestore.firestore()
        db.collection("users").document(uid).updateData([field: value]) { [weak self] error in
            if let error = error {
                self?.errorMessage = error.localizedDescription
            }
        }
    }
}
