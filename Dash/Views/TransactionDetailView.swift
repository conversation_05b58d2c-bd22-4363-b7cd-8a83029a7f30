import SwiftUI
import FirebaseFirestore
import FirebaseAuth

struct TransactionDetailView: View {
    let transaction: Transaction
    @State private var split: Split?
    @State private var isLoadingSplit = false
    @Environment(\.presentationMode) var presentationMode
    @State private var showingSplitDetail = false
    @ObservedObject private var splitViewModel = SplitViewModel.shared

    private var displayTitle: String {
        if transaction.type == .transfer {
            if transaction.name == "Money Sent" {
                return "Money Sent to \(transaction.recipientName ?? "Unknown")"
            } else if transaction.name == "Money Received" {
                return "Money Received from \(transaction.senderName ?? "Unknown")"
            }
        }
        return transaction.name
    }

    private var formattedAmount: Double {
        if transaction.type == .transfer && transaction.name == "Money Sent" {
            return -transaction.amount // Show negative for sent money
        }
        return transaction.amount // Show positive for received money and other types
    }

    private var transactionIcon: String {
        switch transaction.type {
        case .income:
            return "arrow.down.circle.fill"
        case .expense:
            return "arrow.up.circle.fill"
        case .transfer:
            if transaction.name == "Money Sent" {
                return "arrow.up.right.circle.fill"
            } else {
                return "arrow.down.left.circle.fill"
            }
        }
    }

    private var transactionIconColor: Color {
        switch transaction.type {
        case .income:
            return .green
        case .expense:
            return .red
        case .transfer:
            return .green
        }
    }

    private var amountColor: Color {
        if transaction.type == .transfer && transaction.name == "Money Sent" {
            return .primary // Sent money - neutral color
        }
        return transaction.type == .income || (transaction.type == .transfer && transaction.name == "Money Received") ? .green : .primary
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Transaction Header
                    VStack(alignment: .center, spacing: 12) {
                        Image(systemName: transactionIcon)
                            .font(.system(size: 60))
                            .foregroundColor(transactionIconColor)

                        Text(displayTitle)
                            .font(.title2)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)

                        Text(formattedAmount, format: .currency(code: "AUD"))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(amountColor)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical)
                    
                    Divider()
                    
                    // Transaction Details
                    VStack(alignment: .leading, spacing: 16) {
                        DetailRow(title: "Date & Time", value: formatDateTime(transaction.date.dateValue()))
                        DetailRow(title: "Type", value: transaction.type.rawValue.capitalized)
                        DetailRow(title: "Category", value: transaction.category)
                        DetailRow(title: "Status", value: transaction.status.rawValue.capitalized)
                        
                        if !transaction.detail.isEmpty {
                            DetailRow(title: "Description", value: transaction.detail)
                        }
                    }
                    
                    // Split Details (if applicable)
                    if transaction.splitId != nil {
                        Divider()
                        
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Split Details")
                                .font(.headline)
                                .fontWeight(.bold)
                            
                            if isLoadingSplit {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Loading split details...")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                            } else if let split = split {
                                VStack(alignment: .leading, spacing: 12) {
                                    DetailRow(title: "Total Amount", value: String(format: "$%.2f", split.totalAmount))
                                    DetailRow(title: "Participants", value: "\(split.participants.count)")
                                    DetailRow(title: "Split Status", value: split.status.rawValue.capitalized)
                                    
                                    Text("Participants:")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .padding(.top, 8)
                                    
                                    ForEach(split.participants, id: \.id) { participant in
                                        HStack {
                                            Text(participant.name)
                                                .font(.body)
                                            Spacer()
                                            Text(String(format: "$%.2f", participant.share))
                                                .font(.body)
                                                .fontWeight(.medium)
                                            if participant.hasPaid {
                                                Image(systemName: "checkmark.circle.fill")
                                                    .foregroundColor(.green)
                                                    .font(.caption)
                                            } else {
                                                Image(systemName: "clock.circle")
                                                    .foregroundColor(.orange)
                                                    .font(.caption)
                                            }
                                        }
                                        .padding(.vertical, 4)
                                        .padding(.horizontal, 12)
                                        .background(Color.gray.opacity(0.1))
                                        .cornerRadius(8)
                                    }

                                    // Add button to reopen split if it's completed
                                    if split.status == .complete {
                                        Button(action: {
                                            reopenSplit()
                                        }) {
                                            HStack {
                                                Image(systemName: "qrcode")
                                                Text("View Split QR Code")
                                            }
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(.white)
                                            .padding(.vertical, 12)
                                            .padding(.horizontal, 20)
                                            .background(Color.blue)
                                            .cornerRadius(10)
                                        }
                                        .padding(.top, 12)
                                    }
                                }
                            } else {
                                Text("Split details unavailable")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    
                    
                    Spacer(minLength: 20)
                }
                .padding()
            }
            .navigationTitle("Transaction Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("Done") {
                presentationMode.wrappedValue.dismiss()
            })
        }
        .onAppear {
            if let splitId = transaction.splitId {
                loadSplitDetails(splitId: splitId)
            }
        }
        .sheet(isPresented: $showingSplitDetail) {
            SplitDetailView()
        }
    }
    
    private func loadSplitDetails(splitId: String) {
        isLoadingSplit = true
        
        Firestore.firestore()
            .collection("splits")
            .document(splitId)
            .getDocument { document, error in
                DispatchQueue.main.async {
                    isLoadingSplit = false
                    
                    if let error = error {
                        print("Error loading split details: \(error.localizedDescription)")
                        return
                    }
                    
                    guard let document = document, document.exists else {
                        print("Split document does not exist")
                        return
                    }
                    
                    do {
                        var decodedSplit = try document.data(as: Split.self)
                        decodedSplit.id = document.documentID
                        self.split = decodedSplit
                    } catch {
                        print("Error decoding split: \(error.localizedDescription)")
                    }
                }
            }
    }

    private func reopenSplit() {
        guard let splitId = transaction.splitId else {
            print("TransactionDetailView: No split ID available")
            return
        }

        print("TransactionDetailView: Reopening split with ID: \(splitId)")
        splitViewModel.loadSplitForReopening(splitId: splitId)
        showingSplitDetail = true
    }

    private func formatDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
}

struct DetailRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .padding(.vertical, 2)
    }
}

struct TransactionDetailView_Previews: PreviewProvider {
    static var previews: some View {
        TransactionDetailView(transaction: Transaction(
            name: "Coffee Shop",
            detail: "Morning coffee",
            amount: 4.50,
            type: .expense,
            category: "Food & Drink",
            date: Timestamp(date: Date()),
            status: .completed,
            splitId: nil
        ))
    }
}
