import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var viewModel: AuthViewModel
    @EnvironmentObject var securityViewModel: SecurityViewModel
    @StateObject private var notificationService = NotificationService.shared
    @StateObject private var walletService = WalletPassService.shared
    @AppStorage("isDarkMode") private var isDarkMode = false

    @State private var isShowingSecurity = false
    @State private var isShowingPreferences = false
    @State private var isShowingWallet = false
    @State private var isShowingSupport = false

    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.surfaceSecondary.ignoresSafeArea(.all)

                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.lg) {
                        if let user = viewModel.currentUser {
                            // Account Details Card
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.lg) {
                                    // Profile Header
                                    HStack(spacing: Spacing.md) {
                                        // Profile Avatar
                                        ZStack {
                                            Circle()
                                                .fill(Color.theme.primary.opacity(0.1))
                                                .frame(width: 56, height: 56)

                                            Text(user.initial)
                                                .font(Font.theme.titleMedium)
                                                .fontWeight(.bold)
                                                .foregroundColor(Color.theme.primary)
                                        }

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text(user.displayName ?? "User")
                                                .font(Font.theme.titleMedium)
                                                .fontWeight(.semibold)
                                                .foregroundColor(Color.theme.textPrimary)

                                            Text(user.email ?? "")
                                                .font(Font.theme.bodyMedium)
                                                .foregroundColor(Color.theme.textSecondary)

                                            if let phone = user.phoneNumber {
                                                Text(phone)
                                                    .font(Font.theme.labelMedium)
                                                    .foregroundColor(Color.theme.textTertiary)
                                            }
                                        }

                                        Spacer()
                                        
                                        // Balance
                                        VStack(alignment: .trailing, spacing: 4) {
                                            Text("Balance")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)

                                            Text(user.balance ?? 0.0, format: .currency(code: "AUD"))
                                                .font(Font.theme.titleMedium)
                                                .fontWeight(.bold)
                                                .foregroundColor(Color.theme.textPrimary)
                                        }
                                    }
                                    

                                }
                            }
                        } else {
                            ModernCard(padding: Spacing.lg) {
                                HStack {
                                    Spacer()
                                    ProgressView()
                                        .scaleEffect(1.2)
                                    Spacer()
                                }
                            }
                        }

                        // Settings Categories
                        VStack(spacing: Spacing.md) {
                            // Security Settings
                            Button(action: { isShowingSecurity = true }) {
                                settingsCategoryCard(
                                    icon: "shield.checkered",
                                    title: "Security",
                                    subtitle: "PIN, 2FA, and privacy settings",
                                    iconColor: Color.theme.success
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            // Preferences Settings
                            Button(action: { isShowingPreferences = true }) {
                                settingsCategoryCard(
                                    icon: "gear",
                                    title: "Preferences",
                                    subtitle: "App settings and customization",
                                    iconColor: Color.theme.primary
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                            
                            // Apple Wallet
                            if walletService.isWalletAvailable() {
                                Button(action: { isShowingWallet = true }) {
                                    settingsCategoryCard(
                                        icon: "wallet.pass.fill",
                                        title: "Apple Wallet",
                                        subtitle: "Manage your Dash card passes",
                                        iconColor: Color.theme.info
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                            
                            // Support
                            Button(action: { isShowingSupport = true }) {
                                settingsCategoryCard(
                                    icon: "questionmark.circle.fill",
                                    title: "Support",
                                    subtitle: "Help and contact information",
                                    iconColor: Color.theme.warning
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }

                        // Sign Out Section
                        ModernButton(
                            "Sign Out",
                            icon: "rectangle.portrait.and.arrow.right",
                            style: .destructive,
                            size: .medium
                        ) {
                            viewModel.signOut()
                        }
                        .padding(.top, Spacing.lg)

                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.sm)
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)

            .sheet(isPresented: $isShowingSecurity) {
                SecuritySettingsView()
                    .environmentObject(viewModel)
                    .environmentObject(securityViewModel)
            }
            .sheet(isPresented: $isShowingPreferences) {
                PreferencesView()
            }
            .sheet(isPresented: $isShowingWallet) {
                WalletSettingsView()
                    .environmentObject(viewModel)
            }
            .sheet(isPresented: $isShowingSupport) {
                SupportView()
            }
            .onAppear {
                // Refresh wallet passes when view appears
                walletService.loadUserPasses()
            }
        }
    }
    
    // MARK: - Helper Views
    private func settingsCategoryCard(icon: String, title: String, subtitle: String, iconColor: Color) -> some View {
        ModernCard(padding: Spacing.lg) {
            HStack(spacing: Spacing.md) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(iconColor)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(Font.theme.bodyMedium)
                        .fontWeight(.medium)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text(subtitle)
                        .font(Font.theme.labelMedium)
                        .foregroundColor(Color.theme.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color.theme.textTertiary)
            }
        }
    }
}

struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsView()
            .environmentObject(AuthViewModel.shared)
            .environmentObject(SecurityViewModel())
    }
}
