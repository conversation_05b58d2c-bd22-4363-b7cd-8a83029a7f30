import SwiftUI

struct SplitDetailView: View {
    @ObservedObject private var viewModel = SplitViewModel.shared
    @State private var qrCodeImage: UIImage?
    @State private var showingPaymentConfirmation = false
    @State private var showingFinalizationAlert = false
    private let qrCodeService = QRCodeService()
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel

    private var allParticipantsJoined: Bool {
        guard let split = viewModel.split else { return false }
        return split.participants.count == split.numberOfParticipants
    }
    
    private var allParticipantsPaid: Bool {
        guard let split = viewModel.split else { return false }
        return split.participants.allSatisfy { $0.hasPaid }
    }
    
    private var totalAssignedAmount: Double {
        guard let split = viewModel.split else { return 0 }
        return split.participants.reduce(0) { $0 + $1.share }
    }
    
    private var canFinalizeSplit: Bool {
        guard let split = viewModel.split else { return false }
        return allParticipantsPaid && abs(totalAssignedAmount - split.totalAmount) < 0.01
    }
    
    private var isCreator: Bool {
        guard let split = viewModel.split,
              let currentUserId = authViewModel.currentUser?.uid else { return false }
        return split.creatorId == currentUserId
    }
    
    private var creatorHasPaid: Bool {
        guard let split = viewModel.split,
              let currentUserId = authViewModel.currentUser?.uid else { return false }
        return split.participants.first { $0.id == currentUserId }?.hasPaid ?? false
    }

    var body: some View {
        ZStack {
            Color.theme.background.ignoresSafeArea()

            if let split = viewModel.split {
                VStack(spacing: 0) {
                    // Modern Header
                    ModernHeader(
                        title: "Split Details",
                        onDismiss: { presentationMode.wrappedValue.dismiss() }
                    )

                    ScrollView(showsIndicators: false) {
                        VStack(spacing: Spacing.xxl) {
                            // QR Code Section
                            VStack(spacing: Spacing.lg) {
                                Text("Scan to Join Split")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)
                                    .padding(.top, Spacing.lg)

                                ModernCard(padding: Spacing.lg) {
                                    VStack(spacing: Spacing.lg) {
                                        if let image = qrCodeImage {
                                            Image(uiImage: image)
                                                .resizable()
                                                .interpolation(.none)
                                                .scaledToFit()
                                                .frame(width: 200, height: 200)
                                                .background(Color.theme.surface)
                                                .cornerRadius(BorderRadius.lg)
                                                .shadow(color: Color.theme.textTertiary.opacity(0.1), radius: 4, x: 0, y: 2)
                                        } else {
                                            VStack(spacing: Spacing.md) {
                                                ProgressView()
                                                    .scaleEffect(1.2)

                                                Text("Generating QR Code...")
                                                    .font(Font.theme.bodyMedium)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }
                                            .frame(width: 200, height: 200)
                                            .background(Color.theme.surfaceSecondary)
                                            .cornerRadius(BorderRadius.lg)
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)

                            // Split Summary
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    HStack {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text("Total Amount")
                                                .font(Font.theme.bodySmall)
                                                .foregroundColor(Color.theme.textSecondary)

                                            Text(split.totalAmount.toCurrency())
                                                .font(Font.theme.titleLarge)
                                                .fontWeight(.bold)
                                                .foregroundColor(Color.theme.textPrimary)
                                        }

                                        Spacer()

                                        VStack(alignment: .trailing, spacing: 4) {
                                            Text("Participants")
                                                .font(Font.theme.bodySmall)
                                                .foregroundColor(Color.theme.textSecondary)

                                            Text("\(split.participants.count) / \(split.numberOfParticipants)")
                                                .font(Font.theme.titleLarge)
                                                .fontWeight(.bold)
                                                .foregroundColor(Color.theme.primary)
                                        }
                                    }

                                    if allParticipantsJoined {
                                        Divider()
                                            .background(Color.theme.textTertiary.opacity(0.2))

                                        HStack(spacing: Spacing.sm) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(Color.theme.success)
                                                .font(.system(size: 16, weight: .medium))

                                            Text("All participants joined!")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.success)
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)

                            // Participants List
                            VStack(alignment: .leading, spacing: Spacing.md) {
                                Text("Participants")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)

                                VStack(spacing: Spacing.sm) {
                                    ForEach(split.participants, id: \.id) { participant in
                                        ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                                            HStack(spacing: Spacing.md) {
                                                // Avatar
                                                ZStack {
                                                    Circle()
                                                        .fill(Color.theme.primary.opacity(0.1))
                                                        .frame(width: 40, height: 40)

                                                    Text(String(participant.name.prefix(1).uppercased()))
                                                        .font(Font.theme.bodyMedium)
                                                        .fontWeight(.semibold)
                                                        .foregroundColor(Color.theme.primary)
                                                }

                                                VStack(alignment: .leading, spacing: 2) {
                                                    HStack(spacing: Spacing.xs) {
                                                        Text(participant.name)
                                                            .font(Font.theme.bodyLarge)
                                                            .fontWeight(.medium)
                                                            .foregroundColor(Color.theme.textPrimary)

                                                        if participant.id == authViewModel.currentUser?.uid {
                                                            Text("(You)")
                                                                .font(Font.theme.labelSmall)
                                                                .foregroundColor(Color.theme.textSecondary)
                                                                .padding(.horizontal, Spacing.xs)
                                                                .padding(.vertical, 2)
                                                                .background(Color.theme.surfaceSecondary)
                                                                .cornerRadius(BorderRadius.xs)
                                                        }
                                                    }

                                                    // Payment status
                                                    HStack(spacing: Spacing.xs) {
                                                        Circle()
                                                            .fill(participant.hasPaid ? Color.theme.success : Color.theme.warning)
                                                            .frame(width: 6, height: 6)

                                                        Text(participant.hasPaid ? "Paid" : "Pending")
                                                            .font(Font.theme.labelSmall)
                                                            .foregroundColor(participant.hasPaid ? Color.theme.success : Color.theme.warning)
                                                    }
                                                }

                                                Spacer()

                                                VStack(alignment: .trailing, spacing: 4) {
                                                    Text(participant.share.toCurrency())
                                                        .font(Font.theme.bodyLarge)
                                                        .fontWeight(.semibold)
                                                        .foregroundColor(Color.theme.textPrimary)

                                                    if !participant.hasPaid && participant.id == authViewModel.currentUser?.uid {
                                                        Button("Pay Now") {
                                                            payUserShare(participant: participant)
                                                        }
                                                        .font(Font.theme.labelSmall)
                                                        .foregroundColor(Color.theme.primary)
                                                        .padding(.horizontal, Spacing.sm)
                                                        .padding(.vertical, 4)
                                                        .background(Color.theme.primary.opacity(0.1))
                                                        .cornerRadius(BorderRadius.xs)
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)

                            // Status and Action Section
                            if isCreator && allParticipantsPaid && !canFinalizeSplit {
                                ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.md) {
                                    VStack(spacing: Spacing.sm) {
                                        HStack(spacing: Spacing.sm) {
                                            Image(systemName: "exclamationmark.triangle.fill")
                                                .foregroundColor(Color.theme.warning)
                                                .font(.system(size: 16, weight: .medium))

                                            Text("Amounts Don't Match")
                                                .font(Font.theme.bodyLarge)
                                                .fontWeight(.semibold)
                                                .foregroundColor(Color.theme.warning)
                                        }

                                        VStack(spacing: 4) {
                                            Text("Total assigned: \(totalAssignedAmount.toCurrency())")
                                                .font(Font.theme.bodySmall)
                                                .foregroundColor(Color.theme.textSecondary)

                                            Text("Split total: \((viewModel.split?.totalAmount ?? 0).toCurrency())")
                                                .font(Font.theme.bodySmall)
                                                .foregroundColor(Color.theme.textSecondary)

                                            Text("Ask participants to adjust their amounts")
                                                .font(Font.theme.bodySmall)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.warning)
                                        }
                                    }
                                }
                                .padding(.horizontal, Spacing.lg)
                            } else if isCreator && !allParticipantsPaid {
                                ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.md) {
                                    VStack(spacing: Spacing.sm) {
                                        HStack(spacing: Spacing.sm) {
                                            Image(systemName: "clock.fill")
                                                .foregroundColor(Color.theme.primary)
                                                .font(.system(size: 16, weight: .medium))

                                            Text("Waiting for Payments")
                                                .font(Font.theme.bodyLarge)
                                                .fontWeight(.semibold)
                                                .foregroundColor(Color.theme.primary)
                                        }

                                        Text("Some participants haven't paid yet")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }
                                }
                                .padding(.horizontal, Spacing.lg)
                            }

                            // Bottom spacing
                            Color.clear.frame(height: 120)
                        }
                    }

                    // Bottom Action Area
                    VStack(spacing: Spacing.md) {
                        if isCreator && !creatorHasPaid && allParticipantsJoined {
                            let creatorShare = split.participants.first { $0.id == authViewModel.currentUser?.uid }?.share ?? 0
                            ModernButton(
                                "Pay My Share (\(creatorShare.toCurrency()))",
                                icon: "creditcard.fill",
                                style: .primary,
                                size: .large
                            ) {
                                showingPaymentConfirmation = true
                            }
                            .padding(.horizontal, Spacing.lg)
                        }

                        if isCreator && canFinalizeSplit {
                            ModernButton(
                                "Finalize Split",
                                icon: "checkmark.circle.fill",
                                style: .primary,
                                size: .large
                            ) {
                                showingFinalizationAlert = true
                            }
                            .padding(.horizontal, Spacing.lg)
                        }

                        ModernButton(
                            "Close",
                            icon: "xmark.circle.fill",
                            style: .secondary,
                            size: .large
                        ) {
                            presentationMode.wrappedValue.dismiss()
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.bottom, Spacing.lg)
                    }
                    .background(
                        Color.theme.background
                            .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
                    )
                }
            } else {
                VStack(spacing: Spacing.lg) {
                    ProgressView()
                        .scaleEffect(1.2)

                    Text("Loading Split...")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            generateQR()
            if let splitId = viewModel.split?.id {
                viewModel.listenToSplit(splitId: splitId)
            }
        }
        .alert("Pay Your Share", isPresented: $showingPaymentConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Pay Now") {
                payCreatorShare()
            }
        } message: {
            let userShare = viewModel.split?.participants.first { $0.id == authViewModel.currentUser?.uid }?.share ?? 0
            Text("This will deduct \(userShare.toCurrency()) from your Dash balance.")
        }
        .alert("Finalize Split", isPresented: $showingFinalizationAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Finalize") {
                finalizeSplit()
            }
        } message: {
            Text("This will complete the split and close it for all participants.")
        }
    }

    private func generateQR() {
        guard let splitId = viewModel.split?.id else { return }
        self.qrCodeImage = qrCodeService.generateQRCode(from: splitId)
    }
    
    private func payCreatorShare() {
        guard let split = viewModel.split,
              let splitId = split.id,
              let creatorParticipant = split.participants.first(where: { $0.id == authViewModel.currentUser?.uid }) else { return }
        
        viewModel.joinAndPayForSplit(splitId: splitId, amount: creatorParticipant.share) { result in
            switch result {
            case .success(_):
                print("Creator payment successful")
            case .failure(let error):
                print("Creator payment failed: \(error.localizedDescription)")
            }
        }
    }
    
    private func payUserShare(participant: Participant) {
        guard let split = viewModel.split,
              let splitId = split.id else { return }
        
        viewModel.joinAndPayForSplit(splitId: splitId, amount: participant.share) { result in
            switch result {
            case .success(_):
                print("User payment successful")
            case .failure(let error):
                print("User payment failed: \(error.localizedDescription)")
            }
        }
    }
    
    private func finalizeSplit() {
        guard let split = viewModel.split,
              let splitId = split.id else { return }

        viewModel.finalizeSplit(splitId: splitId, participants: split.participants)

        // Open Apple Wallet for the split creator
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            WalletPassService.shared.openWalletApp()
        }

        presentationMode.wrappedValue.dismiss()
    }
}
