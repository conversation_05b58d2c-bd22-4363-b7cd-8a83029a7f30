import SwiftUI

struct TwoFactorVerificationView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var verificationCode = ""
    @State private var showErrorAlert = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Modern gradient background
                LinearGradient(
                    colors: [Color.theme.surfaceSecondary, Color.theme.surface],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea(.all)

                VStack(spacing: Spacing.xl) {
                    Spacer()

                    // Logo and Icon
                    VStack(spacing: Spacing.lg) {
                        ZStack {
                            Circle()
                                .fill(Color.white)
                                .frame(width: 80, height: 80)

                            Image(systemName: "lock.shield.fill")
                                .font(.system(size: 32, weight: .medium))
                                .foregroundColor(Color.theme.success)
                        }

                        VStack(spacing: Spacing.sm) {
                            Text("Two-Factor Authentication")
                                .font(Font.theme.titleLarge)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                                .multilineTextAlignment(.center)

                            Text("Enter the 6-digit code from your authenticator app")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    
                    // Error/Attempts Warning
                    if authViewModel.failedLoginAttempts > 0 {
                        HStack(spacing: Spacing.sm) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color.theme.warning)

                            Text("Attempts remaining: \(5 - authViewModel.failedLoginAttempts)")
                                .font(Font.theme.labelMedium)
                                .foregroundColor(Color.theme.warning)
                        }
                        .padding(.horizontal, Spacing.md)
                        .padding(.vertical, Spacing.sm)
                        .background(Color.theme.warning.opacity(0.1))
                        .cornerRadius(BorderRadius.md)
                    }

                    // Verification Code Input
                    VStack(spacing: Spacing.lg) {
                        TextField("000000", text: $verificationCode)
                            .keyboardType(.numberPad)
                            .textContentType(.oneTimeCode)
                            .multilineTextAlignment(.center)
                            .font(.system(size: 28, weight: .medium, design: .monospaced))
                            .foregroundColor(Color.theme.textPrimary)
                            .padding(.vertical, Spacing.lg)
                            .padding(.horizontal, Spacing.xl)
                            .background(Color.theme.surfaceSecondary)
                            .cornerRadius(BorderRadius.lg)
                            .overlay(
                                RoundedRectangle(cornerRadius: BorderRadius.lg)
                                    .stroke(verificationCode.count == 6 ? Color.theme.success : Color.clear, lineWidth: 2)
                            )
                            .onChange(of: verificationCode) { newValue in
                                // Limit to 6 digits
                                if newValue.count > 6 {
                                    verificationCode = String(newValue.prefix(6))
                                }
                                // Auto-verify when 6 digits are entered
                                if newValue.count == 6 {
                                    verifyCode()
                                }
                            }

                        // Verify Button
                        Button(action: verifyCode) {
                            HStack {
                                if authViewModel.isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                } else {
                                    Text("Verify Code")
                                        .font(Font.theme.titleLarge)
                                        .fontWeight(.semibold)
                                }
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, Spacing.md)
                            .background(verificationCode.count == 6 ? Color.theme.success : Color.theme.neutral400)
                            .cornerRadius(BorderRadius.lg)
                        }
                        .disabled(verificationCode.count != 6 || authViewModel.isLoading)
                    }
                    
                    Spacer(minLength: 40)

                    // Sign Out Button
                    Button(action: {
                        authViewModel.signOut()
                    }) {
                        Text("Sign Out")
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.success)
                    }
                    .padding(.bottom, Spacing.lg)

                    Spacer()
                }
                .padding(.horizontal, Spacing.lg)
            }
            .navigationBarHidden(true)
        }
        .interactiveDismissDisabled(true) // Prevent dismissal
        .alert("Authentication Error", isPresented: $showErrorAlert) {
            Button("OK") {
                authViewModel.errorMessage = nil
                verificationCode = ""
            }
        } message: {
            Text(authViewModel.errorMessage ?? "An unknown error occurred.")
        }
        .onChange(of: authViewModel.errorMessage) { _, newValue in
            if newValue != nil {
                showErrorAlert = true
            }
        }
        .onChange(of: authViewModel.isLockedOut) { _, isLockedOut in
            if isLockedOut {
                // User will be automatically signed out by AuthViewModel
            }
        }
    }
    
    private func verifyCode() {
        authViewModel.verifyTwoFactorCode(verificationCode)
    }
}

struct TwoFactorVerificationView_Previews: PreviewProvider {
    static var previews: some View {
        TwoFactorVerificationView()
            .environmentObject(AuthViewModel.shared)
    }
}
