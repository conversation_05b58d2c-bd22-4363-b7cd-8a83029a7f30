import SwiftUI
import UIKit

struct InviteMembersView: View {
    let pod: Pod
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var inviteMethod: InviteMethod = .shareLink
    @State private var isGenerating = false
    @State private var generatedInviteCode = ""
    @State private var showingShareSheet = false
    @State private var showingQRCode = false
    @State private var qrCodeImage: UIImage?

    enum InviteMethod: String, CaseIterable {
        case shareLink = "Share Link"
        case qrCode = "QR Code"

        var icon: String {
            switch self {
            case .shareLink: return "square.and.arrow.up"
            case .qrCode: return "qrcode"
            }
        }

        var description: String {
            switch self {
            case .shareLink: return "Share an invite link via messages, email, or social media"
            case .qrCode: return "Generate a QR code that others can scan to join"
            }
        }
    }
    
    private var isFormValid: Bool {
        return true // Always valid since we're just generating links/QR codes
    }

    private var inviteLink: String {
        guard !generatedInviteCode.isEmpty else { return "" }
        return "dash://join/\(generatedInviteCode)"
    }

    // MARK: - View Components

    private var headerSection: some View {
        VStack(spacing: Spacing.md) {
            ZStack {
                Circle()
                    .fill(Color.theme.info.opacity(0.1))
                    .frame(width: 60, height: 60)

                Image(systemName: "person.badge.plus.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(Color.theme.info)
            }

            VStack(spacing: Spacing.sm) {
                Text("Invite Members")
                    .font(Font.theme.titleLarge)
                    .fontWeight(.bold)
                    .foregroundColor(Color.theme.textPrimary)

                Text("Add people to \"\(pod.name)\"")
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.textSecondary)
            }
        }
        .padding(.top, Spacing.lg)
    }

    private var inviteMethodSelector: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Choose Invite Method")
                .font(Font.theme.labelLarge)
                .fontWeight(.medium)
                .foregroundColor(Color.theme.textPrimary)
                .padding(.horizontal, Spacing.lg)

            ModernCard(padding: Spacing.lg) {
                VStack(spacing: Spacing.md) {
                    ForEach(InviteMethod.allCases, id: \.self) { method in
                        InviteMethodRow(
                            method: method,
                            isSelected: inviteMethod == method
                        ) {
                            inviteMethod = method
                        }
                    }
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }

    private var methodDescriptionSection: some View {
        VStack(spacing: Spacing.md) {
            Text(inviteMethod.description)
                .font(Font.theme.bodyMedium)
                .foregroundColor(Color.theme.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, Spacing.lg)

            // Generated Content Display
            if !generatedInviteCode.isEmpty {
                generatedContentCard
            }
        }
    }

    private var generatedContentCard: some View {
        ModernCard(padding: Spacing.lg) {
            VStack(spacing: Spacing.lg) {
                if inviteMethod == .shareLink {
                    shareLinkDisplay
                } else {
                    qrCodeDisplay
                }
            }
        }
        .padding(.horizontal, Spacing.lg)
    }

    private var shareLinkDisplay: some View {
        VStack(spacing: Spacing.md) {
            HStack {
                Text("Invite Link")
                    .font(Font.theme.labelLarge)
                    .fontWeight(.medium)
                    .foregroundColor(Color.theme.textPrimary)

                Spacer()

                Button("Copy") {
                    UIPasteboard.general.string = inviteLink
                }
                .font(Font.theme.labelMedium)
                .foregroundColor(Color.theme.primary)
            }

            Text(inviteLink)
                .font(Font.theme.bodyMedium)
                .foregroundColor(Color.theme.textSecondary)
                .padding()
                .background(Color.theme.surfaceSecondary)
                .cornerRadius(BorderRadius.sm)
                .frame(maxWidth: .infinity, alignment: .leading)

            ModernButton("Share Link", icon: "square.and.arrow.up", style: .primary) {
                showingShareSheet = true
            }
        }
    }

    private var qrCodeDisplay: some View {
        VStack(spacing: Spacing.md) {
            HStack {
                Text("QR Code")
                    .font(Font.theme.labelLarge)
                    .fontWeight(.medium)
                    .foregroundColor(Color.theme.textPrimary)

                Spacer()

                // Success indicator
                HStack(spacing: 4) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(Color.theme.success)
                        .font(.system(size: 14))
                    Text("Ready")
                        .font(Font.theme.labelSmall)
                        .foregroundColor(Color.theme.success)
                }
            }

            if let qrImage = qrCodeImage {
                Image(uiImage: qrImage)
                    .interpolation(.none)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 200, height: 200)
                    .background(Color.white)
                    .cornerRadius(BorderRadius.md)
                    .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
            }

            Text("Others can scan this QR code with the Dash app to join \"\(pod.name)\"")
                .font(Font.theme.bodySmall)
                .foregroundColor(Color.theme.textSecondary)
                .multilineTextAlignment(.center)

            HStack(spacing: Spacing.md) {
                ModernButton("Share QR", icon: "square.and.arrow.up", style: .secondary) {
                    showingShareSheet = true
                }

                ModernButton("Save to Photos", icon: "photo", style: .secondary) {
                    saveQRCodeToPhotos()
                }
            }
        }
    }

    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()

                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.xl) {
                        headerSection
                        inviteMethodSelector
                        methodDescriptionSection
                        actionButtonSection
                        currentMembersSection

                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                }
            }
            .navigationTitle("Invite Members")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .alert("Success", isPresented: .constant(podViewModel.successMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                    // Don't dismiss the view - let user continue using the invite features
                }
            } message: {
                Text(podViewModel.successMessage ?? "")
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
            .sheet(isPresented: $showingShareSheet) {
                shareSheetContent
            }
        }
    }

    private var actionButtonSection: some View {
        Group {
            if generatedInviteCode.isEmpty {
                VStack(spacing: Spacing.md) {
                    ModernButton(
                        inviteMethod == .shareLink ? "Generate Share Link" : "Generate QR Code",
                        icon: inviteMethod.icon,
                        style: .primary,
                        isLoading: isGenerating
                    ) {
                        generateInvite()
                    }
                    .padding(.horizontal, Spacing.lg)
                }
            }
        }
    }

    private var currentMembersSection: some View {
        Group {
            if !pod.members.isEmpty {
                VStack(alignment: .leading, spacing: Spacing.md) {
                    HStack {
                        Text("Current Members (\(pod.activeMemberCount))")
                            .font(Font.theme.labelLarge)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)

                        Spacer()
                    }
                    .padding(.horizontal, Spacing.lg)

                    LazyVStack(spacing: Spacing.sm) {
                        ForEach(pod.members.filter { $0.isActive }) { member in
                            memberRow(member)
                        }
                    }
                }
            }
        }
    }

    private func memberRow(_ member: PodMember) -> some View {
        ModernCard(padding: Spacing.md) {
            HStack {
                ZStack {
                    Circle()
                        .fill(Color.theme.primary.opacity(0.1))
                        .frame(width: 32, height: 32)

                    Text(member.displayName.prefix(1).uppercased())
                        .font(Font.theme.labelMedium)
                        .fontWeight(.bold)
                        .foregroundColor(Color.theme.primary)
                }

                VStack(alignment: .leading, spacing: 2) {
                    Text(member.displayName)
                        .font(Font.theme.bodyMedium)
                        .fontWeight(.medium)
                        .foregroundColor(Color.theme.textPrimary)

                    if let email = member.email {
                        Text(email)
                            .font(Font.theme.labelSmall)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }

                Spacer()

                if member.role == .admin {
                    Text("ADMIN")
                        .font(Font.theme.labelSmall)
                        .fontWeight(.bold)
                        .foregroundColor(Color.theme.info)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.theme.info.opacity(0.1))
                        .cornerRadius(4)
                }
            }
        }
        .padding(.horizontal, Spacing.lg)
    }

    private var shareSheetContent: some View {
        Group {
            if inviteMethod == .shareLink {
                ShareSheet(activityItems: [inviteLink])
            } else if let qrImage = qrCodeImage {
                ShareSheet(activityItems: [qrImage])
            }
        }
    }

    // MARK: - Methods

    private func generateInvite() {
        guard let podId = pod.id,
              let currentUser = authViewModel.currentUser,
              let currentUserId = currentUser.uid,
              let currentUserName = currentUser.displayName else {
            return
        }

        isGenerating = true
        // Clear any existing messages to prevent immediate alert
        podViewModel.clearMessages()

        Task {
            let invitation = PodInvitation(
                podId: podId,
                podName: pod.name,
                invitedBy: currentUserId,
                invitedByName: currentUserName,
                invitedEmail: "<EMAIL>" // Placeholder for general invitations
            )

            let success = await podViewModel.createInvitation(invitation)

            await MainActor.run {
                isGenerating = false
                if success {
                    generatedInviteCode = invitation.inviteCode
                    print("🟢 INVITE_DEBUG: Invitation created successfully, code: \(invitation.inviteCode)")

                    // Generate QR code if needed
                    if inviteMethod == .qrCode {
                        generateQRCode()
                    }

                    // Clear the success message to prevent alert from showing
                    // The QR code display itself indicates success
                    podViewModel.clearMessages()
                } else {
                    print("🔴 INVITE_DEBUG: Failed to create invitation")
                }
            }
        }
    }

    private func generateQRCode() {
        print("🔵 QR_DEBUG: Generating QR code for invite link: \(inviteLink)")
        print("🔵 QR_DEBUG: Generated invite code: \(generatedInviteCode)")
        let qrService = QRCodeService()
        qrCodeImage = qrService.generateQRCode(from: inviteLink)
        print("🔵 QR_DEBUG: QR code generated successfully: \(qrCodeImage != nil)")
    }

    private func saveQRCodeToPhotos() {
        guard let image = qrCodeImage else { return }
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
    }
}

struct InviteMethodRow: View {
    let method: InviteMembersView.InviteMethod
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: Spacing.sm) {
                HStack(spacing: Spacing.md) {
                    Image(systemName: method.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(isSelected ? Color.theme.primary : Color.theme.textSecondary)
                        .frame(width: 20)

                    Text(method.rawValue)
                        .font(Font.theme.bodyMedium)
                        .fontWeight(.medium)
                        .foregroundColor(Color.theme.textPrimary)

                    Spacer()

                    ZStack {
                        Circle()
                            .stroke(isSelected ? Color.theme.primary : Color.theme.textTertiary, lineWidth: 2)
                            .frame(width: 20, height: 20)

                        if isSelected {
                            Circle()
                                .fill(Color.theme.primary)
                                .frame(width: 12, height: 12)
                        }
                    }
                }

                if isSelected {
                    Text(method.description)
                        .font(Font.theme.labelMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            .padding(.vertical, Spacing.sm)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Share Sheet
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    InviteMembersView(pod: Pod(name: "Test Pod", createdBy: "user1", createdByName: "John Doe"))
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
