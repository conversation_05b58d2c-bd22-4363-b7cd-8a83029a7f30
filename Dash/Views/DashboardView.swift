import SwiftUI

struct DashboardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var securityViewModel: SecurityViewModel
    @EnvironmentObject var notificationService: NotificationService
    @StateObject private var splitViewModel = SplitViewModel.shared
    @StateObject private var podViewModel = PodViewModel.shared
    @State private var searchText = ""
    @State private var isShowingCreateSplit = false
    @State private var isShowingAddMoney = false
    @State private var isShowingScanner = false
    @State private var isShowingSendMoney = false
    @State private var isShowingJoinSplitSheet = false
    @State private var isShowingPhoneNumberPrompt = false
    @State private var isShowingPodsList = false
    @State private var isShowingNotifications = false
    @State private var isShowingFullTransactions = false
    @State private var isShowingPaymentRequests = false
    @State private var scannedCode: String?
    @State private var pendingSplitId: String?
    @State private var pendingPodInviteCode: String?
    @State private var isShowingPodInviteSheet = false


    var body: some View {
        ZStack {
            Color.theme.surfaceSecondary.ignoresSafeArea(.all)

            ScrollView(showsIndicators: false) {
                LazyVStack(alignment: .leading, spacing: Spacing.xl) {
                    // Header
                    ModernHeaderView(
                        authViewModel: authViewModel,
                        notificationService: notificationService,
                        podViewModel: podViewModel,
                        isShowingNotifications: $isShowingNotifications,
                        isShowingPaymentRequests: $isShowingPaymentRequests
                    )
                    .padding(.horizontal, Spacing.lg)

                    // Virtual Card
                    VirtualCardView(
                        balance: authViewModel.currentUser?.balance ?? 0.0
                    )
                    .padding(.horizontal, Spacing.lg)

                    // Quick Actions
                    ModernActionButtonsView(
                        isShowingAddMoney: $isShowingAddMoney,
                        isShowingCreateSplit: $isShowingCreateSplit,
                        isShowingScanner: $isShowingScanner,
                        isShowingSendMoney: $isShowingSendMoney,
                        isShowingJoinSplitSheet: $isShowingJoinSplitSheet,
                        scannedCode: $scannedCode
                    )
                    .padding(.horizontal, Spacing.lg)

                    // Quick Stats
                    ModernQuickStatsView(authViewModel: authViewModel, podViewModel: podViewModel, isShowingPodsList: $isShowingPodsList)
                        .padding(.horizontal, Spacing.lg)

                    // Recent Activity Section
                    VStack(alignment: .leading, spacing: Spacing.md) {
                        HStack {
                            Text("Recent Activity")
                                .font(Font.theme.headlineSmall)
                                .foregroundColor(Color.theme.textPrimary)

                            Spacer()

                            Button("See All") {
                                isShowingFullTransactions = true
                            }
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.primary)
                        }
                        .padding(.horizontal, Spacing.lg)

                        ModernTransactionsListView(searchText: $searchText)
                            .environmentObject(authViewModel)
                    }

                    // Bottom spacing for safe area
                    Color.clear.frame(height: Spacing.xl)
                }
                .padding(.top, Spacing.sm)
            }
            .sheet(isPresented: $isShowingCreateSplit) {
                CreateSplitView()
                    .environmentObject(authViewModel)
                    .environmentObject(splitViewModel)
            }
            .sheet(isPresented: $isShowingAddMoney) { AddMoneyView() }
            .sheet(isPresented: $isShowingSendMoney) { 
                SendMoneyView()
                    .environmentObject(authViewModel)
                    .environmentObject(securityViewModel)
            }
            .sheet(isPresented: $isShowingPhoneNumberPrompt) {
                PhoneNumberPromptView()
                    .environmentObject(authViewModel)
            }
            .fullScreenCover(isPresented: $isShowingScanner) {
                ZStack {
                    QRCodeScannerView { code in
                        print("DashboardView: Scanned QR Code: \(code)")

                        // Validate the code first
                        guard !code.isEmpty else {
                            print("DashboardView: ERROR - Empty QR code")
                            return
                        }

                        // Determine if this is a pod invitation or split code
                        DispatchQueue.main.async {
                            self.scannedCode = code

                            if code.contains("dash://join/") || code.contains("dash.app/join/") {
                                // This is a pod invitation link
                                let inviteCode = code.replacingOccurrences(of: "dash://join/", with: "")
                                                     .replacingOccurrences(of: "https://dash.app/join/", with: "")
                                                     .replacingOccurrences(of: "http://dash.app/join/", with: "")
                                                     .replacingOccurrences(of: "dash.app/join/", with: "")
                                self.pendingPodInviteCode = inviteCode
                                print("DashboardView: Detected pod invitation code: \(inviteCode)")
                            } else {
                                // Assume it's a split code
                                self.pendingSplitId = code
                                print("DashboardView: Detected split code: \(code)")
                            }

                            self.isShowingScanner = false
                            print("DashboardView: Scanner dismissed")
                        }
                    }

                    // Exit button overlay
                    VStack {
                        HStack {
                            Button(action: {
                                isShowingScanner = false
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(Color.black.opacity(0.3))
                                        .frame(width: 44, height: 44)

                                    Image(systemName: "xmark")
                                        .font(.system(size: 18, weight: .semibold))
                                        .foregroundColor(.white)
                                }
                            }
                            .padding(.top, 50)
                            .padding(.leading, 20)

                            Spacer()
                        }

                        Spacer()
                    }
                }
            }
            .onChange(of: isShowingScanner) { isShowing in
                // When scanner is dismissed and we have a scanned code, show the appropriate sheet
                if !isShowing && scannedCode != nil {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        if self.pendingPodInviteCode != nil {
                            print("DashboardView: About to show pod invite sheet with code: \(self.pendingPodInviteCode ?? "nil")")
                            self.isShowingPodInviteSheet = true
                        } else if self.pendingSplitId != nil {
                            print("DashboardView: About to show split sheet with splitId: \(self.pendingSplitId ?? "nil")")
                            self.isShowingJoinSplitSheet = true
                        }
                    }
                }
            }
            .sheet(isPresented: $isShowingJoinSplitSheet, onDismiss: {
                print("DashboardView: Join split sheet dismissed, clearing codes")
                scannedCode = nil
                pendingSplitId = nil
                // Clear any error state when sheet is dismissed
                splitViewModel.errorMessage = nil
            }) {
                Group {
                    if let splitId = pendingSplitId ?? scannedCode, !splitId.isEmpty {
                        JoinSplitWrapperView(splitId: splitId)
                            .environmentObject(authViewModel)
                            .onAppear {
                                print("DashboardView: Sheet is showing with splitId: \(splitId)")
                                // Clear any previous error state when showing new split
                                splitViewModel.errorMessage = nil
                            }
                    } else {
                        VStack(spacing: 20) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.system(size: 50))
                                .foregroundColor(.orange)
                            
                            Text("Error: No split ID found")
                                .font(.headline)
                                .padding()
                            
                            VStack {
                                Text("Scanned code: \(scannedCode ?? "nil")")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                Text("Pending ID: \(pendingSplitId ?? "nil")")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                            
                            Button("Dismiss") {
                                isShowingJoinSplitSheet = false
                            }
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .padding()
                        .onAppear {
                            print("DashboardView: Sheet showing error - scannedCode: \(scannedCode ?? "nil"), pendingSplitId: \(pendingSplitId ?? "nil")")
                        }
                    }
                }
            }
            .sheet(isPresented: $isShowingPodInviteSheet, onDismiss: {
                print("DashboardView: Pod invite sheet dismissed, clearing codes")
                scannedCode = nil
                pendingPodInviteCode = nil
                // Clear any error state when sheet is dismissed
                podViewModel.clearMessages()
            }) {
                if let inviteCode = pendingPodInviteCode {
                    PodInviteJoinView(inviteCode: inviteCode)
                        .environmentObject(authViewModel)
                        .environmentObject(podViewModel)
                } else {
                    Text("Error: No invite code found")
                        .font(.headline)
                        .padding()
                }
            }
            .sheet(isPresented: $isShowingPodsList) {
                PodsListView()
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .sheet(isPresented: $isShowingNotifications) {
                NotificationsView()
                    .environmentObject(authViewModel)
                    .environmentObject(notificationService)
            }
            .sheet(isPresented: $isShowingFullTransactions) {
                FullTransactionsView()
                    .environmentObject(authViewModel)
            }
            .sheet(isPresented: $isShowingPaymentRequests) {
                PaymentRequestsView()
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .onAppear {
                checkPhoneNumberRequirement()
                // Fetch user's pods and payment requests
                if let userId = authViewModel.currentUser?.uid {
                    print("Fetching pods for user: \(userId)")
                    podViewModel.fetchUserPods(userId: userId)
                    podViewModel.fetchPaymentRequests(userId: userId)
                } else {
                    print("No current user found when trying to fetch pods")
                }

                // Listen for deep link pod invitations
                NotificationCenter.default.addObserver(
                    forName: NSNotification.Name("HandlePodInvite"),
                    object: nil,
                    queue: .main
                ) { notification in
                    if let inviteCode = notification.userInfo?["inviteCode"] as? String {
                        print("DashboardView: Received deep link pod invitation: \(inviteCode)")
                        self.pendingPodInviteCode = inviteCode
                        self.isShowingPodInviteSheet = true
                    }
                }
            }
            .onChange(of: authViewModel.currentUser) { _, currentUser in
                if currentUser != nil {
                    checkPhoneNumberRequirement()
                    // Fetch user's pods and payment requests when user changes
                    if let userId = currentUser?.uid {
                        podViewModel.fetchUserPods(userId: userId)
                        podViewModel.fetchPaymentRequests(userId: userId)
                    }
                }
            }
            .onDisappear {
                // Clean up notification observer
                NotificationCenter.default.removeObserver(self, name: NSNotification.Name("HandlePodInvite"), object: nil)
            }
        }
    }
    
    private func checkPhoneNumberRequirement() {
        // Check if user needs to add phone number
        if let currentUser = authViewModel.currentUser,
           currentUser.phoneNumber?.isEmpty ?? true {
            // Delay showing the prompt to allow the view to settle
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                isShowingPhoneNumberPrompt = true
            }
        }
    }
}

struct ModernHeaderView: View {
    @ObservedObject var authViewModel: AuthViewModel
    @ObservedObject var notificationService: NotificationService
    @ObservedObject var podViewModel: PodViewModel
    @Binding var isShowingNotifications: Bool
    @Binding var isShowingPaymentRequests: Bool
    @State private var isShowingSettings = false
    @State private var currentTime = Date()

    private let timer = Timer.publish(every: 60, on: .main, in: .common).autoconnect()

    var body: some View {
        HStack(alignment: .top) {
            VStack(alignment: .leading, spacing: Spacing.xs) {
                Text(greetingText)
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.textSecondary)

                Text(authViewModel.currentUser?.displayName ?? "User")
                    .font(Font.theme.headlineMedium)
                    .fontWeight(.bold)
                    .foregroundColor(Color.theme.textPrimary)
            }

            Spacer()

            HStack(spacing: Spacing.md) {
                // Payment requests button
                Button(action: {
                    isShowingPaymentRequests = true
                }) {
                    ZStack {
                        Circle()
                            .fill(Color.theme.neutral100)
                            .frame(width: 44, height: 44)

                        Image(systemName: "paperplane")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(Color.theme.textPrimary)

                        // Payment requests badge
                        if !podViewModel.paymentRequests.isEmpty {
                            ZStack {
                                Circle()
                                    .fill(Color.theme.warning)
                                    .frame(width: 16, height: 16)

                                Text("\(min(podViewModel.paymentRequests.count, 9))")
                                    .font(.system(size: 10, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            .offset(x: 8, y: -8)
                        }
                    }
                }

                // Notifications button
                Button(action: {
                    isShowingNotifications = true
                }) {
                    ZStack {
                        Circle()
                            .fill(Color.theme.neutral100)
                            .frame(width: 44, height: 44)

                        Image(systemName: "bell")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(Color.theme.textPrimary)

                        // Notification badge
                        if notificationService.notificationCount > 0 {
                            ZStack {
                                Circle()
                                    .fill(Color.theme.error)
                                    .frame(width: 16, height: 16)

                                Text("\(min(notificationService.notificationCount, 9))")
                                    .font(.system(size: 10, weight: .bold))
                                    .foregroundColor(.white)
                            }
                            .offset(x: 8, y: -8)
                        }
                    }
                }

                // Settings button
                Button(action: { isShowingSettings = true }) {
                    ZStack {
                        Circle()
                            .fill(Color.theme.neutral100)
                            .frame(width: 44, height: 44)

                        Image(systemName: "person.circle")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(Color.theme.textPrimary)
                    }
                }
            }
        }
        .onReceive(timer) { _ in
            currentTime = Date()
        }
        .sheet(isPresented: $isShowingSettings) {
            SettingsView()
                .environmentObject(authViewModel)
        }
    }

    private var greetingText: String {
        let hour = Calendar.current.component(.hour, from: currentTime)
        switch hour {
        case 5..<12: return "Good morning"
        case 12..<17: return "Good afternoon"
        case 17..<22: return "Good evening"
        default: return "Good night"
        }
    }
}

struct ModernActionButtonsView: View {
    @Binding var isShowingAddMoney: Bool
    @Binding var isShowingCreateSplit: Bool
    @Binding var isShowingScanner: Bool
    @Binding var isShowingSendMoney: Bool
    @Binding var isShowingJoinSplitSheet: Bool
    @Binding var scannedCode: String?

    var body: some View {
        ModernCard(padding: Spacing.lg) {
            VStack(spacing: Spacing.lg) {
                HStack {
                    Text("Quick Actions")
                        .font(Font.theme.titleMedium)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)

                    Spacer()
                }

                HStack(spacing: 0) {
                    ModernActionButton(
                        icon: "arrow.up.right",
                        title: "Send",
                        subtitle: "Money",
                        color: Color.theme.primary
                    ) {
                        isShowingSendMoney = true
                    }
                    .frame(maxWidth: .infinity)

                    ModernActionButton(
                        icon: "plus",
                        title: "Add",
                        subtitle: "Funds",
                        color: Color.theme.success
                    ) {
                        isShowingAddMoney = true
                    }
                    .frame(maxWidth: .infinity)

                    ModernActionButton(
                        icon: "divide",
                        title: "Split",
                        subtitle: "Bill",
                        color: Color.theme.warning
                    ) {
                        isShowingCreateSplit = true
                    }
                    .frame(maxWidth: .infinity)

                    ModernActionButton(
                        icon: "qrcode.viewfinder",
                        title: "Scan",
                        subtitle: "QR Code",
                        color: Color.theme.info
                    ) {
                        print("DashboardView: Scan button tapped")
                        isShowingScanner = true
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
    }
}

// MARK: - Modern Quick Stats View
struct ModernQuickStatsView: View {
    @ObservedObject var authViewModel: AuthViewModel
    @ObservedObject var podViewModel: PodViewModel
    @Binding var isShowingPodsList: Bool

    var body: some View {
        ModernCard(padding: Spacing.lg) {
            VStack(spacing: Spacing.lg) {
                HStack {
                    Text("Quick Stats")
                        .font(Font.theme.titleMedium)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)

                    Spacer()
                }

                HStack(spacing: 0) {
                    // This Month Spending
                    VStack(alignment: .center, spacing: Spacing.sm) {
                        HStack {
                            Image(systemName: "chart.line.uptrend.xyaxis")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.theme.primary)

                            Text("This Month")
                                .font(Font.theme.labelMedium)
                                .foregroundColor(Color.theme.textSecondary)
                        }

                        Text("$2,450")
                            .font(Font.theme.titleLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)

                        HStack(spacing: 4) {
                            Image(systemName: "arrow.up.right")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(Color.theme.success)

                            Text("+12%")
                                .font(Font.theme.labelSmall)
                                .foregroundColor(Color.theme.success)
                        }
                    }
                    .frame(maxWidth: .infinity)

                    // Savings
                    VStack(alignment: .center, spacing: Spacing.sm) {
                        HStack {
                            Image(systemName: "banknote")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.theme.success)

                            Text("Saved")
                                .font(Font.theme.labelMedium)
                                .foregroundColor(Color.theme.textSecondary)
                        }

                        Text("$890")
                            .font(Font.theme.titleLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)

                        HStack(spacing: 4) {
                            Image(systemName: "arrow.up.right")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(Color.theme.success)

                            Text("+5%")
                                .font(Font.theme.labelSmall)
                                .foregroundColor(Color.theme.success)
                        }
                    }
                    .frame(maxWidth: .infinity)

                    // Pods tile
                    ModernActionButton(
                        icon: "person.3.fill",
                        title: "Pods",
                        subtitle: "Groups",
                        color: Color.theme.info
                    ) {
                        isShowingPodsList = true
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
    }

    private var totalOutstandingBalance: Double {
        return podViewModel.pods.reduce(0) { total, pod in
            if let userId = authViewModel.currentUser?.uid {
                return total + pod.memberBalance(for: userId)
            }
            return total
        }
    }
}

// MARK: - Modern Transactions List View
struct ModernTransactionsListView: View {
    @Binding var searchText: String
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var transactionService = TransactionService.shared

    private var recentTransactions: [Transaction] {
        // Show only the 3 most recent transactions for dashboard
        let allTransactions = transactionService.transactions
        return Array(allTransactions.prefix(3))
    }

    var body: some View {
        LazyVStack(spacing: Spacing.sm) {
            if transactionService.isLoading {
                // Loading state
                ForEach(0..<3, id: \.self) { _ in
                    ModernTransactionRowSkeleton()
                        .padding(.horizontal, Spacing.lg)
                }
            } else if recentTransactions.isEmpty {
                // Empty state
                ModernCard(padding: Spacing.xl) {
                    VStack(spacing: Spacing.md) {
                        Image(systemName: "list.bullet.rectangle")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(Color.theme.textTertiary)

                        Text("No transactions yet")
                            .font(Font.theme.titleMedium)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)

                        Text("Your recent activity will appear here")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding(.horizontal, Spacing.lg)
            } else {
                // Real transactions
                ForEach(recentTransactions) { transaction in
                    ModernTransactionRow(transaction: transaction)
                        .padding(.horizontal, Spacing.lg)
                }
            }
        }
        .onAppear {
            transactionService.loadTransactions()
        }
    }
}

struct ModernTransactionRow: View {
    let transaction: Transaction
    @State private var showingDetail = false

    private var displayName: String {
        if transaction.type == .transfer {
            // For transfers, show the other party's name based on transaction direction
            if transaction.name == "Money Sent" {
                return transaction.recipientName ?? "Unknown Recipient"
            } else if transaction.name == "Money Received" {
                return transaction.senderName ?? "Unknown Sender"
            }
        }
        return transaction.name
    }

    private var displayDetail: String {
        if transaction.type == .transfer {
            // Use the transaction name to determine the detail
            if transaction.name == "Money Sent" {
                return "Money Sent"
            } else if transaction.name == "Money Received" {
                return "Money Received"
            }
        }
        return transaction.detail
    }

    private var transactionIcon: String {
        switch transaction.type {
        case .income:
            return "arrow.down.circle.fill"
        case .expense:
            if transaction.splitId != nil {
                return "divide.circle.fill"
            }
            return "arrow.up.circle.fill"
        case .transfer:
            if transaction.recipientName != nil {
                return "arrow.up.right.circle.fill"
            } else {
                return "arrow.down.left.circle.fill"
            }
        }
    }

    private var transactionColor: Color {
        switch transaction.type {
        case .income:
            return Color.theme.success
        case .expense:
            if transaction.splitId != nil {
                return Color.theme.info
            }
            return Color.theme.warning
        case .transfer:
            return Color.theme.primary
        }
    }

    private var isPositiveAmount: Bool {
        switch transaction.type {
        case .income:
            return true
        case .transfer:
            return transaction.name == "Money Received" // Received money is positive
        case .expense:
            return false
        }
    }

    private var formattedTime: String {
        let formatter = DateFormatter()
        let calendar = Calendar.current
        let transactionDate = transaction.date.dateValue()
        let now = Date()

        if calendar.isDate(transactionDate, inSameDayAs: now) {
            formatter.timeStyle = .short
            return "Today, \(formatter.string(from: transactionDate))"
        } else if let yesterday = calendar.date(byAdding: .day, value: -1, to: now),
                  calendar.isDate(transactionDate, inSameDayAs: yesterday) {
            return "Yesterday"
        } else {
            formatter.dateStyle = .short
            return formatter.string(from: transactionDate)
        }
    }

    var body: some View {
        Button(action: { showingDetail = true }) {
            ModernCard(padding: Spacing.lg) {
                HStack(spacing: Spacing.md) {
                    // Icon
                    ZStack {
                        Circle()
                            .fill(transactionColor.opacity(0.1))
                            .frame(width: 44, height: 44)

                        Image(systemName: transactionIcon)
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(transactionColor)
                    }

                    // Transaction details
                    VStack(alignment: .leading, spacing: 2) {
                        Text(displayName)
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)

                        Text(formattedTime)
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                    }

                    Spacer()

                    // Amount
                    Text("\(isPositiveAmount ? "+" : "")\(transaction.amount, format: .currency(code: "AUD"))")
                        .font(Font.theme.bodyMedium)
                        .fontWeight(.semibold)
                        .foregroundColor(isPositiveAmount ? Color.theme.success : Color.theme.textPrimary)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDetail) {
            TransactionDetailView(transaction: transaction)
        }
    }
}

// MARK: - Loading Skeleton
struct ModernTransactionRowSkeleton: View {
    @State private var isAnimating = false

    var body: some View {
        ModernCard(padding: Spacing.lg) {
            HStack(spacing: Spacing.md) {
                // Icon skeleton
                Circle()
                    .fill(Color.theme.neutral200)
                    .frame(width: 44, height: 44)

                // Text skeleton
                VStack(alignment: .leading, spacing: 6) {
                    Rectangle()
                        .fill(Color.theme.neutral200)
                        .frame(width: 120, height: 14)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(Color.theme.neutral200)
                        .frame(width: 80, height: 12)
                        .cornerRadius(4)
                }

                Spacer()

                // Amount skeleton
                Rectangle()
                    .fill(Color.theme.neutral200)
                    .frame(width: 60, height: 16)
                    .cornerRadius(4)
            }
        }
        .opacity(isAnimating ? 0.6 : 1.0)
        .animation(Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isAnimating)
        .onAppear {
            isAnimating = true
        }
    }
}

#Preview {
    DashboardView()
        .environmentObject(AuthViewModel.shared)
        .environmentObject(SecurityViewModel())
        .environmentObject(NotificationService.shared)
}
