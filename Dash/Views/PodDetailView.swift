import SwiftUI

struct PodDetailView: View {
    let pod: Pod
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedTab = 0
    @State private var isShowingAddTransaction = false
    @State private var isShowingInviteMembers = false
    @State private var isShowingPaymentRequest = false
    @State private var selectedMemberForPayment: PodMember?
    @State private var isSettling = false
    @State private var isShowingEditPod = false
    @State private var isShowingPodSettings = false
    @State private var isShowingArchiveConfirmation = false
    
    private var currentUserId: String {
        return authViewModel.currentUser?.uid ?? ""
    }

    // Get the latest pod data from the view model
    private var currentPod: Pod {
        return podViewModel.pods.first { $0.id == pod.id } ?? pod
    }

    private var isCurrentUserAdmin: Bool {
        return currentPod.isAdmin(userId: currentUserId)
    }

    private var userBalance: Double {
        return currentPod.memberBalance(for: currentUserId)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Navigation Header
                    HStack {
                        Button("Close") {
                            dismiss()
                        }
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.primary)

                        Spacer()

                        if isCurrentUserAdmin {
                            Menu {
                                Button("Edit Pod", action: {
                                    isShowingEditPod = true
                                })

                                Button("Pod Settings", action: {
                                    isShowingPodSettings = true
                                })

                                Divider()

                                Button("Archive Pod", role: .destructive, action: {
                                    isShowingArchiveConfirmation = true
                                })
                            } label: {
                                Image(systemName: "ellipsis")
                                    .font(.system(size: 18, weight: .medium))
                                    .foregroundColor(Color.theme.primary)
                            }
                        }
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.sm)

                    // Header Card
                    ModernCard(padding: Spacing.lg) {
                        VStack(spacing: Spacing.md) {
                            // Pod Info
                            HStack {
                                VStack(alignment: .leading, spacing: Spacing.sm) {
                                    Text(currentPod.name)
                                        .font(Font.theme.titleLarge)
                                        .fontWeight(.bold)
                                        .foregroundColor(Color.theme.textPrimary)

                                    if let description = currentPod.description {
                                        Text(description)
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }

                                    HStack(spacing: Spacing.sm) {
                                        Label("\(currentPod.activeMemberCount) members", systemImage: "person.2.fill")
                                            .font(Font.theme.labelMedium)
                                            .foregroundColor(Color.theme.textSecondary)

                                        if currentPod.totalExpenses > 0 {
                                            Label("$\(String(format: "%.2f", currentPod.totalExpenses))", systemImage: "dollarsign.circle.fill")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }
                                    }
                                }
                                
                                Spacer()
                                
                                // Pod Avatar
                                ZStack {
                                    Circle()
                                        .fill(Color.theme.info.opacity(0.1))
                                        .frame(width: 60, height: 60)
                                    
                                    Text(String(currentPod.name.prefix(2)).uppercased())
                                        .font(Font.theme.titleMedium)
                                        .fontWeight(.bold)
                                        .foregroundColor(Color.theme.info)
                                }
                            }
                            
                            // Balance Info
                            if userBalance != 0 {
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text("Your Balance")
                                            .font(Font.theme.labelMedium)
                                            .foregroundColor(Color.theme.textSecondary)

                                        Text(userBalance > 0 ? "You owe $\(String(format: "%.2f", userBalance))" : "You're owed $\(String(format: "%.2f", abs(userBalance)))")
                                            .font(Font.theme.titleMedium)
                                            .fontWeight(.semibold)
                                            .foregroundColor(userBalance > 0 ? Color.theme.warning : Color.theme.success)
                                    }

                                    Spacer()
                                }
                                .padding(.top, Spacing.sm)
                            }
                        }
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.md)
                    
                    // Action Buttons
                    VStack(spacing: Spacing.md) {
                        HStack(spacing: Spacing.md) {
                            ModernButton("Add Expense", icon: "plus", style: .primary, size: .medium) {
                                isShowingAddTransaction = true
                            }
                            .frame(maxWidth: .infinity)

                            if userBalance > 0 {
                                ModernButton(
                                    isSettling ? "Settling..." : "Settle Up",
                                    icon: "dollarsign.circle",
                                    style: .secondary,
                                    size: .medium,
                                    isLoading: isSettling
                                ) {
                                    settleUp()
                                }
                                .disabled(isSettling)
                                .frame(maxWidth: .infinity)
                            } else if isCurrentUserAdmin {
                                ModernButton("Invite Members", icon: "person.badge.plus", style: .tertiary, size: .medium) {
                                    isShowingInviteMembers = true
                                }
                                .frame(maxWidth: .infinity)
                            }
                        }

                        // Always show invite button for admins when they have a balance to settle
                        if isCurrentUserAdmin && userBalance > 0 {
                            ModernButton("Invite Members", icon: "person.badge.plus", style: .tertiary, size: .medium) {
                                isShowingInviteMembers = true
                            }
                        }
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.md)
                    
                    // Tab Selector
                    Picker("Tab", selection: $selectedTab) {
                        Text("Activity").tag(0)
                        Text("Members").tag(1)
                        Text("Balances").tag(2)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.md)
                    
                    // Tab Content
                    TabView(selection: $selectedTab) {
                        // Activity Tab
                        PodActivityView(pod: currentPod)
                            .tag(0)

                        // Members Tab
                        PodMembersView(pod: currentPod, isCurrentUserAdmin: isCurrentUserAdmin)
                            .tag(1)

                        // Balances Tab
                        PodBalancesView(pod: currentPod, onRequestPayment: { member in
                            selectedMemberForPayment = member
                            isShowingPaymentRequest = true
                        })
                        .tag(2)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                }
            }
            .navigationTitle("Pod Details")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $isShowingAddTransaction) {
                AddPodTransactionView(pod: pod)
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .sheet(isPresented: $isShowingInviteMembers) {
                InviteMembersView(pod: pod)
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .sheet(isPresented: $isShowingPaymentRequest, onDismiss: {
                selectedMemberForPayment = nil
            }) {
                CreatePaymentRequestView(
                    pod: currentPod,
                    preselectedMember: selectedMemberForPayment,
                    prefilledAmount: selectedMemberForPayment?.balance
                )
                .environmentObject(authViewModel)
                .environmentObject(podViewModel)
            }
            .sheet(isPresented: $isShowingEditPod) {
                EditPodView(pod: currentPod)
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .sheet(isPresented: $isShowingPodSettings) {
                PodSettingsView(pod: currentPod)
                    .environmentObject(authViewModel)
                    .environmentObject(podViewModel)
            }
            .onAppear {
                // Fetch pod transactions and recalculate balances when view appears
                if let podId = pod.id {
                    podViewModel.fetchPodTransactions(podId: podId)

                    // Force recalculation of balances
                    Task {
                        await podViewModel.calculateMemberBalances(for: podId)
                    }
                }
            }
            .alert("Success", isPresented: .constant(podViewModel.successMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.successMessage ?? "")
            }
            .alert("Archive Pod", isPresented: $isShowingArchiveConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Archive", role: .destructive) {
                    archivePod()
                }
            } message: {
                Text("Are you sure you want to archive this pod? This action cannot be undone.")
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
        }
    }

    // MARK: - Methods

    private func settleUp() {
        print("🟡 UI_DEBUG: settleUp() called")

        guard let podId = pod.id,
              let userId = authViewModel.currentUser?.uid else {
            print("🔴 UI_DEBUG: Missing podId or userId")
            return
        }

        let userBalance = currentPod.memberBalance(for: userId)
        print("🟡 UI_DEBUG: User balance: \(userBalance)")

        guard userBalance > 0 else {
            print("🔴 UI_DEBUG: User balance is not positive, cannot settle")
            return
        }

        print("🟡 UI_DEBUG: Starting settlement process...")
        isSettling = true

        Task {
            let success = await podViewModel.settleUserBalance(
                podId: podId,
                payerId: userId,
                amount: userBalance
            )

            await MainActor.run {
                isSettling = false
                print("🟡 UI_DEBUG: Settlement completed with success: \(success)")
                if success {
                    // Refresh pod data
                    podViewModel.fetchUserPods(userId: userId)
                }
            }
        }
    }

    private func archivePod() {
        guard let podId = pod.id else { return }

        Task {
            let success = await podViewModel.deletePod(podId: podId)

            await MainActor.run {
                if success {
                    // Refresh pods list and dismiss view
                    if let userId = authViewModel.currentUser?.uid {
                        podViewModel.fetchUserPods(userId: userId)
                    }
                    dismiss()
                }
            }
        }
    }
}

// MARK: - Pod Activity View
struct PodActivityView: View {
    let pod: Pod
    @EnvironmentObject var podViewModel: PodViewModel
    
    var body: some View {
        ScrollView(showsIndicators: false) {
            LazyVStack(spacing: Spacing.md) {
                if podViewModel.podTransactions.isEmpty {
                    // Empty State
                    VStack(spacing: Spacing.lg) {
                        Image(systemName: "list.bullet.rectangle")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(Color.theme.textTertiary)
                        
                        Text("No Activity Yet")
                            .font(Font.theme.titleMedium)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        Text("Transactions and payments will appear here")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, Spacing.xl)
                } else {
                    ForEach(podViewModel.podTransactions) { transaction in
                        PodTransactionRowView(transaction: transaction)
                            .padding(.horizontal, Spacing.lg)
                    }
                }
                
                // Bottom spacing
                Color.clear.frame(height: Spacing.xl)
            }
            .padding(.top, Spacing.md)
        }
    }
}

// MARK: - Pod Members View
struct PodMembersView: View {
    let pod: Pod
    let isCurrentUserAdmin: Bool
    
    var body: some View {
        ScrollView(showsIndicators: false) {
            LazyVStack(spacing: Spacing.md) {
                ForEach(pod.members.filter { $0.isActive }) { member in
                    PodMemberRowView(member: member, isCurrentUserAdmin: isCurrentUserAdmin)
                        .padding(.horizontal, Spacing.lg)
                }
                
                // Bottom spacing
                Color.clear.frame(height: Spacing.xl)
            }
            .padding(.top, Spacing.md)
        }
    }
}

// MARK: - Pod Balances View
struct PodBalancesView: View {
    let pod: Pod
    let onRequestPayment: (PodMember) -> Void

    var body: some View {
        ScrollView(showsIndicators: false) {
            LazyVStack(spacing: Spacing.md) {
                ForEach(pod.members.filter { $0.isActive && $0.balance != 0 }) { member in
                    ModernCard(padding: Spacing.lg) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(member.displayName)
                                    .font(Font.theme.bodyMedium)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)

                                Text(member.balance > 0 ? "Owes" : "Is owed")
                                    .font(Font.theme.labelMedium)
                                    .foregroundColor(Color.theme.textSecondary)
                            }

                            Spacer()

                            VStack(alignment: .trailing, spacing: 8) {
                                Text("$\(String(format: "%.2f", abs(member.balance)))")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.bold)
                                    .foregroundColor(member.balance > 0 ? Color.theme.warning : Color.theme.success)

                                // Add Request Pay button for users who owe money
                                if member.balance > 0 {
                                    ModernButton("Request Pay", icon: "paperplane", style: .secondary, size: .small) {
                                        onRequestPayment(member)
                                    }
                                }
                            }
                        }
                    }
                    .padding(.horizontal, Spacing.lg)
                }
                
                if pod.members.filter({ $0.isActive && $0.balance != 0 }).isEmpty {
                    VStack(spacing: Spacing.lg) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 32, weight: .medium))
                            .foregroundColor(Color.theme.success)
                        
                        Text("All Settled Up!")
                            .font(Font.theme.titleMedium)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        Text("Everyone's balances are even")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                    .padding(.top, Spacing.xl)
                }
                
                // Bottom spacing
                Color.clear.frame(height: Spacing.xl)
            }
            .padding(.top, Spacing.md)
        }
    }
}

// MARK: - Pod Transaction Row View
struct PodTransactionRowView: View {
    let transaction: PodTransaction

    var body: some View {
        ModernCard(padding: Spacing.lg) {
            HStack(spacing: Spacing.md) {
                // Icon
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 40, height: 40)

                    Image(systemName: transaction.type.icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(iconColor)
                }

                // Transaction Details
                VStack(alignment: .leading, spacing: 4) {
                    Text(transaction.description)
                        .font(Font.theme.bodyMedium)
                        .fontWeight(.medium)
                        .foregroundColor(Color.theme.textPrimary)

                    HStack {
                        Text("Paid by \(transaction.paidByName)")
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)

                        Text("•")
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textTertiary)

                        Text(transaction.date.formatted(date: .abbreviated, time: .omitted))
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }

                Spacer()

                // Amount
                VStack(alignment: .trailing, spacing: 2) {
                    Text("$\(String(format: "%.2f", transaction.amount))")
                        .font(Font.theme.titleSmall)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)

                    if !transaction.isSettled {
                        Text("Pending")
                            .font(Font.theme.labelSmall)
                            .foregroundColor(Color.theme.warning)
                    }
                }
            }
        }
    }

    private var iconColor: Color {
        switch transaction.type {
        case .expense:
            return Color.theme.warning
        case .payment:
            return Color.theme.success
        case .settlement:
            return Color.theme.info
        }
    }
}

// MARK: - Pod Member Row View
struct PodMemberRowView: View {
    let member: PodMember
    let isCurrentUserAdmin: Bool

    var body: some View {
        ModernCard(padding: Spacing.lg) {
            HStack(spacing: Spacing.md) {
                // Avatar
                ZStack {
                    Circle()
                        .fill(Color.theme.primary.opacity(0.1))
                        .frame(width: 40, height: 40)

                    Text(member.displayName.prefix(1).uppercased())
                        .font(Font.theme.labelLarge)
                        .fontWeight(.bold)
                        .foregroundColor(Color.theme.primary)
                }

                // Member Details
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(member.displayName)
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)

                        if member.role == .admin {
                            Text("ADMIN")
                                .font(Font.theme.labelSmall)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.info)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.theme.info.opacity(0.1))
                                .cornerRadius(4)
                        }
                    }

                    if let email = member.email {
                        Text(email)
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }

                Spacer()

                // Balance
                if member.balance != 0 {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("$\(String(format: "%.2f", abs(member.balance)))")
                            .font(Font.theme.titleSmall)
                            .fontWeight(.semibold)
                            .foregroundColor(member.balance > 0 ? Color.theme.warning : Color.theme.success)

                        Text(member.balance > 0 ? "owes" : "owed")
                            .font(Font.theme.labelSmall)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }
            }
        }
    }
}

#Preview {
    PodDetailView(pod: Pod(name: "Weekend Trip", description: "Our amazing weekend getaway", createdBy: "user1", createdByName: "John Doe"))
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
