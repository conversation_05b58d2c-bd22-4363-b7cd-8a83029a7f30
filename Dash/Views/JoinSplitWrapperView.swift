import SwiftUI

struct JoinSplitWrapperView: View {
    let splitId: String
    @StateObject private var viewModel = SplitViewModel.shared
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var isLoading = true
    @State private var showingSplitStatus = false

    var body: some View {
        VStack {
            if showingSplitStatus {
                SplitStatusView()
                    .environmentObject(authViewModel)
            } else if let split = viewModel.split {
                JoinSplitView(split: split, onJoinSuccess: {
                    print("JoinSplitWrapperView: User successfully joined split, showing status")
                    showingSplitStatus = true
                })
                .environmentObject(authViewModel)
                .onAppear {
                    print("JoinSplitWrapperView: Showing JoinSplitView with split: \(split.id ?? "N/A")")
                    // Ensure loading state is turned off when split is shown
                    if isLoading {
                        isLoading = false
                    }
                }
            } else if viewModel.errorMessage != nil {
                VStack(spacing: 20) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.orange)
                    
                    Text("Unable to Load Split")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text(viewModel.errorMessage ?? "Unknown error")
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                    
                    HStack(spacing: 15) {
                        Button("Try Again") {
                            // Reset error state and try again
                            viewModel.errorMessage = nil
                            isLoading = true
                            fetchSplitData()
                        }
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                        
                        Button("Dismiss") {
                            presentationMode.wrappedValue.dismiss()
                        }
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                    }
                }
                .padding()
                .onAppear {
                    print("JoinSplitWrapperView: Showing error: \(viewModel.errorMessage ?? "Unknown")")
                }
            } else if isLoading {
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    
                    Text("Loading Split Details...")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Please wait while we fetch the split information")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .onAppear {
                    print("JoinSplitWrapperView: Showing loading state")
                }
            }
        }
        .onAppear {
            print("JoinSplitWrapperView: onAppear called with splitId: \(splitId)")
            
            guard !splitId.isEmpty else {
                print("JoinSplitWrapperView: Split ID is empty")
                viewModel.errorMessage = "Scanned QR code is invalid."
                isLoading = false
                return
            }
            
            // Check if we already have the split data for this ID
            if let existingSplit = viewModel.split, existingSplit.id == splitId {
                print("JoinSplitWrapperView: Split data already available for ID: \(splitId)")
                isLoading = false
                return
            }
            
            // Only reset if we're loading a different split
            if viewModel.split?.id != splitId {
                print("JoinSplitWrapperView: Loading different split, resetting state")
                viewModel.reset()
            }
            
            isLoading = true
            print("JoinSplitWrapperView: Starting to fetch split data")
            fetchSplitData()
        }
        .onChange(of: viewModel.split) { split in
            if split != nil {
                print("JoinSplitWrapperView: Split data received, turning off loading")
                isLoading = false
            }
        }
        .onChange(of: viewModel.errorMessage) { errorMessage in
            if errorMessage != nil {
                print("JoinSplitWrapperView: Error received, turning off loading")
                isLoading = false
            }
        }
        .onDisappear {
            print("JoinSplitWrapperView: onDisappear called, hasJoinedSplit: \(viewModel.hasJoinedSplit)")
            // Only stop listening if the user hasn't joined the split
            // If they joined, the listener should continue for real-time updates
            if !viewModel.hasJoinedSplit {
                viewModel.stopListening()
            }
        }
    }
    
    private func fetchSplitData() {
        print("JoinSplitWrapperView: fetchSplitData called for splitId: \(splitId)")
        
        // Set up a timeout to handle slow network requests
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
            if self.isLoading && self.viewModel.split == nil && self.viewModel.errorMessage == nil {
                print("JoinSplitWrapperView: Request timed out after 10 seconds")
                self.viewModel.errorMessage = "Request timed out. Please check your internet connection and try again."
                self.isLoading = false
            }
        }
        
        // Use the listener approach but with immediate callback for existing data
        print("JoinSplitWrapperView: Setting up listener for immediate data fetch")
        viewModel.listenToSplit(splitId: splitId)
        
        // Also try direct fetch as backup
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            if self.isLoading && self.viewModel.split == nil && self.viewModel.errorMessage == nil {
                print("JoinSplitWrapperView: Listener didn't provide data quickly, trying direct fetch")
                self.viewModel.fetchSplit(splitId: self.splitId) { result in
                    DispatchQueue.main.async {
                        guard self.isLoading else { return }
                        
                        switch result {
                        case .success(let split):
                            print("JoinSplitWrapperView: Direct fetch successful")
                            self.viewModel.split = split
                            self.isLoading = false
                        case .failure(let error):
                            print("JoinSplitWrapperView: Direct fetch failed: \(error.localizedDescription)")
                            if self.viewModel.errorMessage == nil {
                                self.viewModel.errorMessage = error.localizedDescription
                                self.isLoading = false
                            }
                        }
                    }
                }
            }
        }
    }
}
