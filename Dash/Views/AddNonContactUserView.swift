import SwiftUI

struct AddNonContactUserView: View {
    @ObservedObject var viewModel: SendMoneyViewModel
    @Binding var selectedUser: User?
    @Binding var isPresented: Bool
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                headerSection
                searchSection

                if let foundUser = viewModel.foundUser {
                    foundUserSection(foundUser)
                }

                Spacer()
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarHidden(true)
            .background(Color(.systemGroupedBackground))
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    cancelButton
                }
            }
        }
    }

    private var headerSection: some View {
        VStack(spacing: 12) {
            Text("Add User")
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.primary)

            Text("Search by email or phone number")
                .font(.system(size: 15, weight: .regular))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 32)
    }

    private var searchSection: some View {
        VStack(spacing: 20) {
            VStack(spacing: 12) {
                emailTextField
                searchButton
            }
        }
        .padding(.horizontal, 24)
    }

    private var emailTextField: some View {
        TextField("Email or phone number", text: $viewModel.manualEmail)
            .font(.system(size: 16, weight: .regular))
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(textFieldBackground)
            .overlay(textFieldBorder)
    }

    private var textFieldBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color(.systemGray6))
    }

    private var textFieldBorder: some View {
        RoundedRectangle(cornerRadius: 12)
            .stroke(viewModel.manualEmail.isEmpty ? Color.clear : Color.green.opacity(0.3), lineWidth: 1)
    }

    private var searchButton: some View {
        Button(action: {
            viewModel.searchUserByEmailOrPhone()
        }) {
            searchButtonContent
        }
        .disabled(viewModel.manualEmail.isEmpty || viewModel.isSearchingManualUser)
    }

    private var searchButtonContent: some View {
        HStack(spacing: 8) {
            if viewModel.isSearchingManualUser {
                ProgressView()
                    .scaleEffect(0.9)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            } else {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 14, weight: .medium))
                Text("Search")
                    .font(.system(size: 16, weight: .medium))
            }
        }
        .foregroundColor(.white)
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(searchButtonBackground)
    }

    private var searchButtonBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(viewModel.manualEmail.isEmpty ? Color.gray.opacity(0.6) : Color.green)
    }

    private func foundUserSection(_ foundUser: User) -> some View {
        VStack(spacing: 16) {
            foundUserRow(foundUser)
            selectUserButton
        }
        .padding(.horizontal, 24)
    }

    private func foundUserRow(_ foundUser: User) -> some View {
        HStack(spacing: 16) {
            // Avatar
            ZStack {
                Circle()
                    .fill(Color.green)
                    .frame(width: 52, height: 52)

                Text(String(foundUser.displayName?.first ?? foundUser.email?.first ?? "?").uppercased())
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(foundUser.displayName ?? "No Name")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)

                Text(foundUser.email ?? "No Email")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.secondary)
            }

            Spacer()

            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 20, weight: .medium))
                .foregroundColor(.green)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(foundUserRowBackground)
    }

    private var foundUserRowBackground: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color.green.opacity(0.05))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.green.opacity(0.2), lineWidth: 1)
            )
    }

    private var selectUserButton: some View {
        Button(action: {
            viewModel.selectFoundUser()
            selectedUser = viewModel.selectedUser
            isPresented = false
        }) {
            Text("Select User")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.green)
                )
        }
    }

    private var cancelButton: some View {
        Button(action: {
            viewModel.clearFoundUser()
            isPresented = false
        }) {
            HStack(spacing: 6) {
                Image(systemName: "xmark")
                    .font(.system(size: 14, weight: .medium))
                Text("Cancel")
                    .font(.system(size: 16, weight: .medium))
            }
            .foregroundColor(.secondary)
        }
    }
}

#Preview {
    AddNonContactUserView(
        viewModel: SendMoneyViewModel(),
        selectedUser: .constant(nil),
        isPresented: .constant(true)
    )
}
