import SwiftUI
import FirebaseFirestore

struct TwoFactorManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var showingBackupCodes = false
    @State private var showingDisableConfirmation = false
    @State private var isDisabling = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 16) {
                    Image(systemName: "shield.checkered")
                        .font(.system(size: 60))
                        .foregroundColor(.green)
                    
                    Text("Two-Factor Authentication")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Your account is protected with two-factor authentication.")
                        .multilineTextAlignment(.center)
                        .foregroundColor(.secondary)
                    
                    if let setupDate = authViewModel.currentUser?.twoFactorSetupDate {
                        Text("Enabled on \(setupDate, style: .date)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                // Management Options
                VStack(spacing: 16) {
                    Button(action: {
                        showingBackupCodes = true
                    }) {
                        HStack {
                            Image(systemName: "doc.text")
                                .frame(width: 24)
                            Text("View Backup Codes")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button(action: {
                        generateNewBackupCodes()
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                                .frame(width: 24)
                            Text("Generate New Backup Codes")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                Spacer()
                
                // Disable 2FA Section
                VStack(spacing: 16) {
                    Text("Disable Two-Factor Authentication")
                        .font(.headline)
                        .foregroundColor(.red)
                    
                    Text("Disabling 2FA will make your account less secure. Only disable if you're having trouble accessing your account.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    if !errorMessage.isEmpty {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                    
                    Button(action: {
                        showingDisableConfirmation = true
                    }) {
                        HStack {
                            if isDisabling {
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                            Text(isDisabling ? "Disabling..." : "Disable Two-Factor Authentication")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .cornerRadius(16)
                    }
                    .disabled(isDisabling)
                }
                .padding()
                .background(Color.red.opacity(0.1))
                .cornerRadius(16)
            }
            .padding()
            .navigationTitle("Manage 2FA")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingBackupCodes) {
                BackupCodesView()
            }
            .alert("Disable Two-Factor Authentication", isPresented: $showingDisableConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Disable", role: .destructive) {
                    disableTwoFactorAuthentication()
                }
            } message: {
                Text("Are you sure you want to disable two-factor authentication? This will make your account less secure.")
            }
        }
    }
    
    private func generateNewBackupCodes() {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        let newCodes = TOTPService.shared.generateBackupCodes()
        _ = TOTPService.shared.saveBackupCodes(newCodes, for: userId)
        
        SecurityAuditService.shared.logSecurityEvent(.securitySettingsChanged, for: userId, metadata: [
            "action": "backup_codes_generated",
            "codes_count": newCodes.count
        ])
        
        showingBackupCodes = true
    }
    
    private func disableTwoFactorAuthentication() {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        isDisabling = true
        errorMessage = ""
        
        // Remove TOTP secret from keychain
        switch TOTPService.shared.removeSecret(for: userId) {
        case .success:
            // Update user's 2FA status in Firestore
            let db = Firestore.firestore()
            db.collection("users").document(userId).updateData([
                "twoFactorEnabled": false,
                "twoFactorSetupDate": FieldValue.delete(),
                "requireTwoFactorForTransactions": false
            ]) { error in
                DispatchQueue.main.async {
                    if let error = error {
                        self.errorMessage = "Failed to disable 2FA: \(error.localizedDescription)"
                        self.isDisabling = false
                        print("DEBUG: Failed to update twoFactorEnabled in Firebase: \(error.localizedDescription)")
                    } else {
                        print("DEBUG: Successfully disabled 2FA in Firebase for user: \(userId)")
                        
                        // Remove backup codes
                        _ = TOTPService.shared.removeBackupCodes(for: userId)
                        
                        // Update local user state
                        self.authViewModel.updateLocalTwoFactorState(enabled: false)
                        
                        // Refresh user data to ensure UI reflects the change
                        self.authViewModel.refreshCurrentUser()
                        
                        // Log security event
                        SecurityAuditService.shared.logSecurityEvent(.twoFactorDisabled, for: userId)
                        
                        // Send notification
                        NotificationService.shared.notify2FADisabled()
                        
                        self.isDisabling = false
                        self.dismiss()
                    }
                }
            }
        case .failure(let error):
            DispatchQueue.main.async {
                self.errorMessage = "Failed to remove authentication secret: \(error.localizedDescription)"
                self.isDisabling = false
                print("DEBUG: Failed to remove TOTP secret from keychain: \(error.localizedDescription)")
            }
        }
    }
}

struct BackupCodesView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var backupCodes: [String] = []
    @State private var isLoading = true
    @State private var codesCopied = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if isLoading {
                    ProgressView("Loading backup codes...")
                } else if backupCodes.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)
                        
                        Text("No Backup Codes Found")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("You don't have any backup codes. Generate new ones to ensure you can access your account if you lose your authenticator device.")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                    }
                } else {
                    VStack(spacing: 16) {
                        Text("Backup Codes")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("Save these codes in a secure location. Each code can only be used once.")
                            .multilineTextAlignment(.center)
                            .foregroundColor(.secondary)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                            ForEach(backupCodes, id: \.self) { code in
                                Text(code)
                                    .font(.system(.body, design: .monospaced))
                                    .padding()
                                    .background(Color(.systemGray6))
                                    .cornerRadius(8)
                            }
                        }
                        
                        Button(action: copyToClipboard) {
                            HStack {
                                Image(systemName: codesCopied ? "checkmark" : "doc.on.doc")
                                Text(codesCopied ? "Copied!" : "Copy All Codes")
                            }
                            .foregroundColor(codesCopied ? .green : .blue)
                        }
                        .padding(.top)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Backup Codes")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear(perform: loadBackupCodes)
    }
    
    private func loadBackupCodes() {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        switch TOTPService.shared.loadBackupCodes(for: userId) {
        case .success(let codes):
            backupCodes = codes
        case .failure:
            backupCodes = []
        }
        isLoading = false
    }
    
    private func copyToClipboard() {
        let codesText = backupCodes.joined(separator: "\n")
        UIPasteboard.general.string = codesText
        codesCopied = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            codesCopied = false
        }
    }
}

#Preview {
    TwoFactorManagementView()
        .environmentObject(AuthViewModel.shared)
}
