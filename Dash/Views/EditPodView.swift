import SwiftUI

struct EditPodView: View {
    let pod: Pod
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var podName: String
    @State private var podDescription: String
    @State private var isPrivate: Bool
    @State private var maxMembers: Int
    @State private var isUpdating = false
    
    init(pod: Pod) {
        self.pod = pod
        self._podName = State(initialValue: pod.name)
        self._podDescription = State(initialValue: pod.description ?? "")
        self._isPrivate = State(initialValue: pod.isPrivate)
        self._maxMembers = State(initialValue: pod.maxMembers)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: Spacing.lg) {
                        // Pod Name
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Pod Name")
                                .font(Font.theme.labelLarge)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernTextField("Pod Name", text: $podName, placeholder: "Enter pod name")
                        }
                        
                        // Pod Description
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Description (Optional)")
                                .font(Font.theme.labelLarge)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernTextField("Description", text: $podDescription, placeholder: "Enter pod description")
                        }
                        
                        // Privacy Setting
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Privacy")
                                .font(Font.theme.labelLarge)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Toggle("Private Pod", isOn: $isPrivate)
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textPrimary)
                        }
                        
                        // Max Members
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Maximum Members")
                                .font(Font.theme.labelLarge)
                                .fontWeight(.medium)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Stepper("\(maxMembers) members", value: $maxMembers, in: 2...100)
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textPrimary)
                        }
                        
                        // Update Button
                        ModernButton(
                            isUpdating ? "Updating..." : "Update Pod",
                            icon: "checkmark",
                            style: .primary,
                            size: .medium,
                            isLoading: isUpdating,
                            isDisabled: !isFormValid
                        ) {
                            updatePod()
                        }
                        .padding(.top, Spacing.lg)
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.md)
                }
            }
            .navigationTitle("Edit Pod")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
        }
    }
    
    private var isFormValid: Bool {
        !podName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private func updatePod() {
        guard let podId = pod.id else { return }

        isUpdating = true

        // Since Pod has immutable properties, we need to update via Firestore directly
        let updatedData: [String: Any] = [
            "name": podName.trimmingCharacters(in: .whitespacesAndNewlines),
            "description": podDescription.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? NSNull() : podDescription.trimmingCharacters(in: .whitespacesAndNewlines),
            "isPrivate": isPrivate,
            "maxMembers": maxMembers,
            "updatedAt": Date()
        ]

        Task {
            let success = await podViewModel.updatePodFields(podId: podId, fields: updatedData)

            await MainActor.run {
                isUpdating = false
                if success {
                    // Refresh pods list
                    if let userId = authViewModel.currentUser?.uid {
                        podViewModel.fetchUserPods(userId: userId)
                    }
                    dismiss()
                }
            }
        }
    }
}

#Preview {
    EditPodView(pod: Pod(name: "Test Pod", description: "Test Description", createdBy: "user1", createdByName: "John Doe"))
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
