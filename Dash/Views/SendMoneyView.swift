import SwiftUI

struct SendMoneyView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var viewModel = SendMoneyViewModel()
    
    @State private var amount: String = "0"
    @State private var selectedUser: User?
    @State private var searchText: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var successMessage: String?
    @State private var step: Step = .enterAmount
    @State private var showingAddNonContactSheet = false
    
    enum Step {
        case enterAmount
        case selectUser
    }
    
    var amountValue: Double {
        (Double(amount) ?? 0) / 100.0
    }
    
    var body: some View {
        ZStack {
            Color.theme.background.ignoresSafeArea()

            VStack(spacing: 0) {
                // Modern Header
                ModernHeader(
                    title: step == .selectUser ? "Choose Recipient" : "Send Money",
                    onDismiss: {
                        if step == .selectUser {
                            withAnimation(Animations.medium) {
                                step = .enterAmount
                            }
                        } else {
                            presentationMode.wrappedValue.dismiss()
                        }
                    }
                )

                if let successMessage = successMessage {
                    successView(message: successMessage)
                } else {
                    switch step {
                    case .enterAmount:
                        amountEntryView
                    case .selectUser:
                        userSelectionView
                    }
                }

                if let errorMessage = errorMessage ?? viewModel.errorMessage {
                    errorView(message: errorMessage)
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            viewModel.loadContactUsers()
        }
        .sheet(isPresented: $showingAddNonContactSheet) {
            AddNonContactUserView(
                viewModel: viewModel,
                selectedUser: $selectedUser,
                isPresented: $showingAddNonContactSheet
            )
        }
    }
    
    private var amountEntryView: some View {
        VStack(spacing: 0) {
            ScrollView(showsIndicators: false) {
                VStack(spacing: Spacing.xxxl) {
                    // Amount Display Section
                    VStack(spacing: Spacing.lg) {
                        Text("Amount to send")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .padding(.top, Spacing.xxl)

                        // Large amount display
                        Text(amountValue.toCurrency())
                            .font(Font.theme.displaySmall)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                            .minimumScaleFactor(0.5)
                            .lineLimit(1)
                    }
                    .padding(.horizontal, Spacing.lg)

                    // Balance check card
                    if let currentUser = authViewModel.currentUser,
                       let balance = currentUser.balance {
                        ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                            HStack(spacing: Spacing.sm) {
                                Image(systemName: amountValue > balance ? "exclamationmark.triangle.fill" : "checkmark.circle.fill")
                                    .foregroundColor(amountValue > balance ? Color.theme.warning : Color.theme.success)
                                    .font(.system(size: 16, weight: .medium))

                                VStack(alignment: .leading, spacing: 2) {
                                    Text(amountValue > balance ? "Insufficient balance" : "Available balance")
                                        .font(Font.theme.bodySmall)
                                        .foregroundColor(amountValue > balance ? Color.theme.warning : Color.theme.success)

                                    Text(balance.toCurrency())
                                        .font(Font.theme.labelSmall)
                                        .foregroundColor(Color.theme.textSecondary)
                                }

                                Spacer()
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                    }

                    // Numeric Keypad
                    NumericKeypad(
                        onKeyPress: { digit in
                            if amount == "0" {
                                amount = digit
                            } else {
                                amount += digit
                            }
                        },
                        onDelete: {
                            amount = String(amount.dropLast())
                            if amount.isEmpty {
                                amount = "0"
                            }
                        }
                    )
                    .padding(.top, Spacing.xl)

                    // Bottom spacing for button
                    Color.clear.frame(height: 120)
                }
            }

            // Bottom Action Area
            VStack(spacing: Spacing.md) {
                ModernButton(
                    "Continue",
                    icon: "arrow.right.circle.fill",
                    style: .primary,
                    size: .large,
                    isDisabled: !canContinue
                ) {
                    withAnimation(Animations.medium) {
                        step = .selectUser
                    }
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.bottom, Spacing.lg)
            }
            .background(
                Color.theme.background
                    .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
            )
        }
    }
    
    private var userSelectionView: some View {
        VStack(spacing: 0) {
            VStack(spacing: Spacing.lg) {
                // Search section
                ModernTextField(
                    "Search",
                    text: $searchText,
                    placeholder: "Search contacts"
                )
                .padding(.horizontal, Spacing.lg)
                .padding(.top, Spacing.lg)

                // Add non-contact user option
                Button(action: {
                    showingAddNonContactSheet = true
                }) {
                    ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.md) {
                        HStack(spacing: Spacing.md) {
                            // Icon
                            ZStack {
                                Circle()
                                    .fill(Color.theme.primary)
                                    .frame(width: 44, height: 44)

                                Image(systemName: "plus")
                                    .font(.system(size: 18, weight: .semibold))
                                    .foregroundColor(.white)
                            }

                            VStack(alignment: .leading, spacing: 4) {
                                Text("Add Non-Contact")
                                    .font(Font.theme.bodyLarge)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)

                                Text("Search by email or phone")
                                    .font(Font.theme.bodySmall)
                                    .foregroundColor(Color.theme.textSecondary)
                            }

                            Spacer()

                            Image(systemName: "chevron.right")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(Color.theme.textTertiary)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, Spacing.lg)
            }


            // Users list
            if viewModel.isLoading {
                VStack(spacing: Spacing.lg) {
                    ProgressView()
                        .scaleEffect(1.2)

                    Text("Loading contacts...")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(.top, Spacing.xxxl)
            } else {
                ScrollView(showsIndicators: false) {
                    LazyVStack(spacing: Spacing.sm) {
                        if filteredUsers.isEmpty {
                            VStack(spacing: Spacing.md) {
                                Image(systemName: "person.2.slash")
                                    .font(.system(size: 48, weight: .light))
                                    .foregroundColor(Color.theme.textTertiary)

                                Text("No contacts found")
                                    .font(Font.theme.bodyLarge)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textSecondary)

                                Text("Try searching with a different term")
                                    .font(Font.theme.bodySmall)
                                    .foregroundColor(Color.theme.textTertiary)
                            }
                            .padding(.top, Spacing.xxxl)
                        } else {
                            ForEach(filteredUsers) { user in
                                userRow(user: user)
                            }
                        }

                        // Bottom spacing for button
                        Color.clear.frame(height: 120)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.lg)
                }
            }

            // Bottom Action Area
            VStack(spacing: Spacing.md) {
                if let selectedUser = selectedUser {
                    // Selected user summary
                    ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                        HStack(spacing: Spacing.md) {
                            // Avatar
                            ZStack {
                                Circle()
                                    .fill(Color.theme.primary.opacity(0.1))
                                    .frame(width: 40, height: 40)

                                Text(String((selectedUser.displayName ?? selectedUser.email ?? "U").prefix(1).uppercased()))
                                    .font(Font.theme.bodyMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.primary)
                            }

                            VStack(alignment: .leading, spacing: 2) {
                                Text("Sending to")
                                    .font(Font.theme.labelSmall)
                                    .foregroundColor(Color.theme.textSecondary)

                                Text(selectedUser.displayName ?? selectedUser.email ?? "Unknown")
                                    .font(Font.theme.bodySmall)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color.theme.textPrimary)
                            }

                            Spacer()

                            Text(amountValue.toCurrency())
                                .font(Font.theme.bodyLarge)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                        }
                    }
                    .padding(.horizontal, Spacing.lg)
                }

                // Send button
                ModernButton(
                    selectedUser != nil ? "Send \(amountValue.toCurrency())" : "Select a recipient",
                    icon: "paperplane.fill",
                    style: .primary,
                    size: .large,
                    isLoading: isLoading,
                    isDisabled: selectedUser == nil
                ) {
                    sendMoney()
                }
                .padding(.horizontal, Spacing.lg)
                .padding(.bottom, Spacing.lg)
            }
            .background(
                Color.theme.background
                    .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
            )
        }
    }
    
    private var filteredUsers: [User] {
        let users = viewModel.contactUsers.filter { user in
            // Exclude current user
            guard let currentUserUID = authViewModel.currentUser?.uid else { return true }
            return user.uid != currentUserUID
        }
        
        if searchText.isEmpty {
            return users
        } else {
            return users.filter { user in
                (user.displayName?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (user.email?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }
    }
    
    private func userRow(user: User) -> some View {
        let isSelected = selectedUser?.id == user.id

        return Button(action: {
            withAnimation(Animations.medium) {
                selectedUser = user
            }
        }) {
            ModernCard(
                padding: Spacing.lg,
                cornerRadius: BorderRadius.md
            ) {
                HStack(spacing: Spacing.md) {
                    // Selection indicator
                    ZStack {
                        Circle()
                            .stroke(
                                isSelected ? Color.theme.primary : Color.theme.textTertiary.opacity(0.3),
                                lineWidth: 2
                            )
                            .frame(width: 20, height: 20)

                        if isSelected {
                            Circle()
                                .fill(Color.theme.primary)
                                .frame(width: 12, height: 12)
                        }
                    }

                    // Avatar
                    ZStack {
                        Circle()
                            .fill(Color.theme.primary.opacity(0.1))
                            .frame(width: 44, height: 44)

                        Text(String((user.displayName ?? user.email ?? "U").prefix(1).uppercased()))
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.primary)
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(user.displayName ?? "No Name")
                            .font(Font.theme.bodyLarge)
                            .fontWeight(.medium)
                            .foregroundColor(Color.theme.textPrimary)

                        Text(user.email ?? "No Email")
                            .font(Font.theme.bodySmall)
                            .foregroundColor(Color.theme.textSecondary)
                    }

                    Spacer()
                }
            }
            .overlay(
                RoundedRectangle(cornerRadius: BorderRadius.md)
                    .stroke(
                        isSelected ? Color.theme.primary : Color.clear,
                        lineWidth: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }


    
    private func successView(message: String) -> some View {
        VStack(spacing: 24) {
            Spacer()
            
            ZStack {
                Circle()
                    .fill(Color.green.opacity(0.1))
                    .frame(width: 120, height: 120)
                
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
            }
            
            VStack(spacing: 8) {
                Text("Money Sent!")
                    .font(.title)
                    .foregroundColor(.primary)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
            
            Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Text("Done")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(Color.green)
                    .cornerRadius(16)
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 32)
        }
    }
    
    private func errorView(message: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.orange)
            
            Text(message)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
        .padding(.horizontal, 24)
        .padding(.bottom, 16)
    }
    
    private var canContinue: Bool {
        guard amountValue > 0 else { return false }
        
        if let currentUser = authViewModel.currentUser,
           let balance = currentUser.balance {
            return amountValue <= balance
        }
        
        return true
    }
    
    private func sendMoney() {
        guard let toUser = selectedUser else {
            errorMessage = "Please select a user to send to."
            return
        }
        
        guard let fromUser = authViewModel.currentUser else {
            errorMessage = "You must be logged in to send money."
            return
        }
        
        guard amountValue > 0 else {
            errorMessage = "Please enter an amount."
            return
        }
        
        guard let fromUserBalance = fromUser.balance, fromUserBalance >= amountValue else {
            errorMessage = "You do not have enough money to send."
            return
        }
        
        guard let toUserUID = toUser.uid else {
            errorMessage = "Recipient user ID not found."
            return
        }
        
        isLoading = true
        errorMessage = nil
        successMessage = nil
        
        Task {
            do {
                let transactionId = try await TransactionService.shared.sendMoney(
                    to: toUserUID,
                    amount: amountValue,
                    description: "Money transfer"
                )
                
                await MainActor.run {
                    self.isLoading = false
                    self.successMessage = "Successfully sent \(self.amountValue.toCurrency()) to \(toUser.displayName ?? toUser.email ?? "user")"
                    
                    // Update the current user's balance in the auth view model
                    if var currentUser = authViewModel.currentUser {
                        currentUser.balance = (currentUser.balance ?? 0) - self.amountValue
                        authViewModel.currentUser = currentUser
                    }
                    
                    // Refresh the user's data from Firestore to ensure balance is up to date
                    authViewModel.refreshCurrentUser()
                    
                    // Refresh transactions to ensure the new transaction appears in history
                    TransactionService.shared.refreshTransactions()
                    
                    // Reset form
                    self.amount = "0"
                    self.selectedUser = nil
                    self.searchText = ""
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = "Transaction failed: \(error.localizedDescription)"
                }
            }
        }
    }
}
