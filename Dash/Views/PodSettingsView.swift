import SwiftUI

struct PodSettingsView: View {
    let pod: Pod
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var isShowingDeleteConfirmation = false
    @State private var isShowingLeaveConfirmation = false
    
    private var isCurrentUserAdmin: Bool {
        guard let userId = authViewModel.currentUser?.uid else { return false }
        return pod.isAdmin(userId: userId)
    }
    
    private var isCurrentUserCreator: Bool {
        guard let userId = authViewModel.currentUser?.uid else { return false }
        return pod.createdBy == userId
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: Spacing.lg) {
                        // Pod Information Section
                        ModernCard(padding: Spacing.lg) {
                            VStack(alignment: .leading, spacing: Spacing.md) {
                                Text("Pod Information")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                VStack(alignment: .leading, spacing: Spacing.sm) {
                                    HStack {
                                        Text("Name:")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                        Spacer()
                                        Text(pod.name)
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                    }
                                    
                                    if let description = pod.description {
                                        HStack {
                                            Text("Description:")
                                                .font(Font.theme.bodyMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                            Spacer()
                                            Text(description)
                                                .font(Font.theme.bodyMedium)
                                                .foregroundColor(Color.theme.textPrimary)
                                                .multilineTextAlignment(.trailing)
                                        }
                                    }
                                    
                                    HStack {
                                        Text("Privacy:")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                        Spacer()
                                        Text(pod.isPrivate ? "Private" : "Public")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                    }
                                    
                                    HStack {
                                        Text("Members:")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                        Spacer()
                                        Text("\(pod.activeMemberCount) / \(pod.maxMembers)")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                    }
                                    
                                    HStack {
                                        Text("Created:")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                        Spacer()
                                        Text(pod.createdAt, style: .date)
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                    }
                                }
                            }
                        }
                        
                        // Actions Section
                        ModernCard(padding: Spacing.lg) {
                            VStack(alignment: .leading, spacing: Spacing.md) {
                                Text("Actions")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                VStack(spacing: Spacing.sm) {
                                    // Leave Pod (for non-creators)
                                    if !isCurrentUserCreator {
                                        ModernButton(
                                            "Leave Pod",
                                            icon: "arrow.right.square",
                                            style: .destructive,
                                            size: .medium
                                        ) {
                                            isShowingLeaveConfirmation = true
                                        }
                                    }
                                    
                                    // Delete Pod (for creators only)
                                    if isCurrentUserCreator {
                                        ModernButton(
                                            "Delete Pod",
                                            icon: "trash",
                                            style: .destructive,
                                            size: .medium
                                        ) {
                                            isShowingDeleteConfirmation = true
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.md)
                }
            }
            .navigationTitle("Pod Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .alert("Leave Pod", isPresented: $isShowingLeaveConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Leave", role: .destructive) {
                    leavePod()
                }
            } message: {
                Text("Are you sure you want to leave this pod? You will lose access to all pod data.")
            }
            .alert("Delete Pod", isPresented: $isShowingDeleteConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    deletePod()
                }
            } message: {
                Text("Are you sure you want to delete this pod? This action cannot be undone and all data will be lost.")
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
        }
    }
    
    private func leavePod() {
        guard let podId = pod.id,
              let userId = authViewModel.currentUser?.uid else { return }
        
        Task {
            let success = await podViewModel.removeMemberFromPod(podId: podId, userId: userId)
            
            await MainActor.run {
                if success {
                    // Refresh pods list and dismiss
                    podViewModel.fetchUserPods(userId: userId)
                    dismiss()
                }
            }
        }
    }
    
    private func deletePod() {
        guard let podId = pod.id else { return }
        
        Task {
            let success = await podViewModel.deletePod(podId: podId)
            
            await MainActor.run {
                if success {
                    // Refresh pods list and dismiss
                    if let userId = authViewModel.currentUser?.uid {
                        podViewModel.fetchUserPods(userId: userId)
                    }
                    dismiss()
                }
            }
        }
    }
}

#Preview {
    PodSettingsView(pod: Pod(name: "Test Pod", description: "Test Description", createdBy: "user1", createdByName: "John Doe"))
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
