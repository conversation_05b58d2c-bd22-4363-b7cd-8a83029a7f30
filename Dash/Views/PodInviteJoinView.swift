import SwiftUI
import FirebaseFirestore

struct PodInviteJoinView: View {
    let inviteCode: String
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var invitation: PodInvitation?
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var isJoining = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                if isLoading {
                    // Loading State
                    VStack(spacing: Spacing.lg) {
                        ProgressView()
                            .scaleEffect(1.2)
                        
                        Text("Loading invitation...")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                    
                } else if let error = errorMessage {
                    // Error State
                    VStack(spacing: Spacing.lg) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 48, weight: .medium))
                            .foregroundColor(Color.theme.error)
                        
                        Text("Invalid Invitation")
                            .font(Font.theme.titleLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        Text(error)
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                        
                        ModernButton("Close", style: .secondary) {
                            dismiss()
                        }
                        .padding(.horizontal, Spacing.lg)
                    }
                    .padding(.top, Spacing.xl)
                    
                } else if let invitation = invitation {
                    // Invitation Details
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: Spacing.xl) {
                            // Header
                            VStack(spacing: Spacing.md) {
                                ZStack {
                                    Circle()
                                        .fill(Color.theme.primary.opacity(0.1))
                                        .frame(width: 80, height: 80)
                                    
                                    Image(systemName: "person.2.badge.plus")
                                        .font(.system(size: 32, weight: .medium))
                                        .foregroundColor(Color.theme.primary)
                                }
                                
                                Text("Pod Invitation")
                                    .font(Font.theme.titleLarge)
                                    .fontWeight(.bold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                Text("You've been invited to join a pod!")
                                    .font(Font.theme.bodyMedium)
                                    .foregroundColor(Color.theme.textSecondary)
                            }
                            .padding(.top, Spacing.lg)
                            
                            // Invitation Details Card
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.lg) {
                                    // Pod Info
                                    VStack(spacing: Spacing.md) {
                                        HStack {
                                            Text("Pod Name")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                            
                                            Spacer()
                                        }
                                        
                                        HStack {
                                            ZStack {
                                                Circle()
                                                    .fill(Color.theme.info.opacity(0.1))
                                                    .frame(width: 40, height: 40)
                                                
                                                Text(String(invitation.podName.prefix(2)).uppercased())
                                                    .font(Font.theme.labelMedium)
                                                    .fontWeight(.bold)
                                                    .foregroundColor(Color.theme.info)
                                            }
                                            
                                            VStack(alignment: .leading, spacing: 2) {
                                                Text(invitation.podName)
                                                    .font(Font.theme.titleMedium)
                                                    .fontWeight(.semibold)
                                                    .foregroundColor(Color.theme.textPrimary)
                                                
                                                Text("Shared expense pod")
                                                    .font(Font.theme.labelMedium)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }
                                            
                                            Spacer()
                                        }
                                    }
                                    
                                    Divider()
                                    
                                    // Inviter Info
                                    VStack(spacing: Spacing.md) {
                                        HStack {
                                            Text("Invited by")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                            
                                            Spacer()
                                        }
                                        
                                        HStack {
                                            ZStack {
                                                Circle()
                                                    .fill(Color.theme.primary.opacity(0.1))
                                                    .frame(width: 32, height: 32)
                                                
                                                Text(String(invitation.invitedByName.prefix(1)).uppercased())
                                                    .font(Font.theme.labelSmall)
                                                    .fontWeight(.bold)
                                                    .foregroundColor(Color.theme.primary)
                                            }
                                            
                                            Text(invitation.invitedByName)
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Spacer()
                                        }
                                    }
                                    
                                    Divider()
                                    
                                    // Invitation Date
                                    HStack {
                                        Text("Invited")
                                            .font(Font.theme.labelMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                        
                                        Spacer()
                                        
                                        Text(invitation.createdAt.formatted(date: .abbreviated, time: .shortened))
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)
                            
                            // Action Buttons
                            VStack(spacing: Spacing.md) {
                                ModernButton(
                                    "Join Pod",
                                    icon: "checkmark.circle",
                                    style: .primary,
                                    isLoading: isJoining
                                ) {
                                    joinPod()
                                }
                                .padding(.horizontal, Spacing.lg)
                                
                                ModernButton("Decline", style: .secondary) {
                                    dismiss()
                                }
                                .padding(.horizontal, Spacing.lg)
                            }
                            
                            // Bottom spacing
                            Color.clear.frame(height: Spacing.xl)
                        }
                    }
                }
            }
            .navigationTitle("Pod Invitation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
        }
        .onAppear {
            loadInvitation()
        }
        .alert("Success", isPresented: .constant(podViewModel.successMessage != nil)) {
            Button("OK") {
                podViewModel.clearMessages()
                // Refresh pods list to include the newly joined pod
                if let userId = authViewModel.currentUser?.uid {
                    podViewModel.fetchUserPods(userId: userId)
                }
                dismiss()
            }
        } message: {
            Text(podViewModel.successMessage ?? "")
        }
        .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
            Button("OK") {
                podViewModel.clearMessages()
            }
        } message: {
            Text(podViewModel.errorMessage ?? "")
        }
    }
    
    private func loadInvitation() {
        isLoading = true
        errorMessage = nil

        print("🔵 INVITE_DEBUG: Loading invitation for code: \(inviteCode)")

        Task {
            do {
                // Query Firestore for the invitation with this code
                let db = Firestore.firestore()
                print("🔵 INVITE_DEBUG: Querying Firestore for inviteCode: \(inviteCode)")
                let querySnapshot = try await db.collection("podInvitations")
                    .whereField("inviteCode", isEqualTo: inviteCode)
                    .whereField("status", isEqualTo: "pending")
                    .limit(to: 1)
                    .getDocuments()

                print("🔵 INVITE_DEBUG: Query returned \(querySnapshot.documents.count) documents")

                await MainActor.run {
                    if let document = querySnapshot.documents.first {
                        print("🟢 INVITE_DEBUG: Found invitation document: \(document.documentID)")
                        print("🟢 INVITE_DEBUG: Document data: \(document.data())")
                        do {
                            self.invitation = try document.data(as: PodInvitation.self)
                            print("🟢 INVITE_DEBUG: Successfully parsed invitation")
                        } catch {
                            print("🔴 INVITE_DEBUG: Failed to parse invitation: \(error.localizedDescription)")
                            self.errorMessage = "Failed to parse invitation data"
                        }
                    } else {
                        print("🔴 INVITE_DEBUG: No invitation found for code: \(inviteCode)")
                        self.errorMessage = "Invitation not found or has expired"
                    }
                    self.isLoading = false
                }

            } catch {
                print("🔴 INVITE_DEBUG: Query failed: \(error.localizedDescription)")
                await MainActor.run {
                    self.errorMessage = "Failed to load invitation: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    private func joinPod() {
        guard let invitation = invitation,
              let invitationId = invitation.id,
              let currentUser = authViewModel.currentUser,
              let userId = currentUser.uid else {
            print("🔴 INVITE_DEBUG: Missing required data for joining pod")
            return
        }

        // Get user name with fallback options
        let userName = currentUser.displayName ?? currentUser.email ?? "Unknown User"

        print("🔵 INVITE_DEBUG: Starting pod join process")
        print("🔵 INVITE_DEBUG: User ID: \(userId)")
        print("🔵 INVITE_DEBUG: User Name: \(userName)")
        print("🔵 INVITE_DEBUG: Invitation ID: \(invitationId)")

        isJoining = true

        Task {
            let success = await podViewModel.respondToInvitation(
                invitationId: invitationId,
                response: .accepted,
                userId: userId,
                userName: userName
            )

            await MainActor.run {
                isJoining = false
                print("🔵 INVITE_DEBUG: Pod join result: \(success)")
                if success {
                    // Success message will be shown via alert
                    // Dismiss will happen in alert handler
                } else {
                    // Error message will be shown via alert
                }
            }
        }
    }
}

#Preview {
    PodInviteJoinView(inviteCode: "ABC123")
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
