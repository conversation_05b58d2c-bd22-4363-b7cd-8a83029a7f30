import SwiftUI
import PassKit

struct AddToWalletButton: View {
    let user: User
    @StateObject private var walletService = WalletPassService.shared
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isSuccess = false
    @State private var isProcessing = false
    
    var body: some View {
        Button(action: {
            guard !isProcessing else { return }
            isProcessing = true

            Task {
                await walletService.generateAndAddPass(for: user)
                await MainActor.run {
                    isProcessing = false
                }
            }
        }) {
            HStack(spacing: Spacing.sm) {
                if walletService.isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(.white)
                } else {
                    Image(systemName: "wallet.pass")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                
                Text(walletService.isLoading ? "Adding to Wallet..." : "Add to Wallet")
                    .font(Font.theme.bodyMedium)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: BorderRadius.md)
                    .fill(Color.black)
            )
        }
        .disabled(walletService.isLoading || !walletService.isWalletAvailable() || isProcessing)
        .opacity(walletService.isWalletAvailable() ? 1.0 : 0.6)
        .onChange(of: walletService.errorMessage) { errorMessage in
            if let error = errorMessage {
                alertMessage = error
                isSuccess = false
                showingAlert = true
            }
        }
        .onChange(of: walletService.successMessage) { successMessage in
            if let success = successMessage {
                alertMessage = success
                isSuccess = true
                showingAlert = true
            }
        }
        .alert(isSuccess ? "Success" : "Error", isPresented: $showingAlert) {
            Button("OK") {
                showingAlert = false
                // Clear messages after showing alert
                Task {
                    await MainActor.run {
                        walletService.errorMessage = nil
                        walletService.successMessage = nil
                    }
                }
            }
        } message: {
            Text(alertMessage)
        }
    }
}

// MARK: - PKAddPassButton Wrapper (Alternative Implementation)

struct PKAddPassButtonWrapper: UIViewRepresentable {
    let user: User
    @StateObject private var walletService = WalletPassService.shared
    
    func makeUIView(context: Context) -> PKAddPassButton {
        let button = PKAddPassButton(addPassButtonStyle: .black)
        button.addTarget(context.coordinator, action: #selector(Coordinator.addPassTapped), for: .touchUpInside)
        return button
    }
    
    func updateUIView(_ uiView: PKAddPassButton, context: Context) {
        // Update button state if needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(user: user, walletService: walletService)
    }
    
    class Coordinator: NSObject {
        let user: User
        let walletService: WalletPassService
        
        init(user: User, walletService: WalletPassService) {
            self.user = user
            self.walletService = walletService
        }
        
        @objc func addPassTapped() {
            Task {
                await walletService.generateAndAddPass(for: user)
            }
        }
    }
}

// MARK: - Preview

struct AddToWalletButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: Spacing.lg) {
            AddToWalletButton(user: User(
                uid: "preview-user",
                email: "<EMAIL>",
                displayName: "Preview User"
            ))
            
            PKAddPassButtonWrapper(user: User(
                uid: "preview-user",
                email: "<EMAIL>",
                displayName: "Preview User"
            ))
            .frame(height: 44)
        }
        .padding()
    }
}
