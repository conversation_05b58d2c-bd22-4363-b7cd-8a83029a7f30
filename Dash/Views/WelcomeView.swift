import SwiftUI

struct WelcomeView: View {
    @State private var isShowingAuthView = false
    @State private var animateElements = false

    var body: some View {
        ZStack {
            // Modern gradient background
            LinearGradient(
                colors: [
                    Color.theme.success,
                    Color.theme.success.opacity(0.8),
                    Color.theme.surfaceSecondary
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea(.all)

            // Floating elements for visual interest
            GeometryReader { geometry in
                ForEach(0..<6, id: \.self) { index in
                    Circle()
                        .fill(Color.white.opacity(0.1))
                        .frame(width: CGFloat.random(in: 20...80))
                        .position(
                            x: CGFloat.random(in: 0...geometry.size.width),
                            y: CGFloat.random(in: 0...geometry.size.height)
                        )
                        .scaleEffect(animateElements ? 1.2 : 0.8)
                        .animation(
                            Animation.easeInOut(duration: Double.random(in: 2...4))
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.2),
                            value: animateElements
                        )
                }
            }

            VStack(spacing: Spacing.xxxl) {
                Spacer()

                // App Icon and Branding
                VStack(spacing: Spacing.xl) {
                    ZStack {
                        Circle()
                            .fill(Color.white)
                            .frame(width: 120, height: 120)
                            .scaleEffect(animateElements ? 1.1 : 1.0)

                        Image("DashLogo")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 80, height: 80)
                            .foregroundColor(.white)
                    }
                    .animation(Animations.spring.delay(0.2), value: animateElements)

                    VStack(spacing: Spacing.sm) {
                        Text("Dash")
                            .font(Font.theme.displayMedium)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 20)

                        Text("Making it easier to split with your mates")
                            .font(Font.theme.bodyLarge)
                            .foregroundColor(.white.opacity(0.9))
                            .multilineTextAlignment(.center)
                            .opacity(animateElements ? 1 : 0)
                            .offset(y: animateElements ? 0 : 20)
                    }
                    .animation(Animations.spring.delay(0.4), value: animateElements)
                }

                Spacer()

                // Call to Action
                VStack(spacing: Spacing.lg) {
                    Button(action: {
                        withAnimation(Animations.spring) {
                            isShowingAuthView = true
                        }
                    }) {
                        HStack(spacing: Spacing.sm) {
                            Text("Get Started")
                                .font(Font.theme.titleLarge)
                                .fontWeight(.semibold)

                            Image(systemName: "arrow.right")
                                .font(.system(size: 18, weight: .medium))
                        }
                        .foregroundColor(Color.theme.success)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, Spacing.md)
                        .background(Color.white)
                        .cornerRadius(BorderRadius.lg)
                        .shadow(
                            color: Color.black.opacity(0.1),
                            radius: 10,
                            x: 0,
                            y: 5
                        )
                    }
                    .opacity(animateElements ? 1 : 0)
                    .offset(y: animateElements ? 0 : 30)
                    .animation(Animations.spring.delay(0.6), value: animateElements)


                }
                .padding(.horizontal, Spacing.xl)
                .padding(.bottom, Spacing.xxxxl)
            }
        }
        .onAppear {
            withAnimation {
                animateElements = true
            }
            // Ensure keyboard is dismissed when welcome view appears
            UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        }
        .fullScreenCover(isPresented: $isShowingAuthView) {
            AuthView()
        }
    }
}

struct WelcomeView_Previews: PreviewProvider {
    static var previews: some View {
        WelcomeView()
    }
} 