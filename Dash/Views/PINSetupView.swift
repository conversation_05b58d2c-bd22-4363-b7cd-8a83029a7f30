import SwiftUI

struct PINSetupView: View {
    @EnvironmentObject var securityViewModel: SecurityViewModel
    @State private var pin: String = ""
    @State private var confirmedPin: String = ""
    @State private var isConfirming: Bool = false
    @State private var errorMessage: String?
    @FocusState private var isPinFieldFocused: Bool

    var body: some View {
        VStack(spacing: 20) {
            Spacer()

            Text(isConfirming ? "Confirm PIN" : (securityViewModel.requiresPinReset ? "Reset Your PIN" : "Create a PIN"))
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding(.bottom, 10)

            Text(securityViewModel.requiresPinReset ?
                 "Your PIN has been reset due to too many failed attempts. Please create a new PIN." :
                 "Your PIN will be used to log in and confirm transactions.")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Spacer()

            PINInputView(pin: isConfirming ? $confirmedPin : $pin, isFocused: $isPinFieldFocused)
                .padding(.vertical, 20)


            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .font(.caption)
            }

            Spacer()
            Spacer()
            
            Button(action: handlePinSubmission) {
                Text(isConfirming ? "Save PIN" : "Confirm PIN")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(isPinValid() ? Color.blue : Color.gray)
                    .cornerRadius(10)
            }
            .disabled(!isPinValid())
            .padding(.horizontal)
        }
        .padding(.bottom, 20)
        .background(Color(.systemGroupedBackground))
        .onAppear {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isPinFieldFocused = true
            }
        }
        .onDisappear {
            // Always dismiss keyboard when leaving the PIN setup screen
            isPinFieldFocused = false
            print("DEBUG: PINSetupView disappeared, dismissing keyboard")
        }
    }
    
    private func isPinValid() -> Bool {
        let currentPin = isConfirming ? confirmedPin : pin
        return currentPin.count == 4 // Example: 4-digit PIN
    }

    private func handlePinSubmission() {
        errorMessage = nil
        isPinFieldFocused = false
        if !isConfirming {
            isConfirming = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                isPinFieldFocused = true
            }
        } else {
            if pin == confirmedPin {
                securityViewModel.savePin(pin)
            } else {
                errorMessage = "PINs do not match. Please try again."
                pin = ""
                confirmedPin = ""
                isConfirming = false
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    isPinFieldFocused = true
                }
            }
        }
    }
}

struct PINInputView: View {
    @Binding var pin: String
    var isFocused: FocusState<Bool>.Binding
    private let pinLength = 4

    var body: some View {
        HStack(spacing: 20) {
            ForEach(0..<pinLength, id: \.self) { index in
                Circle()
                    .strokeBorder(index < pin.count ? Color.primary : Color.secondary.opacity(0.5), lineWidth: 2)
                    .background(Circle().foregroundColor(index < pin.count ? .primary : .clear))
                    .frame(width: 20, height: 20)
            }
        }
        .overlay(
            TextField("", text: $pin)
                .keyboardType(.numberPad)
                .foregroundColor(.clear)
                .accentColor(.clear)
                .focused(isFocused)
                .onChange(of: pin) { newValue in
                    if newValue.count > pinLength {
                        pin = String(newValue.prefix(pinLength))
                    }
                }
        )
    }
}

struct PINSetupView_Previews: PreviewProvider {
    static var previews: some View {
        PINSetupView()
            .environmentObject(SecurityViewModel())
    }
} 