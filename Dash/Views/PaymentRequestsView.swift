import SwiftUI

struct PaymentRequestsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var isResponding = false
    @State private var selectedRequest: PaymentRequest?
    
    private var currentUserId: String {
        return authViewModel.currentUser?.uid ?? ""
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                if podViewModel.paymentRequests.isEmpty {
                    // Empty State
                    VStack(spacing: Spacing.lg) {
                        Image(systemName: "paperplane")
                            .font(.system(size: 48, weight: .medium))
                            .foregroundColor(Color.theme.textTertiary)
                        
                        Text("No Payment Requests")
                            .font(Font.theme.titleLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        Text("You don't have any pending payment requests at the moment.")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }
                } else {
                    // Payment Requests List
                    ScrollView {
                        LazyVStack(spacing: Spacing.md) {
                            ForEach(podViewModel.paymentRequests) { request in
                                PaymentRequestRowView(
                                    request: request,
                                    onRespond: { response in
                                        respondToRequest(request, response: response)
                                    }
                                )
                                .padding(.horizontal, Spacing.lg)
                            }
                            
                            // Bottom spacing
                            Color.clear.frame(height: Spacing.xl)
                        }
                        .padding(.top, Spacing.md)
                    }
                }
            }
            .navigationTitle("Payment Requests")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .onAppear {
                // Fetch payment requests when view appears
                podViewModel.fetchPaymentRequests(userId: currentUserId)
            }
            .alert("Success", isPresented: .constant(podViewModel.successMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.successMessage ?? "")
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
        }
    }
    
    // MARK: - Methods
    
    private func respondToRequest(_ request: PaymentRequest, response: PaymentRequestStatus) {
        guard let requestId = request.id else { return }
        
        isResponding = true
        selectedRequest = request
        
        Task {
            let success = await podViewModel.respondToPaymentRequest(requestId: requestId, response: response)
            
            await MainActor.run {
                isResponding = false
                selectedRequest = nil
                if success {
                    // Refresh payment requests
                    podViewModel.fetchPaymentRequests(userId: currentUserId)
                }
            }
        }
    }
}

// MARK: - Payment Request Row View
struct PaymentRequestRowView: View {
    let request: PaymentRequest
    let onRespond: (PaymentRequestStatus) -> Void
    
    @State private var isResponding = false
    
    var body: some View {
        ModernCard(padding: Spacing.lg) {
            VStack(alignment: .leading, spacing: Spacing.md) {
                // Header
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Payment Request")
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                        
                        Text("From \(request.fromUserName)")
                            .font(Font.theme.titleMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.textPrimary)
                    }
                    
                    Spacer()
                    
                    Text("$\(String(format: "%.2f", request.amount))")
                        .font(Font.theme.titleLarge)
                        .fontWeight(.bold)
                        .foregroundColor(Color.theme.primary)
                }
                
                // Description
                Text(request.description)
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.textPrimary)
                
                // Notes (if any)
                if let notes = request.notes, !notes.isEmpty {
                    Text(notes)
                        .font(Font.theme.bodySmall)
                        .foregroundColor(Color.theme.textSecondary)
                        .padding(.top, 2)
                }
                
                // Date
                Text("Requested \(request.createdAt.formatted(date: .abbreviated, time: .shortened))")
                    .font(Font.theme.labelSmall)
                    .foregroundColor(Color.theme.textTertiary)
                
                // Action Buttons
                if request.status == .pending {
                    HStack(spacing: Spacing.md) {
                        ModernButton(
                            "Decline",
                            style: .tertiary,
                            size: .small,
                            isDisabled: isResponding
                        ) {
                            onRespond(.declined)
                        }
                        .frame(maxWidth: .infinity)
                        
                        ModernButton(
                            "Pay",
                            style: .primary,
                            size: .small,
                            isDisabled: isResponding
                        ) {
                            onRespond(.paid)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .padding(.top, Spacing.sm)
                } else {
                    // Status indicator
                    HStack {
                        Image(systemName: request.status == .paid ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(request.status == .paid ? Color.theme.success : Color.theme.error)
                        
                        Text(request.status.displayName)
                            .font(Font.theme.labelMedium)
                            .foregroundColor(request.status == .paid ? Color.theme.success : Color.theme.error)
                        
                        Spacer()
                    }
                    .padding(.top, Spacing.sm)
                }
            }
        }
    }
}

#Preview {
    PaymentRequestsView()
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
