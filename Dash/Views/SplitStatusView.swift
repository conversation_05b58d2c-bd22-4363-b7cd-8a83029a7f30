import SwiftUI

struct SplitStatusView: View {
    @ObservedObject private var viewModel = SplitViewModel.shared
    @State private var qrCodeImage: UIImage?
    private let qrCodeService = QRCodeService()
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @State private var hasShownWalletForFinalization = false

    private var allParticipantsJoined: Bool {
        guard let split = viewModel.split else { return false }
        return split.participants.count == split.numberOfParticipants
    }
    
    private var allParticipantsPaid: Bool {
        guard let split = viewModel.split else { return false }
        return split.participants.allSatisfy { $0.hasPaid }
    }
    
    private var isCreator: Bool {
        guard let split = viewModel.split,
              let currentUserId = authViewModel.currentUser?.uid else { return false }
        return split.creatorId == currentUserId
    }
    
    private var currentUserHasPaid: Bool {
        guard let split = viewModel.split,
              let currentUserId = authViewModel.currentUser?.uid else { return false }
        return split.participants.first { $0.id == currentUserId }?.hasPaid ?? false
    }

    var body: some View {
        ZStack {
            Color.theme.background.ignoresSafeArea()

            if let split = viewModel.split {
                VStack(spacing: 0) {
                    // Modern Header
                    ModernHeader(
                        title: "Split Status",
                        subtitle: currentUserHasPaid ? "Waiting for all payments" : "Join the split",
                        onDismiss: {
                            // Dismiss to dashboard
                            presentationMode.wrappedValue.dismiss()
                        }
                    )
                    
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: Spacing.xxl) {
                            // Payment Success Message
                            if currentUserHasPaid {
                                ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.md) {
                                    VStack(spacing: Spacing.md) {
                                        HStack(spacing: Spacing.sm) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(Color.theme.success)
                                                .font(.system(size: 24, weight: .medium))
                                            
                                            VStack(alignment: .leading, spacing: 4) {
                                                Text("Payment Successful!")
                                                    .font(Font.theme.titleMedium)
                                                    .fontWeight(.semibold)
                                                    .foregroundColor(Color.theme.success)
                                                
                                                Text("You've successfully joined the split")
                                                    .font(Font.theme.bodySmall)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }
                                            
                                            Spacer()
                                        }
                                    }
                                }
                                .padding(.horizontal, Spacing.lg)
                                .padding(.top, Spacing.lg)
                            }
                            
                            // Split Summary
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    HStack {
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text("Total Amount")
                                                .font(Font.theme.bodySmall)
                                                .foregroundColor(Color.theme.textSecondary)
                                            
                                            Text(split.totalAmount.toCurrency())
                                                .font(Font.theme.titleLarge)
                                                .fontWeight(.bold)
                                                .foregroundColor(Color.theme.textPrimary)
                                        }
                                        
                                        Spacer()
                                        
                                        VStack(alignment: .trailing, spacing: 4) {
                                            Text("Participants")
                                                .font(Font.theme.bodySmall)
                                                .foregroundColor(Color.theme.textSecondary)
                                            
                                            Text("\(split.participants.count) / \(split.numberOfParticipants)")
                                                .font(Font.theme.titleLarge)
                                                .fontWeight(.bold)
                                                .foregroundColor(Color.theme.primary)
                                        }
                                    }
                                    
                                    if allParticipantsJoined {
                                        Divider()
                                            .background(Color.theme.textTertiary.opacity(0.2))
                                        
                                        HStack(spacing: Spacing.sm) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(Color.theme.success)
                                                .font(.system(size: 16, weight: .medium))
                                            
                                            Text("All participants joined!")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.success)
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)
                            
                            // Participants List
                            VStack(alignment: .leading, spacing: Spacing.md) {
                                Text("Participants")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                VStack(spacing: Spacing.sm) {
                                    ForEach(split.participants, id: \.id) { participant in
                                        ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                                            HStack(spacing: Spacing.md) {
                                                // Avatar
                                                ZStack {
                                                    Circle()
                                                        .fill(Color.theme.primary.opacity(0.1))
                                                        .frame(width: 40, height: 40)
                                                    
                                                    Text(String(participant.name.prefix(1).uppercased()))
                                                        .font(Font.theme.bodyMedium)
                                                        .fontWeight(.semibold)
                                                        .foregroundColor(Color.theme.primary)
                                                }
                                                
                                                VStack(alignment: .leading, spacing: 2) {
                                                    HStack(spacing: Spacing.xs) {
                                                        Text(participant.name)
                                                            .font(Font.theme.bodyLarge)
                                                            .fontWeight(.medium)
                                                            .foregroundColor(Color.theme.textPrimary)
                                                        
                                                        if participant.id == authViewModel.currentUser?.uid {
                                                            Text("(You)")
                                                                .font(Font.theme.labelSmall)
                                                                .foregroundColor(Color.theme.textSecondary)
                                                                .padding(.horizontal, Spacing.xs)
                                                                .padding(.vertical, 2)
                                                                .background(Color.theme.surfaceSecondary)
                                                                .cornerRadius(BorderRadius.xs)
                                                        }
                                                    }
                                                    
                                                    // Payment status
                                                    HStack(spacing: Spacing.xs) {
                                                        Circle()
                                                            .fill(participant.hasPaid ? Color.theme.success : Color.theme.warning)
                                                            .frame(width: 6, height: 6)
                                                        
                                                        Text(participant.hasPaid ? "Paid" : "Pending")
                                                            .font(Font.theme.labelSmall)
                                                            .foregroundColor(participant.hasPaid ? Color.theme.success : Color.theme.warning)
                                                    }
                                                }
                                                
                                                Spacer()
                                                
                                                VStack(alignment: .trailing, spacing: 4) {
                                                    Text(participant.share.toCurrency())
                                                        .font(Font.theme.bodyLarge)
                                                        .fontWeight(.semibold)
                                                        .foregroundColor(Color.theme.textPrimary)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)
                            
                            // Status Message
                            if !allParticipantsPaid {
                                ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.md) {
                                    VStack(spacing: Spacing.sm) {
                                        HStack(spacing: Spacing.sm) {
                                            Image(systemName: "clock.fill")
                                                .foregroundColor(Color.theme.primary)
                                                .font(.system(size: 16, weight: .medium))
                                            
                                            Text("Waiting for Payments")
                                                .font(Font.theme.bodyLarge)
                                                .fontWeight(.semibold)
                                                .foregroundColor(Color.theme.primary)
                                        }
                                        
                                        Text("The split will be finalized once everyone has paid")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)
                                            .multilineTextAlignment(.center)
                                    }
                                }
                                .padding(.horizontal, Spacing.lg)
                            } else {
                                ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.md) {
                                    VStack(spacing: Spacing.sm) {
                                        HStack(spacing: Spacing.sm) {
                                            Image(systemName: "checkmark.circle.fill")
                                                .foregroundColor(Color.theme.success)
                                                .font(.system(size: 16, weight: .medium))
                                            
                                            Text("All Payments Complete!")
                                                .font(Font.theme.bodyLarge)
                                                .fontWeight(.semibold)
                                                .foregroundColor(Color.theme.success)
                                        }
                                        
                                        Text("Waiting for the split creator to finalize")
                                            .font(Font.theme.bodySmall)
                                            .foregroundColor(Color.theme.textSecondary)
                                            .multilineTextAlignment(.center)
                                    }
                                }
                                .padding(.horizontal, Spacing.lg)
                            }
                            
                            // Bottom spacing
                            Color.clear.frame(height: 120)
                        }
                    }
                    
                    // Bottom Action Area
                    VStack(spacing: Spacing.md) {
                        ModernButton(
                            "Back to Dashboard",
                            icon: "house.fill",
                            style: .primary,
                            size: .large
                        ) {
                            presentationMode.wrappedValue.dismiss()
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.bottom, Spacing.lg)
                    }
                    .background(
                        Color.theme.background
                            .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
                    )
                }
            } else {
                VStack(spacing: Spacing.lg) {
                    ProgressView()
                        .scaleEffect(1.2)
                    
                    Text("Loading Split...")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            print("SplitStatusView: onAppear called")
            if let splitId = viewModel.split?.id {
                print("SplitStatusView: Setting up listener for split: \(splitId)")
                viewModel.listenToSplit(splitId: splitId)
            } else {
                print("SplitStatusView: No split ID available")
            }
        }
        .onChange(of: viewModel.isSplitFinalized) { isFinalized in
            if isFinalized && !hasShownWalletForFinalization {
                hasShownWalletForFinalization = true

                // Show wallet app after a brief delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    WalletPassService.shared.openWalletApp()

                    // Dismiss the view after showing wallet
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}
