import SwiftUI
import FirebaseFirestore

struct FullTransactionsView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var transactionService = TransactionService.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var searchText = ""
    @State private var selectedFilter: TransactionFilter = .all
    @State private var isShowingFilterSheet = false
    
    enum TransactionFilter: String, CaseIterable {
        case all = "All"
        case income = "Income"
        case expense = "Expense"
        case transfer = "Transfer"
        case pending = "Pending"
        case completed = "Completed"
        
        var icon: String {
            switch self {
            case .all: return "list.bullet"
            case .income: return "plus.circle"
            case .expense: return "minus.circle"
            case .transfer: return "arrow.left.arrow.right"
            case .pending: return "clock"
            case .completed: return "checkmark.circle"
            }
        }
    }
    
    private var filteredTransactions: [Transaction] {
        let allTransactions = transactionService.transactions
        
        // Apply search filter
        let searchFiltered = searchText.isEmpty ? allTransactions : allTransactions.filter {
            $0.name.lowercased().contains(searchText.lowercased()) ||
            $0.detail.lowercased().contains(searchText.lowercased())
        }
        
        // Apply category filter
        switch selectedFilter {
        case .all:
            return searchFiltered
        case .income:
            return searchFiltered.filter { $0.type == .income }
        case .expense:
            return searchFiltered.filter { $0.type == .expense }
        case .transfer:
            return searchFiltered.filter { $0.type == .transfer }
        case .pending:
            return searchFiltered.filter { $0.status == .pending }
        case .completed:
            return searchFiltered.filter { $0.status == .completed }
        }
    }
    
    private var groupedTransactions: [(String, [Transaction])] {
        let grouped = Dictionary(grouping: filteredTransactions) { transaction in
            let formatter = DateFormatter()
            formatter.dateFormat = "MMMM yyyy"
            return formatter.string(from: transaction.date.dateValue())
        }
        
        return grouped.sorted { first, second in
            let formatter = DateFormatter()
            formatter.dateFormat = "MMMM yyyy"
            let firstDate = formatter.date(from: first.key) ?? Date.distantPast
            let secondDate = formatter.date(from: second.key) ?? Date.distantPast
            return firstDate > secondDate
        }.map { (key, value) in
            (key, value.sorted { $0.date.dateValue() > $1.date.dateValue() })
        }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Search and Filter Bar
                    VStack(spacing: Spacing.md) {
                        // Search Bar
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(Color.theme.textTertiary)
                            
                            TextField("Search transactions...", text: $searchText)
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textPrimary)
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.vertical, Spacing.md)
                        .background(Color.theme.surfaceSecondary)
                        .cornerRadius(12)
                        .padding(.horizontal, Spacing.lg)
                        
                        // Filter Chips
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: Spacing.sm) {
                                ForEach(TransactionFilter.allCases, id: \.self) { filter in
                                    FilterChip(
                                        title: filter.rawValue,
                                        icon: filter.icon,
                                        isSelected: selectedFilter == filter
                                    ) {
                                        selectedFilter = filter
                                    }
                                }
                            }
                            .padding(.horizontal, Spacing.lg)
                        }
                    }
                    .padding(.top, Spacing.sm)
                    .background(Color.theme.background)
                    
                    // Transactions List
                    if transactionService.isLoading {
                        // Loading State
                        VStack(spacing: Spacing.lg) {
                            ProgressView()
                                .scaleEffect(1.2)
                            
                            Text("Loading transactions...")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        
                    } else if filteredTransactions.isEmpty {
                        // Empty State
                        VStack(spacing: Spacing.lg) {
                            Image(systemName: searchText.isEmpty ? "list.bullet.rectangle" : "magnifyingglass")
                                .font(.system(size: 48, weight: .medium))
                                .foregroundColor(Color.theme.textTertiary)
                            
                            Text(searchText.isEmpty ? "No Transactions" : "No Results")
                                .font(Font.theme.titleLarge)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Text(searchText.isEmpty ? 
                                 "Your transaction history will appear here" : 
                                 "Try adjusting your search or filter")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, Spacing.lg)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .padding(.top, Spacing.xl)
                        
                    } else {
                        // Transactions List
                        ScrollView(showsIndicators: false) {
                            LazyVStack(spacing: Spacing.lg) {
                                // Summary Card
                                TransactionSummaryCard(transactions: filteredTransactions)
                                    .padding(.horizontal, Spacing.lg)
                                
                                // Grouped Transactions
                                ForEach(groupedTransactions, id: \.0) { month, transactions in
                                    VStack(alignment: .leading, spacing: Spacing.md) {
                                        // Month Header
                                        HStack {
                                            Text(month)
                                                .font(Font.theme.titleMedium)
                                                .fontWeight(.semibold)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Spacer()
                                            
                                            Text("\(transactions.count) transactions")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }
                                        .padding(.horizontal, Spacing.lg)
                                        
                                        // Transactions for this month
                                        ForEach(transactions) { transaction in
                                            TransactionRow(transaction: transaction)
                                                .padding(.horizontal, Spacing.lg)
                                        }
                                    }
                                }
                                
                                // Bottom spacing
                                Color.clear.frame(height: Spacing.xl)
                            }
                            .padding(.top, Spacing.md)
                        }
                        .refreshable {
                            transactionService.loadTransactions()
                        }
                    }
                }
            }
            .navigationTitle("All Transactions")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        isShowingFilterSheet = true
                    }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(Color.theme.primary)
                    }
                }
            }
        }
        .onAppear {
            transactionService.loadTransactions()
        }
        .sheet(isPresented: $isShowingFilterSheet) {
            TransactionFilterSheet(selectedFilter: $selectedFilter)
        }
    }
}

// MARK: - Filter Chip
struct FilterChip: View {
    let title: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.xs) {
                Image(systemName: icon)
                    .font(.system(size: 12, weight: .medium))
                
                Text(title)
                    .font(Font.theme.labelMedium)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, Spacing.md)
            .padding(.vertical, Spacing.sm)
            .background(isSelected ? Color.theme.primary : Color.theme.surfaceSecondary)
            .foregroundColor(isSelected ? .white : Color.theme.textSecondary)
            .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Transaction Summary Card
struct TransactionSummaryCard: View {
    let transactions: [Transaction]
    
    private var totalIncome: Double {
        transactions.filter { $0.type == .income }.reduce(0) { $0 + $1.amount }
    }

    private var totalExpenses: Double {
        transactions.filter { $0.type == .expense }.reduce(0) { $0 + $1.amount }
    }
    
    private var netAmount: Double {
        totalIncome - totalExpenses
    }
    
    var body: some View {
        ModernCard(padding: Spacing.lg) {
            VStack(spacing: Spacing.md) {
                Text("Summary")
                    .font(Font.theme.titleMedium)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.theme.textPrimary)
                
                HStack(spacing: Spacing.lg) {
                    VStack(spacing: 4) {
                        Text("Income")
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                        
                        Text("+$\(String(format: "%.2f", totalIncome))")
                            .font(Font.theme.titleSmall)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.success)
                    }
                    
                    VStack(spacing: 4) {
                        Text("Expenses")
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                        
                        Text("-$\(String(format: "%.2f", totalExpenses))")
                            .font(Font.theme.titleSmall)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.error)
                    }
                    
                    VStack(spacing: 4) {
                        Text("Net")
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.textSecondary)
                        
                        Text("$\(String(format: "%.2f", netAmount))")
                            .font(Font.theme.titleSmall)
                            .fontWeight(.semibold)
                            .foregroundColor(netAmount >= 0 ? Color.theme.success : Color.theme.error)
                    }
                }
            }
        }
    }
}

// MARK: - Transaction Filter Sheet
struct TransactionFilterSheet: View {
    @Binding var selectedFilter: FullTransactionsView.TransactionFilter
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: Spacing.lg) {
                ForEach(FullTransactionsView.TransactionFilter.allCases, id: \.self) { filter in
                    Button(action: {
                        selectedFilter = filter
                        dismiss()
                    }) {
                        HStack {
                            Image(systemName: filter.icon)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color.theme.primary)
                                .frame(width: 24)
                            
                            Text(filter.rawValue)
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Spacer()
                            
                            if selectedFilter == filter {
                                Image(systemName: "checkmark")
                                    .font(.system(size: 14, weight: .semibold))
                                    .foregroundColor(Color.theme.primary)
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                        .padding(.vertical, Spacing.md)
                        .background(selectedFilter == filter ? Color.theme.primary.opacity(0.1) : Color.clear)
                        .cornerRadius(12)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                Spacer()
            }
            .padding(.top, Spacing.lg)
            .padding(.horizontal, Spacing.lg)
            .navigationTitle("Filter Transactions")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    FullTransactionsView()
        .environmentObject(AuthViewModel.shared)
}
