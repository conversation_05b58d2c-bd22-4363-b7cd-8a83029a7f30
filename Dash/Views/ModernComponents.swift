import SwiftUI

// MARK: - Modern Button Component
struct ModernButton: View {
    let title: String
    let icon: String?
    let style: Style
    let size: Size
    let action: () -> Void
    let isLoading: Bool
    let isDisabled: Bool
    
    enum Style {
        case primary, secondary, tertiary, destructive, ghost, success
    }
    
    enum Size {
        case small, medium, large
    }
    
    init(
        _ title: String,
        icon: String? = nil,
        style: Style = .primary,
        size: Size = .medium,
        isLoading: Bool = false,
        isDisabled: Bool = false,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.icon = icon
        self.style = style
        self.size = size
        self.isLoading = isLoading
        self.isDisabled = isDisabled
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: Spacing.sm) {
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(foregroundColor)
                } else if let icon = icon {
                    Image(systemName: icon)
                        .font(iconFont)
                }
                
                Text(title)
                    .font(textFont)
                    .fontWeight(.semibold)
            }
            .foregroundColor(foregroundColor)
            .padding(.horizontal, horizontalPadding)
            .padding(.vertical, verticalPadding)
            .frame(maxWidth: .infinity)
            .background(backgroundColor)
            .cornerRadius(BorderRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: BorderRadius.md)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
        }
        .disabled(isDisabled || isLoading)
        .opacity(isDisabled ? 0.6 : 1.0)
        .scaleEffect(isDisabled ? 0.98 : 1.0)
        .animation(Animations.fast, value: isDisabled)
        .animation(Animations.fast, value: isLoading)
    }
    
    // MARK: - Style Properties
    private var textFont: Font {
        switch size {
        case .small: return Font.theme.labelSmall
        case .medium: return Font.theme.bodyLarge
        case .large: return Font.theme.titleMedium
        }
    }

    private var iconFont: Font {
        switch size {
        case .small: return .system(size: 12, weight: .medium)
        case .medium: return .system(size: 14, weight: .medium)
        case .large: return .system(size: 16, weight: .medium)
        }
    }

    private var horizontalPadding: CGFloat {
        switch size {
        case .small: return Spacing.sm
        case .medium: return Spacing.lg
        case .large: return Spacing.xl
        }
    }

    private var verticalPadding: CGFloat {
        switch size {
        case .small: return Spacing.xs
        case .medium: return Spacing.sm
        case .large: return Spacing.md
        }
    }
    
    private var foregroundColor: Color {
        switch style {
        case .primary: return Color.theme.textInverse
        case .secondary: return Color.theme.primary
        case .tertiary: return Color.theme.textPrimary
        case .destructive: return Color.theme.textInverse
        case .ghost: return Color.theme.primary
        case .success: return Color.theme.textInverse
        }
    }
    
    private var backgroundColor: Color {
        switch style {
        case .primary: return Color.theme.primary
        case .secondary: return Color.theme.primaryLight
        case .tertiary: return Color.theme.neutral100
        case .destructive: return Color.theme.error
        case .ghost: return Color.clear
        case .success: return Color.theme.success
        }
    }
    
    private var borderColor: Color {
        switch style {
        case .ghost: return Color.theme.primary
        default: return Color.clear
        }
    }
    
    private var borderWidth: CGFloat {
        style == .ghost ? 1 : 0
    }
}

// MARK: - Modern Card Component
struct ModernCard<Content: View>: View {
    let content: Content
    let padding: CGFloat
    let shadow: Shadow
    let cornerRadius: CGFloat
    
    init(
        padding: CGFloat = Spacing.lg,
        shadow: Shadow = Shadows.sm,
        cornerRadius: CGFloat = BorderRadius.lg,
        @ViewBuilder content: () -> Content
    ) {
        self.content = content()
        self.padding = padding
        self.shadow = shadow
        self.cornerRadius = cornerRadius
    }
    
    var body: some View {
        content
            .padding(padding)
            .background(Color.theme.surface)
            .cornerRadius(cornerRadius)
            .shadow(
                color: shadow.color,
                radius: shadow.radius,
                x: shadow.x,
                y: shadow.y
            )
    }
}

// MARK: - Modern Text Field Component
struct ModernTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let keyboardType: UIKeyboardType
    let isSecure: Bool
    let hasError: Bool
    let errorMessage: String?
    let accentColor: Color

    @FocusState private var isFocused: Bool

    init(
        _ title: String,
        text: Binding<String>,
        placeholder: String = "",
        keyboardType: UIKeyboardType = .default,
        isSecure: Bool = false,
        hasError: Bool = false,
        errorMessage: String? = nil,
        accentColor: Color = Color.theme.primary
    ) {
        self.title = title
        self._text = text
        self.placeholder = placeholder.isEmpty ? title : placeholder
        self.keyboardType = keyboardType
        self.isSecure = isSecure
        self.hasError = hasError
        self.errorMessage = errorMessage
        self.accentColor = accentColor
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text(title)
                .font(Font.theme.labelMedium)
                .foregroundColor(Color.theme.textSecondary)
            
            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                } else {
                    TextField(placeholder, text: $text)
                }
            }
            .font(Font.theme.bodyLarge)
            .foregroundColor(Color.theme.textPrimary)
            .keyboardType(keyboardType)
            .focused($isFocused)
            .padding(Spacing.lg)
            .background(Color.theme.surfaceSecondary)
            .cornerRadius(BorderRadius.md)
            .overlay(
                RoundedRectangle(cornerRadius: BorderRadius.md)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .animation(Animations.fast, value: isFocused)
            .animation(Animations.fast, value: hasError)
            
            if let errorMessage = errorMessage, hasError {
                Text(errorMessage)
                    .font(Font.theme.labelSmall)
                    .foregroundColor(Color.theme.error)
                    .transition(.opacity)
            }
        }
    }
    
    private var borderColor: Color {
        if hasError {
            return Color.theme.error
        } else if isFocused {
            return accentColor
        } else {
            return Color.clear
        }
    }
    
    private var borderWidth: CGFloat {
        (isFocused || hasError) ? 2 : 0
    }
}

// MARK: - Modern Action Button (for dashboard)
struct ModernActionButton: View {
    let icon: String
    let title: String
    let subtitle: String?
    let color: Color
    let action: () -> Void
    
    init(
        icon: String,
        title: String,
        subtitle: String? = nil,
        color: Color = Color.theme.primary,
        action: @escaping () -> Void
    ) {
        self.icon = icon
        self.title = title
        self.subtitle = subtitle
        self.color = color
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: Spacing.sm) {
                ZStack {
                    Circle()
                        .fill(color.opacity(0.1))
                        .frame(width: 56, height: 56)
                    
                    Image(systemName: icon)
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(color)
                }
                
                VStack(spacing: 2) {
                    Text(title)
                        .font(Font.theme.labelMedium)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(Font.theme.labelSmall)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Modern List Row Component
struct ModernListRow<Content: View>: View {
    let content: Content
    let action: (() -> Void)?
    
    init(action: (() -> Void)? = nil, @ViewBuilder content: () -> Content) {
        self.content = content()
        self.action = action
    }
    
    var body: some View {
        Group {
            if let action = action {
                Button(action: action) {
                    rowContent
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                rowContent
            }
        }
    }
    
    private var rowContent: some View {
        content
            .padding(Spacing.lg)
            .background(Color.theme.surface)
            .cornerRadius(BorderRadius.md)
            .shadow(
                color: Shadows.xs.color,
                radius: Shadows.xs.radius,
                x: Shadows.xs.x,
                y: Shadows.xs.y
            )
    }
}

// MARK: - Modern Header Component
struct ModernHeader: View {
    let title: String
    let subtitle: String?
    let showBackButton: Bool
    let onDismiss: () -> Void
    let trailingContent: (() -> AnyView)?

    init(
        title: String,
        subtitle: String? = nil,
        showBackButton: Bool = true,
        onDismiss: @escaping () -> Void,
        trailingContent: (() -> AnyView)? = nil
    ) {
        self.title = title
        self.subtitle = subtitle
        self.showBackButton = showBackButton
        self.onDismiss = onDismiss
        self.trailingContent = trailingContent
    }

    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: Spacing.md) {
                if showBackButton {
                    Button(action: onDismiss) {
                        ZStack {
                            Circle()
                                .fill(Color.theme.surfaceSecondary)
                                .frame(width: 40, height: 40)

                            Image(systemName: "chevron.left")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(Color.theme.textPrimary)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                } else {
                    Color.clear.frame(width: 40, height: 40)
                }

                Spacer()

                VStack(spacing: 2) {
                    Text(title)
                        .font(Font.theme.titleMedium)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)

                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(Font.theme.bodySmall)
                            .foregroundColor(Color.theme.textSecondary)
                    }
                }

                Spacer()

                if let trailingContent = trailingContent {
                    trailingContent()
                } else {
                    Color.clear.frame(width: 40, height: 40)
                }
            }
            .padding(.horizontal, Spacing.lg)
            .padding(.vertical, Spacing.md)

            // Subtle separator
            Rectangle()
                .fill(Color.theme.textTertiary.opacity(0.1))
                .frame(height: 1)
        }
        .background(Color.theme.background)
    }
}

#Preview {
    VStack(spacing: Spacing.lg) {
        ModernHeader(title: "Add Money") { }

        ModernButton("Primary Button", icon: "checkmark") { }
        ModernButton("Secondary", style: .secondary) { }
        ModernButton("Loading", isLoading: true) { }

        ModernCard {
            Text("Card Content")
                .padding()
        }

        ModernTextField("Email", text: .constant(""), placeholder: "Enter your email")

        ModernActionButton(icon: "plus", title: "Add Money") { }
    }
    .padding()
}
