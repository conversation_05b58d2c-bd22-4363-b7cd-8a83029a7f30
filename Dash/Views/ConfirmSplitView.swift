import SwiftUI

struct ConfirmSplitView: View {
    let totalAmount: Decimal
    let participants: [Participant]
    
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.presentationMode) var presentationMode

    private var currentUserShare: Decimal {
        Decimal(participants.first(where: { $0.name == "Me" })?.share ?? 0)
    }
    
    private var userBalance: Decimal {
        Decimal(authViewModel.currentUser?.balance ?? 0)
    }
    

    var body: some View {
        ZStack {
            Color.theme.background.edgesIgnoringSafeArea(.all)
            
            VStack {
                Form {
                    Section(header: Text("Your Share")) {
                        HStack {
                            Text("Your portion of the split")
                            Spacer()
                            Text(currentUserShare, format: .currency(code: "AUD"))
                        }
                        
                        HStack {
                            Text("Your current balance")
                            Spacer()
                            Text(userBalance, format: .currency(code: "AUD"))
                        }
                    }
                    
                }
                
                Button(action: {
                    // This will be handled by the DashboardView
                }) {
                    Text("Confirm Split")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(10)
                }
                .padding()
            }
            .navigationTitle("Confirm & Pay")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(leading: Button("Cancel") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}