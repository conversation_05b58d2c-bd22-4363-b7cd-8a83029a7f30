import SwiftUI

struct SignUpView: View {
    @State private var email = ""
    @State private var password = ""
    @State private var fullname = ""
    @State private var phoneNumber = ""

    // Validation states
    @State private var emailError = ""
    @State private var passwordError = ""
    @State private var fullnameError = ""
    @State private var phoneError = ""
    @State private var showErrorAlert = false
    @EnvironmentObject var viewModel: AuthViewModel
    @Environment(\.dismiss) var dismiss

    var isFormValid: Bool {
        !email.isEmpty && 
        !password.isEmpty && 
        !fullname.isEmpty && 
        !phoneNumber.isEmpty &&
        isValidEmail(email) &&
        isValidPhoneNumber(phoneNumber)
    }
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    private func isValidPhoneNumber(_ phone: String) -> Bool {
        // Basic phone number validation - at least 10 digits
        let phoneRegex = "^[+]?[0-9]{10,15}$"
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        return phonePredicate.evaluate(with: phone.replacingOccurrences(of: " ", with: "").replacingOccurrences(of: "-", with: ""))
    }
    
    private func signUpWithValidation() {
        // Clear previous errors
        emailError = ""
        passwordError = ""
        fullnameError = ""
        phoneError = ""

        // Validate email
        if email.isEmpty {
            emailError = "Email is required"
        } else if !email.contains("@") {
            emailError = "Please enter a valid email address"
        }

        // Validate full name
        if fullname.isEmpty {
            fullnameError = "Full name is required"
        } else if fullname.count < 2 {
            fullnameError = "Full name must be at least 2 characters"
        }

        // Validate phone number
        if phoneNumber.isEmpty {
            phoneError = "Phone number is required"
        } else if phoneNumber.count < 10 {
            phoneError = "Please enter a valid phone number"
        }

        // Validate password
        if password.isEmpty {
            passwordError = "Password is required"
        } else if password.count < 6 {
            passwordError = "Password must be at least 6 characters"
        }

        // If all validations pass, proceed with sign up
        if emailError.isEmpty && passwordError.isEmpty && fullnameError.isEmpty && phoneError.isEmpty {
            viewModel.signUp(withEmail: email, password: password, displayName: fullname, phoneNumber: phoneNumber)
        }
    }

    var body: some View {
        ZStack {
            Color.theme.surfaceSecondary.ignoresSafeArea(.all)

            ScrollView(showsIndicators: false) {
                VStack(spacing: Spacing.xl) {
                    // Header
                    VStack(spacing: Spacing.lg) {
                        Text("Create Account")
                            .font(Font.theme.headlineLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)

                        Text("Join thousands of users managing their finances with Dash")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, Spacing.xxxxl)

                    // Form Card
                    ModernCard(padding: Spacing.xl) {
                        VStack(spacing: Spacing.lg) {
                            ModernTextField(
                                "Email",
                                text: $email,
                                placeholder: "Enter your email address",
                                keyboardType: .emailAddress,
                                hasError: !emailError.isEmpty,
                                errorMessage: emailError.isEmpty ? nil : emailError,
                                accentColor: Color.theme.success
                            )

                            ModernTextField(
                                "Full Name",
                                text: $fullname,
                                placeholder: "Enter your full name",
                                hasError: !fullnameError.isEmpty,
                                errorMessage: fullnameError.isEmpty ? nil : fullnameError,
                                accentColor: Color.theme.success
                            )

                            ModernTextField(
                                "Phone Number",
                                text: $phoneNumber,
                                placeholder: "Enter your phone number",
                                keyboardType: .phonePad,
                                hasError: !phoneError.isEmpty,
                                errorMessage: phoneError.isEmpty ? nil : phoneError,
                                accentColor: Color.theme.success
                            )

                            ModernTextField(
                                "Password",
                                text: $password,
                                placeholder: "Create a secure password",
                                isSecure: true,
                                hasError: !passwordError.isEmpty,
                                errorMessage: passwordError.isEmpty ? nil : passwordError,
                                accentColor: Color.theme.success
                            )
                        }
                    }

                    // Sign Up Button
                    ModernButton(
                        "Create Account",
                        icon: "person.badge.plus",
                        style: .success,
                        size: .medium,
                        isLoading: viewModel.isLoading,
                        isDisabled: !isFormValid
                    ) {
                        signUpWithValidation()
                    }

                    // Error Message
                    if let errorMessage = viewModel.errorMessage {
                        Text(errorMessage)
                            .font(Font.theme.labelMedium)
                            .foregroundColor(Color.theme.error)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }

                    // Sign In Link
                    Button {
                        dismiss()
                    } label: {
                        HStack(spacing: 4) {
                            Text("Already have an account?")
                                .foregroundColor(Color.theme.textSecondary)
                            Text("Sign In")
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.success)
                        }
                        .font(Font.theme.bodyMedium)
                    }
                    .padding(.top, Spacing.lg)

                    // Bottom spacing
                    Color.clear.frame(height: Spacing.xl)
                }
                .padding(.horizontal, Spacing.lg)
            }
            .onChange(of: viewModel.userSession) { _, newUserSession in
                if newUserSession != nil {
                    dismiss()
                }
            }
            .onAppear {
                // Clear any existing error messages when the sign-up view appears
                viewModel.clearErrorState()
            }
        }
    }
}

struct SignUpView_Previews: PreviewProvider {
    static var previews: some View {
        SignUpView()
            .environmentObject(AuthViewModel.shared)
    }
}
