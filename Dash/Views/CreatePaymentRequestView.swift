import SwiftUI

struct CreatePaymentRequestView: View {
    let pod: Pod
    let preselectedMember: PodMember?
    let prefilledAmount: Double?
    @EnvironmentObject var authViewModel: AuthViewModel
    @EnvironmentObject var podViewModel: PodViewModel
    @Environment(\.dismiss) private var dismiss

    @State private var selectedMember: PodMember?
    @State private var amount: String = ""
    @State private var description: String = ""
    @State private var notes: String = ""
    @State private var isCreating = false

    init(pod: Pod, preselectedMember: PodMember? = nil, prefilledAmount: Double? = nil) {
        self.pod = pod
        self.preselectedMember = preselectedMember
        self.prefilledAmount = prefilledAmount
    }
    
    private var isFormValid: Bool {
        // Check if member is selected
        guard selectedMember != nil else { return false }

        // Check if amount is valid
        guard let amountValue = Double(amount), amountValue > 0 else { return false }

        // Check if description is not empty
        let trimmedDescription = description.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedDescription.isEmpty else { return false }

        return true
    }
    
    private var currentUserId: String {
        return authViewModel.currentUser?.uid ?? ""
    }
    
    private var availableMembers: [PodMember] {
        return pod.members.filter { $0.isActive && $0.userId != currentUserId }
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background.ignoresSafeArea()

                ScrollView {
                    VStack(spacing: Spacing.lg) {
                        headerSection
                        formSection
                        createButtonSection
                        Spacer(minLength: Spacing.xl)
                    }
                }
            }
            .navigationTitle("Payment Request")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .alert("Success", isPresented: .constant(podViewModel.successMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                    dismiss()
                }
            } message: {
                Text(podViewModel.successMessage ?? "")
            }
            .alert("Error", isPresented: .constant(podViewModel.errorMessage != nil)) {
                Button("OK") {
                    podViewModel.clearMessages()
                }
            } message: {
                Text(podViewModel.errorMessage ?? "")
            }
            .onAppear {
                // Set preselected member and amount if provided
                if let preselectedMember = preselectedMember {
                    selectedMember = preselectedMember
                }
                if let prefilledAmount = prefilledAmount {
                    amount = String(format: "%.2f", prefilledAmount)
                }
            }
        }
    }

    // MARK: - View Components

    private var headerSection: some View {
        VStack(spacing: Spacing.sm) {
            Text("Request Payment")
                .font(Font.theme.titleLarge)
                .fontWeight(.bold)
                .foregroundColor(Color.theme.textPrimary)

            Text("Request money from a pod member")
                .font(Font.theme.bodyMedium)
                .foregroundColor(Color.theme.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.horizontal, Spacing.lg)
        .padding(.top, Spacing.md)
    }

    private var formSection: some View {
        VStack(spacing: Spacing.lg) {
            memberSelectionSection
            amountSection
            descriptionSection
            notesSection
        }
        .padding(.horizontal, Spacing.lg)
    }

    private var memberSelectionSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Request From")
                .font(Font.theme.labelLarge)
                .fontWeight(.medium)
                .foregroundColor(Color.theme.textPrimary)

            ModernCard(padding: Spacing.lg) {
                Picker("Select Member", selection: $selectedMember) {
                    Text("Select a member").tag(nil as PodMember?)
                    ForEach(availableMembers) { member in
                        Text(member.displayName).tag(member as PodMember?)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }
        }
    }

    private var amountSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Amount")
                .font(Font.theme.labelLarge)
                .fontWeight(.medium)
                .foregroundColor(Color.theme.textPrimary)

            ModernTextField(
                "Amount",
                text: $amount,
                placeholder: "0.00",
                keyboardType: .decimalPad
            )
        }
    }

    private var descriptionSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Description")
                .font(Font.theme.labelLarge)
                .fontWeight(.medium)
                .foregroundColor(Color.theme.textPrimary)

            ModernTextField(
                "Description",
                text: $description,
                placeholder: "What is this payment for?"
            )
        }
    }

    private var notesSection: some View {
        VStack(alignment: .leading, spacing: Spacing.sm) {
            Text("Notes (Optional)")
                .font(Font.theme.labelLarge)
                .fontWeight(.medium)
                .foregroundColor(Color.theme.textPrimary)

            ModernTextField(
                "Notes",
                text: $notes,
                placeholder: "Additional details..."
            )
        }
    }

    private var createButtonSection: some View {
        ModernButton(
            isCreating ? "Sending Request..." : "Send Request",
            icon: "paperplane",
            style: .primary,
            size: .medium,
            isLoading: isCreating,
            isDisabled: !isFormValid
        ) {
            createPaymentRequest()
        }
        .padding(.horizontal, Spacing.lg)
        .padding(.top, Spacing.md)
    }
    
    // MARK: - Methods
    
    private func createPaymentRequest() {
        guard let selectedMember = selectedMember,
              let podId = pod.id,
              let amountValue = Double(amount),
              let currentUser = authViewModel.currentUser,
              let currentUserId = currentUser.uid,
              let currentUserName = currentUser.displayName else {
            return
        }
        isCreating = true
        
        let request = PaymentRequest(
            podId: podId,
            fromUserId: currentUserId,
            fromUserName: currentUserName,
            toUserId: selectedMember.userId,
            toUserName: selectedMember.displayName,
            amount: amountValue,
            description: description.trimmingCharacters(in: .whitespacesAndNewlines),
            notes: notes.isEmpty ? nil : notes.trimmingCharacters(in: .whitespacesAndNewlines)
        )
        
        Task {
            let success = await podViewModel.createPaymentRequest(request)
            
            await MainActor.run {
                isCreating = false
                if success {
                    // Success is handled by the alert
                } else {
                    // Error is handled by the alert
                }
            }
        }
    }
}

#Preview {
    CreatePaymentRequestView(pod: Pod(name: "Weekend Trip", description: "Our amazing weekend getaway", createdBy: "user1", createdByName: "John Doe"))
        .environmentObject(AuthViewModel.shared)
        .environmentObject(PodViewModel.shared)
}
