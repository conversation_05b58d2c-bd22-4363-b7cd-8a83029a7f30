import SwiftUI
import Firebase
import FirebaseAuth


class AddMoneyViewModel: ObservableObject {
    @Published var amountString: String = "0"
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    var amount: Double {
        (Double(amountString) ?? 0) / 100.0
    }

    func appendDigit(_ digit: String) {
        if amountString == "0" {
            amountString = digit
        } else {
            amountString += digit
        }
    }

    func deleteDigit() {
        amountString = String(amountString.dropLast())
        if amountString.isEmpty {
            amountString = "0"
        }
    }


    func updateUserBalance() {
        guard let userId = Auth.auth().currentUser?.uid, !userId.isEmpty else {
            self.errorMessage = "User not authenticated."
            return
        }
        
        let db = Firestore.firestore()
        let userRef = db.collection("users").document(userId)
        
        db.runTransaction({ transaction, errorPointer -> Any? in
            let userDocument: DocumentSnapshot
            do {
                try userDocument = transaction.getDocument(userRef)
            } catch let fetchError as NSError {
                errorPointer?.pointee = fetchError
                return nil
            }
            
            let oldBalance = userDocument.data()?["balance"] as? Double ?? 0.0
            let newBalance = oldBalance + self.amount
            
            transaction.updateData(["balance": newBalance], forDocument: userRef)
            return nil
        }) { _, error in
            if let error = error {
                self.errorMessage = "Failed to update balance: \(error.localizedDescription)"
            }
        }
    }
}

struct AddMoneyView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var viewModel = AddMoneyViewModel()
    @Environment(\.self) var environment

    private var presentingViewController: UIViewController? {
        guard let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
            return nil
        }
        return scene.windows.first?.rootViewController
    }

    var body: some View {
        ZStack {
            Color.theme.background.ignoresSafeArea()

            VStack(spacing: 0) {
                // Modern Header
                ModernHeader(
                    title: "Add Money",
                    onDismiss: { presentationMode.wrappedValue.dismiss() }
                )

                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.xxxl) {
                        // Amount Display Section
                        VStack(spacing: Spacing.lg) {
                            Text("Amount to add")
                                .font(Font.theme.bodyMedium)
                                .foregroundColor(Color.theme.textSecondary)
                                .padding(.top, Spacing.xxl)

                            // Large amount display with modern styling
                            Text(viewModel.amount.toCurrency())
                                .font(Font.theme.displaySmall)
                                .fontWeight(.bold)
                                .foregroundColor(Color.theme.textPrimary)
                                .minimumScaleFactor(0.5)
                                .lineLimit(1)
                        }
                        .padding(.horizontal, Spacing.lg)

                        // Numeric Keypad
                        NumericKeypad(
                            onKeyPress: viewModel.appendDigit,
                            onDelete: viewModel.deleteDigit
                        )
                        .padding(.top, Spacing.xl)

                        // Bottom spacing for button
                        Color.clear.frame(height: 120)
                    }
                }

                // Bottom Action Area
                VStack(spacing: Spacing.md) {
                    if let errorMessage = viewModel.errorMessage {
                        ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                            HStack(spacing: Spacing.sm) {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(Color.theme.error)
                                    .font(.system(size: 16, weight: .medium))

                                Text(errorMessage)
                                    .font(Font.theme.bodyMedium)
                                    .foregroundColor(Color.theme.error)
                                    .multilineTextAlignment(.leading)

                                Spacer()
                            }
                        }
                        .padding(.horizontal, Spacing.lg)
                    }

                    // Add Money Button
                    ModernButton(
                        "Add Money",
                        icon: "plus.circle.fill",
                        style: .primary,
                        size: .large,
                        isLoading: viewModel.isLoading,
                        isDisabled: viewModel.amount <= 0
                    ) {
                        viewModel.updateUserBalance()
                        presentationMode.wrappedValue.dismiss()
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.bottom, Spacing.lg)
                }
                .background(
                    Color.theme.background
                        .shadow(color: Color.theme.textTertiary.opacity(0.05), radius: 8, x: 0, y: -2)
                )
            }
        }
        .navigationBarHidden(true)
    }
}
