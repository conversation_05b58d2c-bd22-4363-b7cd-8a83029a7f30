import SwiftUI
import PassKit

struct WalletSettingsView: View {
    @EnvironmentObject var viewModel: AuthViewModel
    @StateObject private var walletService = WalletPassService.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.surfaceSecondary.ignoresSafeArea(.all)
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.lg) {
                        if let user = viewModel.currentUser, walletService.isWalletAvailable() {
                            // Dash Card Section
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Dash Card")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    VStack(spacing: Spacing.md) {
                                        HStack(spacing: Spacing.md) {
                                            Image(systemName: "wallet.pass.fill")
                                                .font(.system(size: 20, weight: .medium))
                                                .foregroundColor(Color.theme.primary)
                                                .frame(width: 24)

                                            VStack(alignment: .leading, spacing: 2) {
                                                Text("Apple Wallet Pass")
                                                    .font(Font.theme.bodyMedium)
                                                    .fontWeight(.medium)
                                                    .foregroundColor(Color.theme.textPrimary)

                                                if walletService.userPasses.isEmpty {
                                                    Text("Add your Dash card to Apple Wallet for quick access")
                                                        .font(Font.theme.labelMedium)
                                                        .foregroundColor(Color.theme.textSecondary)
                                                } else {
                                                    Text("\(walletService.userPasses.count) pass(es) in wallet")
                                                        .font(Font.theme.labelMedium)
                                                        .foregroundColor(Color.theme.success)
                                                }
                                            }

                                            Spacer()
                                        }

                                        if walletService.userPasses.isEmpty {
                                            AddToWalletButton(user: user)
                                        } else {
                                            VStack(spacing: Spacing.sm) {
                                                // Show existing passes
                                                ForEach(walletService.userPasses, id: \.serialNumber) { pass in
                                                    if let details = walletService.getPassDetails(for: pass) {
                                                        HStack {
                                                            VStack(alignment: .leading, spacing: 2) {
                                                                Text("Balance: $\(String(format: "%.2f", details.balance))")
                                                                    .font(Font.theme.bodyMedium)
                                                                    .fontWeight(.medium)
                                                                    .foregroundColor(Color.theme.textPrimary)

                                                                Text(details.cardNumber)
                                                                    .font(Font.theme.labelMedium)
                                                                    .foregroundColor(Color.theme.textSecondary)

                                                                Text("Updated: \(details.lastUpdated, style: .relative)")
                                                                    .font(Font.theme.labelSmall)
                                                                    .foregroundColor(Color.theme.textSecondary)
                                                            }

                                                            Spacer()

                                                            Text("In Wallet")
                                                                .font(Font.theme.labelSmall)
                                                                .fontWeight(.medium)
                                                                .foregroundColor(Color.theme.success)
                                                                .padding(.horizontal, Spacing.sm)
                                                                .padding(.vertical, 4)
                                                                .background(Color.theme.success.opacity(0.1))
                                                                .cornerRadius(BorderRadius.xs)
                                                        }
                                                        .padding(.vertical, Spacing.xs)
                                                        .padding(.horizontal, Spacing.sm)
                                                        .background(Color.theme.surfaceSecondary.opacity(0.5))
                                                        .cornerRadius(BorderRadius.sm)
                                                    }
                                                }

                                                // Add new pass button
                                                Button(action: {
                                                    Task {
                                                        await walletService.generateAndAddPass(for: user)
                                                    }
                                                }) {
                                                    HStack {
                                                        Image(systemName: "plus")
                                                            .font(.system(size: 14, weight: .medium))
                                                        Text("Add New Pass")
                                                            .font(Font.theme.bodyMedium)
                                                            .fontWeight(.medium)
                                                    }
                                                    .foregroundColor(Color.theme.primary)
                                                    .frame(maxWidth: .infinity)
                                                    .padding(.vertical, Spacing.sm)
                                                    .background(
                                                        RoundedRectangle(cornerRadius: BorderRadius.sm)
                                                            .stroke(Color.theme.primary, lineWidth: 1)
                                                    )
                                                }
                                                .disabled(walletService.isLoading)
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // Pass Settings Section
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Pass Settings")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    VStack(spacing: Spacing.md) {
                                        HStack(spacing: Spacing.md) {
                                            Image(systemName: "arrow.clockwise")
                                                .font(.system(size: 20, weight: .medium))
                                                .foregroundColor(Color.theme.info)
                                                .frame(width: 24)

                                            VStack(alignment: .leading, spacing: 2) {
                                                Text("Auto-Update")
                                                    .font(Font.theme.bodyMedium)
                                                    .fontWeight(.medium)
                                                    .foregroundColor(Color.theme.textPrimary)
                                                
                                                Text("Automatically update pass balance")
                                                    .font(Font.theme.labelMedium)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }

                                            Spacer()

                                            Toggle("", isOn: .constant(true))
                                                .labelsHidden()
                                        }
                                        
                                        Divider()
                                            .background(Color.theme.neutral200)
                                        
                                        HStack(spacing: Spacing.md) {
                                            Image(systemName: "bell.badge")
                                                .font(.system(size: 20, weight: .medium))
                                                .foregroundColor(Color.theme.warning)
                                                .frame(width: 24)

                                            VStack(alignment: .leading, spacing: 2) {
                                                Text("Transaction Notifications")
                                                    .font(Font.theme.bodyMedium)
                                                    .fontWeight(.medium)
                                                    .foregroundColor(Color.theme.textPrimary)
                                                
                                                Text("Show notifications on lock screen")
                                                    .font(Font.theme.labelMedium)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }

                                            Spacer()

                                            Toggle("", isOn: .constant(true))
                                                .labelsHidden()
                                        }
                                    }
                                }
                            }
                            
                            // Card Design Section
                            VStack(alignment: .leading, spacing: Spacing.sm) {
                                Text("Card Design")
                                    .font(Font.theme.titleMedium)
                                    .fontWeight(.semibold)
                                    .foregroundColor(Color.theme.textPrimary)
                                
                                ModernCard(padding: Spacing.lg) {
                                    VStack(spacing: Spacing.md) {
                                        HStack(spacing: Spacing.md) {
                                            Image(systemName: "paintbrush.fill")
                                                .font(.system(size: 20, weight: .medium))
                                                .foregroundColor(Color.theme.primary)
                                                .frame(width: 24)

                                            VStack(alignment: .leading, spacing: 2) {
                                                Text("Card Style")
                                                    .font(Font.theme.bodyMedium)
                                                    .fontWeight(.medium)
                                                    .foregroundColor(Color.theme.textPrimary)
                                                
                                                Text("Classic White")
                                                    .font(Font.theme.labelMedium)
                                                    .foregroundColor(Color.theme.textSecondary)
                                            }

                                            Spacer()
                                            
                                            if user.currentTier == .classic {
                                                Text("Upgrade for more")
                                                    .font(Font.theme.labelSmall)
                                                    .fontWeight(.medium)
                                                    .foregroundColor(Color.theme.warning)
                                                    .padding(.horizontal, Spacing.sm)
                                                    .padding(.vertical, 4)
                                                    .background(Color.theme.warning.opacity(0.1))
                                                    .cornerRadius(BorderRadius.xs)
                                            } else {
                                                Image(systemName: "chevron.right")
                                                    .font(.system(size: 14, weight: .medium))
                                                    .foregroundColor(Color.theme.textTertiary)
                                            }
                                        }
                                    }
                                }
                            }
                            
                        } else {
                            // Wallet not available
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    Image(systemName: "exclamationmark.triangle")
                                        .font(.system(size: 40, weight: .medium))
                                        .foregroundColor(Color.theme.warning)
                                    
                                    Text("Apple Wallet Not Available")
                                        .font(Font.theme.titleMedium)
                                        .fontWeight(.semibold)
                                        .foregroundColor(Color.theme.textPrimary)
                                    
                                    Text("Apple Wallet is not available on this device or region.")
                                        .font(Font.theme.bodyMedium)
                                        .foregroundColor(Color.theme.textSecondary)
                                        .multilineTextAlignment(.center)
                                }
                            }
                        }
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.sm)
                }
            }
            .navigationTitle("Apple Wallet")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .onAppear {
                // Refresh wallet passes when view appears
                walletService.loadUserPasses()
            }
        }
    }
}

struct WalletSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        WalletSettingsView()
            .environmentObject(AuthViewModel.shared)
    }
}
