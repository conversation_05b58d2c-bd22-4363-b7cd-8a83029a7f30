import SwiftUI
import FirebaseFirestore

struct TwoFactorSetupView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var verificationCode = ""
    @State private var secret = ""
    @State private var qrCodeImage: UIImage?
    @State private var backupCodes: [String] = []
    @State private var showingBackupCodes = false
    @State private var isVerifying = false
    @State private var setupStep: SetupStep = .introduction
    @State private var errorMessage = ""
    @State private var keyIsCopied = false
    @State private var backupCodesAreCopied = false
    
    enum SetupStep {
        case introduction
        case qrCode
        case verification
        case backupCodes
        case completed
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                switch setupStep {
                case .introduction:
                    introductionView
                case .qrCode:
                    qrCodeView
                case .verification:
                    verificationView
                case .backupCodes:
                    backupCodesView
                case .completed:
                    completedView
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Two-Factor Authentication")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        cleanupTemporaryData()
                        dismiss()
                    }
                }
            }
        }
        .onAppear(perform: setup)
    }
    
    private var introductionView: some View {
        VStack(spacing: 20) {
            Image(systemName: "shield.checkered")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("Enhance Your Security")
                .font(.title)
                .fontWeight(.bold)
            
            Text("Two-factor authentication adds an extra layer of security to your account by requiring a verification code from your authenticator app.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            VStack(alignment: .leading, spacing: 10) {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Protects against unauthorized access")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Works with Google Authenticator and other apps")
                }
                
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("Backup codes for account recovery")
                }
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            Button(action: {
                setupStep = .qrCode
            }) {
                Text("Get Started")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(16)
            }
        }
    }
    
    private var qrCodeView: some View {
        VStack(spacing: 20) {
            Text("Scan QR Code")
                .font(.title2)
                .fontWeight(.bold)
            
            if let qrCodeImage = qrCodeImage {
                Image(uiImage: qrCodeImage)
                    .resizable()
                    .interpolation(.none)
                    .scaledToFit()
                    .frame(width: 250, height: 250)
                    .background(Color.white)
                    .cornerRadius(12)
                    .shadow(radius: 5)
            } else {
                ProgressView()
                    .frame(width: 250, height: 250)
            }
            
            Text("1. Open your authenticator app (Google Authenticator, Authy, etc.)")
                .font(.subheadline)
                .multilineTextAlignment(.center)
            
            Text("2. Tap the + button to add a new account")
                .font(.subheadline)
                .multilineTextAlignment(.center)
            
            Text("3. Scan this QR code OR copy the key to your authenticator app")
                .font(.subheadline)
                .multilineTextAlignment(.center)
            
            VStack(spacing: 12) {
                // Manual key entry section
                VStack(spacing: 8) {
                    Text("Manual Entry Key:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    HStack {
                        Text(secret)
                            .font(.monospaced(.caption)())
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                            .lineLimit(1)
                            .truncationMode(.middle)
                        
                        Button(action: copyKeyToClipboard) {
                            Image(systemName: keyIsCopied ? "checkmark.circle.fill" : "doc.on.doc")
                                .foregroundColor(keyIsCopied ? .green : .blue)
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.05))
                .cornerRadius(12)
                
                Button(action: copyKeyToClipboard) {
                    HStack {
                        Image(systemName: keyIsCopied ? "checkmark.circle.fill" : "doc.on.doc")
                        Text(keyIsCopied ? "Key Copied to Clipboard!" : "Copy Key to Clipboard")
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(keyIsCopied ? .green : .blue)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background((keyIsCopied ? Color.green : Color.blue).opacity(0.1))
                    .cornerRadius(10)
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(keyIsCopied ? Color.green : Color.blue, lineWidth: 1)
                    )
                }
            }
            
            Button(action: {
                setupStep = .verification
            }) {
                Text("I've Added the Account")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(16)
            }
        }
    }
    
    private var verificationView: some View {
        VStack(spacing: 20) {
            Text("Enter Verification Code")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Enter the 6-digit code from your authenticator app to verify the setup.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            TextField("000000", text: $verificationCode)
                .modifier(CustomTextFieldModifier())
                .keyboardType(.numberPad)
                .textContentType(.oneTimeCode)
                .multilineTextAlignment(.center)
                .font(.title)
                .onChange(of: verificationCode) { newValue in
                    // Limit to 6 digits
                    if newValue.count > 6 {
                        verificationCode = String(newValue.prefix(6))
                    }
                    // Auto-verify when 6 digits entered
                    if newValue.count == 6 {
                        verifyCode()
                    }
                }
            
            if !errorMessage.isEmpty {
                Text(errorMessage)
                    .foregroundColor(.red)
                    .font(.caption)
            }
            
            Button(action: verifyCode) {
                HStack {
                    if isVerifying {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                    Text(isVerifying ? "Verifying..." : "Verify Code")
                }
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding()
                .background(verificationCode.count == 6 ? Color.green : Color.gray)
                .cornerRadius(16)
            }
            .disabled(verificationCode.count != 6 || isVerifying)
            
            Button("Go Back") {
                setupStep = .qrCode
                errorMessage = ""
            }
            .foregroundColor(.blue)
        }
    }
    
    private var backupCodesView: some View {
        VStack(spacing: 20) {
            Text("Save Your Backup Codes")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("These codes can be used to access your account if you lose your authenticator device. Save them in a secure location.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            VStack(spacing: 8) {
                ForEach(Array(backupCodes.enumerated()), id: \.offset) { index, code in
                    HStack {
                        Text("\(index + 1).")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(code)
                            .font(.monospaced(.body)())
                            .fontWeight(.medium)
                        Spacer()
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 4)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.05))
            .cornerRadius(12)
            
            Button(action: copyBackupCodesToClipboard) {
                HStack {
                    Image(systemName: backupCodesAreCopied ? "checkmark.circle.fill" : "doc.on.doc")
                    Text(backupCodesAreCopied ? "Backup Codes Copied!" : "Copy All Backup Codes")
                }
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(backupCodesAreCopied ? .green : .blue)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background((backupCodesAreCopied ? Color.green : Color.blue).opacity(0.1))
                .cornerRadius(10)
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(backupCodesAreCopied ? Color.green : Color.blue, lineWidth: 1)
                )
            }
            
            Button(action: {
                setupStep = .completed
            }) {
                Text("I've Saved My Backup Codes")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .cornerRadius(16)
            }
        }
    }
    
    private var completedView: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.green)
            
            Text("Setup Complete!")
                .font(.title)
                .fontWeight(.bold)
            
            Text("Two-factor authentication has been successfully enabled for your account. Your account is now more secure.")
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
            
            Button(action: {
                cleanupTemporaryData()
                dismiss()
            }) {
                Text("Done")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(16)
            }
        }
    }
    
    private func setup() {
        guard let userId = authViewModel.userSession?.uid else { 
            print("DEBUG: No user session found in TwoFactorSetupView")
            dismiss()
            return 
        }
        
        print("DEBUG: TwoFactorSetupView setup for user: \(userId)")
        print("DEBUG: Current user 2FA enabled: \(authViewModel.currentUser?.twoFactorEnabled ?? false)")
        print("DEBUG: Has existing secret: \(TOTPService.shared.hasExistingSecret(for: userId))")
        
        // Check if user already has 2FA enabled in Firestore AND has a secret in keychain
        if authViewModel.currentUser?.twoFactorEnabled == true && TOTPService.shared.hasExistingSecret(for: userId) {
            print("DEBUG: User already has 2FA fully enabled, dismissing setup")
            dismiss()
            return
        }

        // If user has 2FA enabled in Firestore but no secret in keychain, allow re-setup
        // This handles cases where the keychain was cleared but Firestore still shows enabled
        if authViewModel.currentUser?.twoFactorEnabled == true && !TOTPService.shared.hasExistingSecret(for: userId) {
            print("DEBUG: User has 2FA enabled in Firestore but no keychain secret, allowing re-setup")
        }
        
        // Only generate new secret and backup codes if we don't already have them
        if secret.isEmpty {
            // Check if there's a temporary setup secret in keychain
            let tempSecretKey = "temp_totp_setup_\(userId)"
            if let existingSecretData = KeychainService.shared.load(key: tempSecretKey),
               let existingSecret = String(data: existingSecretData, encoding: .utf8) {
                print("DEBUG: Using existing temporary setup secret")
                self.secret = existingSecret
            } else {
                print("DEBUG: Generating new secret for setup")
                self.secret = TOTPService.shared.generateSecret()
                // Save temporary secret for this setup session
                if let secretData = self.secret.data(using: .utf8) {
                    _ = KeychainService.shared.save(key: tempSecretKey, data: secretData)
                }
            }
        }
        
        if backupCodes.isEmpty {
            // Check if there are temporary backup codes
            let tempCodesKey = "temp_backup_codes_\(userId)"
            if let existingCodesData = KeychainService.shared.load(key: tempCodesKey),
               let existingCodes = try? JSONDecoder().decode([String].self, from: existingCodesData) {
                print("DEBUG: Using existing temporary backup codes")
                self.backupCodes = existingCodes
            } else {
                print("DEBUG: Generating new backup codes for setup")
                self.backupCodes = TOTPService.shared.generateBackupCodes()
                // Save temporary backup codes for this setup session
                if let codesData = try? JSONEncoder().encode(self.backupCodes) {
                    _ = KeychainService.shared.save(key: tempCodesKey, data: codesData)
                }
            }
        }
        
        guard let userEmail = authViewModel.currentUser?.email else { 
            print("DEBUG: No user email found")
            return 
        }
        
        // Only generate QR code if we don't already have one
        if qrCodeImage == nil {
            print("DEBUG: Generating QR code for email: \(userEmail)")
            self.qrCodeImage = TOTPService.shared.generateQRCode(for: userEmail, secret: secret)
        }
    }
    
    private func copyKeyToClipboard() {
        UIPasteboard.general.string = secret
        keyIsCopied = true
        
        // Reset the copied state after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            keyIsCopied = false
        }
    }
    
    private func copyBackupCodesToClipboard() {
        let backupCodesText = backupCodes.enumerated().map { index, code in
            "\(index + 1). \(code)"
        }.joined(separator: "\n")
        
        UIPasteboard.general.string = backupCodesText
        backupCodesAreCopied = true
        
        // Reset the copied state after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            backupCodesAreCopied = false
        }
    }
    
    private func verifyCode() {
        guard !isVerifying else { return }
        
        isVerifying = true
        errorMessage = ""
        
        // Simulate network delay for better UX
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            guard let userId = authViewModel.userSession?.uid else {
                errorMessage = "User not found"
                isVerifying = false
                return
            }
            
            // Verify the TOTP code using the temporary secret
            let isValid = TOTPService.shared.verifyTOTP(code: verificationCode, secret: secret)
            
            if isValid {
                // Save the secret and enable 2FA in Firebase
                self.enableTwoFactorAuthentication(for: userId)
            } else {
                errorMessage = "Invalid verification code. Please try again."
                isVerifying = false
            }
        }
    }
    
    private func enableTwoFactorAuthentication(for userId: String) {
        // Save secret securely in Keychain
        switch TOTPService.shared.saveSecret(secret, for: userId) {
        case .success:
            // Update user's 2FA status in Firestore
            let db = Firestore.firestore()
            db.collection("users").document(userId).updateData([
                "twoFactorEnabled": true,
                "twoFactorSetupDate": Date()
            ]) { error in
                DispatchQueue.main.async {
                    if let error = error {
                        self.errorMessage = "Failed to enable 2FA: \(error.localizedDescription)"
                        self.isVerifying = false
                        print("DEBUG: Failed to update twoFactorEnabled in Firebase: \(error.localizedDescription)")
                    } else {
                        print("DEBUG: Successfully enabled 2FA in Firebase for user: \(userId)")

                        // Save backup codes
                        _ = TOTPService.shared.saveBackupCodes(self.backupCodes, for: userId)

                        // Update local user state
                        let setupDate = Date()
                        self.authViewModel.updateLocalTwoFactorState(enabled: true, setupDate: setupDate)

                        // Refresh user data from Firestore to ensure UI reflects the change
                        self.authViewModel.refreshCurrentUser()

                        // Log security event
                        SecurityAuditService.shared.logSecurityEvent(.twoFactorEnabled, for: userId, metadata: [
                            "backup_codes_generated": self.backupCodes.count
                        ])

                        // Send notification
                        NotificationService.shared.notify2FAEnabled()

                        // Clean up temporary data since setup is successful
                        self.cleanupTemporaryData()

                        // Move to backup codes step
                        self.setupStep = .backupCodes
                        self.isVerifying = false
                    }
                }
            }
        case .failure(let error):
            DispatchQueue.main.async {
                self.errorMessage = "Failed to save authentication secret: \(error.localizedDescription)"
                self.isVerifying = false
                print("DEBUG: Failed to save TOTP secret to keychain: \(error.localizedDescription)")
            }
        }
    }
    
    private func cleanupTemporaryData() {
        guard let userId = authViewModel.userSession?.uid else { return }
        
        print("DEBUG: Cleaning up temporary 2FA setup data for user: \(userId)")
        
        // Remove temporary secret and backup codes from keychain
        let tempSecretKey = "temp_totp_setup_\(userId)"
        let tempCodesKey = "temp_backup_codes_\(userId)"
        
        _ = KeychainService.shared.delete(key: tempSecretKey)
        _ = KeychainService.shared.delete(key: tempCodesKey)
        
        print("DEBUG: Temporary 2FA setup data cleaned up")
    }
}

// MARK: - Preview
struct TwoFactorSetupView_Previews: PreviewProvider {
    static var previews: some View {
        TwoFactorSetupView()
            .environmentObject(AuthViewModel.shared)
    }
}
