import SwiftUI

struct PhoneNumberPromptView: View {
    @State private var phoneNumber = ""
    @State private var isLoading = false
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) var dismiss
    
    var isValidPhoneNumber: Bool {
        let phoneRegex = "^[+]?[0-9]{10,15}$"
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        return phonePredicate.evaluate(with: phoneNumber.replacingOccurrences(of: " ", with: "").replacingOccurrences(of: "-", with: ""))
    }
    
    var body: some View {
        ZStack {
            Color.theme.background.edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 30) {
                Spacer()
                
                VStack(spacing: 16) {
                    Image(systemName: "phone.circle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(Color.theme.accent)
                    
                    Text("Phone Number Required")
                        .font(.system(size: 28, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                    
                    Text("To help you connect with contacts and improve your experience, please add your phone number.")
                        .font(.system(.body, design: .rounded))
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                
                VStack(spacing: 20) {
                    TextField("Phone Number", text: $phoneNumber)
                        .modifier(CustomTextFieldModifier())
                        .keyboardType(.phonePad)
                        .padding(.horizontal, 32)
                    
                    Button(action: {
                        updatePhoneNumber()
                    }) {
                        HStack(spacing: 8) {
                            if isLoading {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            } else {
                                Image(systemName: "checkmark")
                                    .font(.system(.body, design: .rounded, weight: .medium))
                            }
                            
                            Text(isLoading ? "Updating..." : "Continue")
                                .font(.system(.headline, design: .rounded, weight: .semibold))
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(isValidPhoneNumber && !isLoading ? Color.theme.accent : Color.gray)
                        .cornerRadius(16)
                    }
                    .disabled(!isValidPhoneNumber || isLoading)
                    .padding(.horizontal, 32)
                    
                    if let errorMessage = authViewModel.errorMessage {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .font(.system(.caption, design: .rounded))
                            .padding(.horizontal, 32)
                    }
                }
                
                Spacer()
                
                Button(action: {
                    dismiss()
                }) {
                    Text("Skip for now")
                        .font(.system(.subheadline, design: .rounded))
                        .foregroundColor(.secondary)
                        .underline()
                }
                .padding(.bottom, 32)
            }
        }
        .interactiveDismissDisabled()
        .onChange(of: authViewModel.successMessage) { _, successMessage in
            if successMessage != nil {
                dismiss()
            }
        }
    }
    
    private func updatePhoneNumber() {
        guard isValidPhoneNumber else {
            authViewModel.errorMessage = "Please enter a valid phone number."
            return
        }
        
        isLoading = true
        authViewModel.errorMessage = nil
        authViewModel.updatePhoneNumber(phoneNumber)
        
        // Reset loading state after a delay if no success message
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            if authViewModel.successMessage == nil {
                isLoading = false
            }
        }
    }
}

struct PhoneNumberPromptView_Previews: PreviewProvider {
    static var previews: some View {
        PhoneNumberPromptView()
            .environmentObject(AuthViewModel.shared)
    }
}
