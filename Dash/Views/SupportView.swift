import SwiftUI
import SafariServices

struct SupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isShowingSafari = false
    @State private var isShowingManualCodeEntry = false
    @State private var manualSplitCode = ""
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.surfaceSecondary.ignoresSafeArea(.all)
                
                ScrollView(showsIndicators: false) {
                    VStack(spacing: Spacing.lg) {
                        // Contact Support Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Get Help")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Button(action: {
                                isShowingSafari = true
                            }) {
                                ModernCard(padding: Spacing.lg) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "questionmark.circle.fill")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.primary)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Contact Support")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)
                                            
                                            Text("Get help with your account or report issues")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Image(systemName: "arrow.up.right")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(Color.theme.textTertiary)
                                    }
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        
                        // FAQ Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Frequently Asked Questions")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            VStack(spacing: Spacing.xs) {
                                faqItem(
                                    question: "How do I add money to my account?",
                                    answer: "Tap 'Add Funds' on the dashboard and follow the prompts to link your bank account or card."
                                )
                                
                                faqItem(
                                    question: "How do splits work?",
                                    answer: "Create a split by tapping 'Split Bill', add participants, and they'll receive a notification to pay their share."
                                )
                                
                                faqItem(
                                    question: "Is my money safe?",
                                    answer: "Yes, we use bank-level security and your funds are protected by Australian financial regulations."
                                )
                                
                                faqItem(
                                    question: "How do I upgrade my subscription?",
                                    answer: "Go to Settings > Subscription to view and upgrade your plan for additional features."
                                )
                            }
                        }
                        
                        // Developer Tools Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("Developer Tools")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            Button(action: { isShowingManualCodeEntry = true }) {
                                ModernCard(padding: Spacing.lg) {
                                    HStack(spacing: Spacing.md) {
                                        Image(systemName: "qrcode.viewfinder")
                                            .font(.system(size: 20, weight: .medium))
                                            .foregroundColor(Color.theme.info)
                                            .frame(width: 24)

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text("Enter Dash Code")
                                                .font(Font.theme.bodyMedium)
                                                .fontWeight(.medium)
                                                .foregroundColor(Color.theme.textPrimary)

                                            Text("Manually join a split with a code")
                                                .font(Font.theme.labelMedium)
                                                .foregroundColor(Color.theme.textSecondary)
                                        }

                                        Spacer()

                                        Image(systemName: "chevron.right")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(Color.theme.textTertiary)
                                    }
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        
                        // App Information Section
                        VStack(alignment: .leading, spacing: Spacing.sm) {
                            Text("App Information")
                                .font(Font.theme.titleMedium)
                                .fontWeight(.semibold)
                                .foregroundColor(Color.theme.textPrimary)
                            
                            ModernCard(padding: Spacing.lg) {
                                VStack(spacing: Spacing.md) {
                                    HStack {
                                        Text("Version")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                        
                                        Spacer()
                                        
                                        Text("1.0.0")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }
                                    
                                    Divider()
                                        .background(Color.theme.neutral200)
                                    
                                    HStack {
                                        Text("Build")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textPrimary)
                                        
                                        Spacer()
                                        
                                        Text("100")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.textSecondary)
                                    }
                                    
                                    Divider()
                                        .background(Color.theme.neutral200)
                                    
                                    HStack {
                                        Text("Terms of Service")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.primary)
                                        
                                        Spacer()
                                        
                                        Image(systemName: "arrow.up.right")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(Color.theme.textTertiary)
                                    }
                                    
                                    Divider()
                                        .background(Color.theme.neutral200)
                                    
                                    HStack {
                                        Text("Privacy Policy")
                                            .font(Font.theme.bodyMedium)
                                            .foregroundColor(Color.theme.primary)
                                        
                                        Spacer()
                                        
                                        Image(systemName: "arrow.up.right")
                                            .font(.system(size: 14, weight: .medium))
                                            .foregroundColor(Color.theme.textTertiary)
                                    }
                                }
                            }
                        }
                        
                        // Bottom spacing
                        Color.clear.frame(height: Spacing.xl)
                    }
                    .padding(.horizontal, Spacing.lg)
                    .padding(.top, Spacing.sm)
                }
            }
            .navigationTitle("Support")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.primary)
                }
            }
            .sheet(isPresented: $isShowingSafari) {
                SafariView(url: URL(string: "https://dashfinanceapp.freshdesk.com/support/tickets/new")!)
            }
            .sheet(isPresented: $isShowingManualCodeEntry) {
                ManualCodeEntryView(splitCode: $manualSplitCode)
            }
        }
    }
    
    private func faqItem(question: String, answer: String) -> some View {
        ModernCard(padding: Spacing.lg) {
            VStack(alignment: .leading, spacing: Spacing.sm) {
                Text(question)
                    .font(Font.theme.bodyMedium)
                    .fontWeight(.medium)
                    .foregroundColor(Color.theme.textPrimary)
                
                Text(answer)
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.textSecondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
    }
}

// MARK: - Safari View Wrapper
struct SafariView: UIViewControllerRepresentable {
    let url: URL

    func makeUIViewController(context: Context) -> SFSafariViewController {
        let safariViewController = SFSafariViewController(url: url)
        safariViewController.preferredControlTintColor = UIColor.systemBlue
        return safariViewController
    }

    func updateUIViewController(_ uiViewController: SFSafariViewController, context: Context) {
        // No updates needed
    }
}

struct SupportView_Previews: PreviewProvider {
    static var previews: some View {
        SupportView()
    }
}
