<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.46095870775-lbm74us5t9evk4g3objvamfes6sre05k</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>dash</string>
			</array>
		</dict>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>This app uses the camera to scan QR codes for splitting bills.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>This app uses Face ID to securely log you in and authorize transactions.</string>
	<key>NSKeychainUsageDescription</key>
	<string>This app uses the Keychain to securely store your two-factor authentication secrets, backup codes, and other sensitive security information to protect your account.</string>
	<key>NSContactsUsageDescription</key>
	<string>This app needs access to your contacts to find friends you can send money to or request money from.</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>NSUserNotificationsUsageDescription</key>
	<string>This app sends notifications to keep you updated on transactions, money requests, and split activities.</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
</dict>
</plist>
