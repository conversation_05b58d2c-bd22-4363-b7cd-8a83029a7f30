import Foundation
import PassKit
import UIKit
import Combine

@MainActor
class WalletPassService: NSObject, ObservableObject {
    static let shared = WalletPassService()

    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    @Published var userPasses: [PKPass] = []

    private let baseURL = "http://192.168.68.74:3001"
    private var cancellables = Set<AnyCancellable>()

    private override init() {
        super.init()

        // Load passes on init
        if isWalletAvailable() {
            loadUserPasses()
        }
    }
    
    // MARK: - Public Methods
    
    /// Check if Apple Wallet is available on this device
    func isWalletAvailable() -> Bool {
        return PKPassLibrary.isPassLibraryAvailable()
    }
    
    /// Generate and add pass to wallet
    func generateAndAddPass(for user: User) async {
        guard isWalletAvailable() else {
            await setError("Apple Wallet is not available on this device")
            return
        }

        await setLoading(true)
        await clearMessages()

        do {
            let passData = try await generatePass(for: user)

            // Add a small delay to ensure any existing modals are dismissed
            try await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

            await addPassToWallet(passData: passData)
        } catch {
            await setError("Failed to generate pass: \(error.localizedDescription)")
        }

        await setLoading(false)
    }
    
    /// Check if user already has a Dash pass in their wallet
    func hasExistingPass(for userId: String) -> Bool {
        let passLibrary = PKPassLibrary()
        let passes = passLibrary.passes()
        
        return passes.contains { pass in
            // Check if this is a Dash pass for this user
            return pass.passTypeIdentifier == "pass.com.dashpay.card" &&
                   pass.serialNumber.contains(userId)
        }
    }
    
    /// Remove existing Dash passes for user
    func removeExistingPasses(for userId: String) {
        let passLibrary = PKPassLibrary()
        let passes = passLibrary.passes()

        let dashPasses = passes.filter { pass in
            return pass.passTypeIdentifier == "pass.com.dashpay.card" &&
                   pass.serialNumber.contains(userId)
        }

        for pass in dashPasses {
            passLibrary.removePass(pass)
        }

        // Reload passes after removal
        loadUserPasses()
    }

    /// Open the Apple Wallet app
    func openWalletApp() {
        guard isWalletAvailable() else {
            print("Apple Wallet is not available on this device")
            return
        }

        // Try different URL schemes for opening Wallet
        let walletURLs = [
            "shoebox://", // Primary Wallet URL scheme
            "wallet://",  // Alternative scheme
        ]

        for urlString in walletURLs {
            if let url = URL(string: urlString),
               UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url, options: [:]) { success in
                    if success {
                        print("Successfully opened Apple Wallet")
                    } else {
                        print("Failed to open Apple Wallet with URL: \(urlString)")
                    }
                }
                return
            }
        }

        print("Could not open Apple Wallet - no supported URL schemes available")
    }

    /// Load user's Dash passes from wallet
    func loadUserPasses() {
        guard isWalletAvailable() else { return }

        let passLibrary = PKPassLibrary()
        let allPasses = passLibrary.passes()

        userPasses = allPasses.filter { pass in
            return pass.passTypeIdentifier == "pass.com.dashpay.card"
        }
    }

    /// Get pass details for display
    func getPassDetails(for pass: PKPass) -> PassDetails? {
        // For now, we'll extract basic info from the pass
        // In a real implementation, you might parse the pass data more thoroughly

        return PassDetails(
            serialNumber: pass.serialNumber,
            balance: 0.0, // Would need to parse from pass data
            userName: "Dash User", // Would need to parse from pass data
            cardNumber: "**** **** **** ****", // Would need to parse from pass data
            lastUpdated: pass.relevantDate ?? Date()
        )
    }
    
    // MARK: - Private Methods
    
    private func generatePass(for user: User) async throws -> Data {
        guard let url = URL(string: "\(baseURL)/generate-pass") else {
            throw WalletPassError.invalidURL
        }
        
        let requestBody: [String: Any] = [
            "userId": user.uid,
            "userName": user.displayName ?? "Dash User",
            "userEmail": user.email ?? "",
            "balance": user.balance ?? 0,
            "membershipTier": "Classic"
        ]
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw WalletPassError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            // Try to parse error message
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = errorData["error"] as? String {
                throw WalletPassError.serverError(errorMessage)
            }
            throw WalletPassError.serverError("Server returned status code: \(httpResponse.statusCode)")
        }
        
        return data
    }
    
    private func addPassToWallet(passData: Data) async {
        do {
            let pass = try PKPass(data: passData)

            let passLibrary = PKPassLibrary()

            // Check if pass can be added
            guard passLibrary.containsPass(pass) == false else {
                await setError("This pass is already in your wallet")
                return
            }

            // Present add pass view controller
            await presentAddPassViewController(for: pass)

        } catch {
            await setError("Failed to create pass: \(error.localizedDescription)")
        }
    }
    
    private func presentAddPassViewController(for pass: PKPass) async {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            await setError("Unable to present pass")
            return
        }

        let addPassViewController = PKAddPassesViewController(pass: pass)
        addPassViewController?.delegate = PassViewControllerDelegate(service: self)

        if let addPassViewController = addPassViewController {
            await MainActor.run {
                // Find the topmost view controller to present from
                var topViewController = window.rootViewController
                while let presentedViewController = topViewController?.presentedViewController {
                    topViewController = presentedViewController
                }

                // Only present if no other modal is being presented
                if topViewController?.presentedViewController == nil {
                    topViewController?.present(addPassViewController, animated: true)
                } else {
                    // Dismiss any existing modal first, then present
                    topViewController?.dismiss(animated: false) {
                        topViewController?.present(addPassViewController, animated: true)
                    }
                }
            }
        } else {
            await setError("Unable to create add pass view controller")
        }
    }
    
    // MARK: - State Management
    
    private func setLoading(_ loading: Bool) async {
        await MainActor.run {
            self.isLoading = loading
        }
    }
    
    private func setError(_ message: String) async {
        await MainActor.run {
            self.errorMessage = message
            self.successMessage = nil
        }
    }
    
    private func setSuccess(_ message: String) async {
        await MainActor.run {
            self.successMessage = message
            self.errorMessage = nil
        }
    }
    
    private func clearMessages() async {
        await MainActor.run {
            self.errorMessage = nil
            self.successMessage = nil
        }
    }

    // MARK: - Pass Update Methods

    /// Update pass with new balance
    func updatePass(for userId: String, newBalance: Double) async {
        guard isWalletAvailable() else { return }

        let passLibrary = PKPassLibrary()
        let passes = passLibrary.passes()

        let userPasses = passes.filter { pass in
            return pass.passTypeIdentifier == "pass.com.dashpay.card" &&
                   pass.serialNumber.contains(userId)
        }

        guard !userPasses.isEmpty else {
            // No passes to update
            return
        }

        // For each pass, request an updated version from the server
        for pass in userPasses {
            do {
                let updatedPassData = try await requestPassUpdate(
                    serialNumber: pass.serialNumber,
                    balance: newBalance
                )

                // In iOS, passes are automatically updated by the system
                // We would typically just trigger a server-side update
                print("Pass update requested for: \(pass.serialNumber)")
            } catch {
                print("Failed to update pass: \(error.localizedDescription)")
            }
        }
    }

    /// Request updated pass from server
    private func requestPassUpdate(serialNumber: String, balance: Double) async throws -> Data {
        guard let url = URL(string: "\(baseURL)/passes/\(serialNumber)/update") else {
            throw WalletPassError.invalidURL
        }

        let requestBody: [String: Any] = [
            "balance": balance
        ]

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
            throw WalletPassError.serverError("Failed to update pass")
        }

        return data
    }
}

// MARK: - Error Types

enum WalletPassError: LocalizedError {
    case invalidURL
    case invalidResponse
    case serverError(String)
    case passCreationFailed

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid server URL"
        case .invalidResponse:
            return "Invalid response from server"
        case .serverError(let message):
            return message
        case .passCreationFailed:
            return "Failed to create wallet pass"
        }
    }
}

// MARK: - Pass Details Model

struct PassDetails {
    let serialNumber: String
    let balance: Double
    let userName: String
    let cardNumber: String
    let lastUpdated: Date
}

// MARK: - PKAddPassesViewControllerDelegate

class PassViewControllerDelegate: NSObject, PKAddPassesViewControllerDelegate {
    private let service: WalletPassService

    init(service: WalletPassService) {
        self.service = service
    }

    func addPassesViewControllerDidFinish(_ controller: PKAddPassesViewController) {
        controller.dismiss(animated: true) {
            Task {
                await MainActor.run {
                    self.service.successMessage = "Pass added to wallet successfully!"
                    self.service.errorMessage = nil
                    // Reload passes to update the UI
                    self.service.loadUserPasses()
                }
            }
        }
    }
}
