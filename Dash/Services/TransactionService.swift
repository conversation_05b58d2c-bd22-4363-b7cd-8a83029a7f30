import Foundation
import FirebaseFirestore
import FirebaseAuth
import Combine

enum TransactionError: LocalizedError {
    case userNotAuthenticated
    case insufficientFunds
    case userNotFound
    case invalidAmount
    case transactionFailed(String)
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .userNotAuthenticated:
            return "User not authenticated"
        case .insufficientFunds:
            return "Insufficient funds"
        case .userNotFound:
            return "User not found"
        case .invalidAmount:
            return "Invalid amount"
        case .transactionFailed(let message):
            return "Transaction failed: \(message)"
        case .networkError:
            return "Network error occurred"
        }
    }
}

class TransactionService: ObservableObject {
    static let shared = TransactionService()
    
    @Published var transactions: [Transaction] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private var transactionListener: ListenerRegistration?
    private let db = Firestore.firestore()
    private let auth = Auth.auth()
    
    private init() {}
    
    // MARK: - Transaction Loading
    func loadTransactions() {
        guard let userId = auth.currentUser?.uid else {
            print("TransactionService: No authenticated user")
            return
        }
        
        print("TransactionService: Loading transactions for user: \(userId)")
        isLoading = true
        
        // Remove any existing listener
        transactionListener?.remove()
        
        // Create new listener
        transactionListener = db
            .collection("users")
            .document(userId)
            .collection("transactions")
            .addSnapshotListener { [weak self] snapshot, error in
                DispatchQueue.main.async {
                    guard let self = self else { return }
                    
                    self.isLoading = false
                    
                    if let error = error {
                        self.errorMessage = error.localizedDescription
                        return
                    }
                    
                    guard let documents = snapshot?.documents else {
                        self.transactions = []
                        return
                    }
                    
                    let previousTransactionCount = self.transactions.count
                    let group = DispatchGroup()
                    var fetchedTransactions: [Transaction] = []
                    
                    for doc in documents {
                        let docData = doc.data()
                        
                        if let transactionRef = docData["transactionRef"] as? DocumentReference {
                            group.enter()
                            transactionRef.getDocument { transactionDoc, error in
                                if let transactionDoc = transactionDoc, transactionDoc.exists {
                                    if let transaction = self.safeDecodeTransaction(from: transactionDoc) {
                                        fetchedTransactions.append(transaction)
                                    }
                                }
                                group.leave()
                            }
                        } else {
                            if let transaction = self.safeDecodeTransaction(from: doc) {
                                fetchedTransactions.append(transaction)
                            }
                        }
                    }
                    
                    group.notify(queue: .main) {
                        self.filterValidTransactions(fetchedTransactions) { validTransactions in
                            DispatchQueue.main.async {
                                self.transactions = validTransactions.sorted(by: { $0.date.dateValue() > $1.date.dateValue() })
                                
                                if previousTransactionCount > 0 && self.transactions.count > previousTransactionCount {
                                    let newTransactions = Array(self.transactions.prefix(self.transactions.count - previousTransactionCount))
                                    for transaction in newTransactions {
                                        if transaction.name == "Money Received" && transaction.type == .transfer {
                                            NotificationService.shared.notifyMoneyReceived(
                                                amount: transaction.amount,
                                                from: transaction.senderName ?? "Someone"
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    }
    
    
    // MARK: - Send Money
    func sendMoney(to recipientId: String, amount: Double, description: String, notes: String? = nil) async throws -> String {
        print("🚀 FIREBASE_DEBUG: ========== STARTING MONEY TRANSFER ==========")
        print("🚀 FIREBASE_DEBUG: Recipient ID: \(recipientId)")
        print("🚀 FIREBASE_DEBUG: Amount: \(amount)")
        print("🚀 FIREBASE_DEBUG: Description: \(description)")
        print("🚀 FIREBASE_DEBUG: Notes: \(notes ?? "nil")")
        
        guard let currentUser = auth.currentUser else {
            print("❌ FIREBASE_DEBUG: User not authenticated")
            throw TransactionError.userNotAuthenticated
        }
        
        print("🚀 FIREBASE_DEBUG: Current user authenticated")
        print("🚀 FIREBASE_DEBUG: User ID: \(currentUser.uid)")
        print("🚀 FIREBASE_DEBUG: User email: \(currentUser.email ?? "no email")")
        print("🚀 FIREBASE_DEBUG: User display name: \(currentUser.displayName ?? "no display name")")
        print("🚀 FIREBASE_DEBUG: User is anonymous: \(currentUser.isAnonymous)")
        print("🚀 FIREBASE_DEBUG: User email verified: \(currentUser.isEmailVerified)")
        
        // Check auth token
        do {
            let idToken = try await currentUser.getIDToken()
            print("🚀 FIREBASE_DEBUG: Successfully retrieved ID token (length: \(idToken.count))")
        } catch {
            print("❌ FIREBASE_DEBUG: Failed to get ID token: \(error)")
        }
        
        guard amount > 0 else {
            print("❌ FIREBASE_DEBUG: Invalid amount: \(amount)")
            throw TransactionError.invalidAmount
        }
        
        let senderId = currentUser.uid
        let transactionId = UUID().uuidString
        let transactionHash = generateTransactionHash(senderId: senderId, recipientId: recipientId, amount: amount)
        
        print("🚀 FIREBASE_DEBUG: Generated transaction ID: \(transactionId)")
        print("🚀 FIREBASE_DEBUG: Generated transaction hash: \(transactionHash)")
        print("🚀 FIREBASE_DEBUG: Sender ID: \(senderId)")
        
        // Pre-check: Verify both users exist before starting transaction
        print("🚀 FIREBASE_DEBUG: Pre-checking user documents...")
        do {
            let senderDoc = try await db.collection("users").document(senderId).getDocument()
            let recipientDoc = try await db.collection("users").document(recipientId).getDocument()
            
            print("🚀 FIREBASE_DEBUG: Sender document exists: \(senderDoc.exists)")
            print("🚀 FIREBASE_DEBUG: Recipient document exists: \(recipientDoc.exists)")
            
            if let senderData = senderDoc.data() {
                print("🚀 FIREBASE_DEBUG: Sender data keys: \(Array(senderData.keys))")
                print("🚀 FIREBASE_DEBUG: Sender balance: \(senderData["balance"] ?? "missing")")
                print("🚀 FIREBASE_DEBUG: Sender display name: \(senderData["displayName"] ?? "missing")")
            }
            
            if let recipientData = recipientDoc.data() {
                print("🚀 FIREBASE_DEBUG: Recipient data keys: \(Array(recipientData.keys))")
                print("🚀 FIREBASE_DEBUG: Recipient balance: \(recipientData["balance"] ?? "missing")")
                print("🚀 FIREBASE_DEBUG: Recipient display name: \(recipientData["displayName"] ?? "missing")")
            }
        } catch {
            print("❌ FIREBASE_DEBUG: Pre-check failed: \(error)")
        }
        
        print("🚀 FIREBASE_DEBUG: Starting Firestore transaction...")
        
        do {
            let result = try await db.runTransaction { transaction, errorPointer in
                print("🚀 FIREBASE_DEBUG: Inside Firestore transaction block")
                
                // Get sender and recipient documents
                let senderRef = self.db.collection("users").document(senderId)
                let recipientRef = self.db.collection("users").document(recipientId)
                
                print("🚀 FIREBASE_DEBUG: Created document references")
                print("🚀 FIREBASE_DEBUG: Sender ref path: \(senderRef.path)")
                print("🚀 FIREBASE_DEBUG: Recipient ref path: \(recipientRef.path)")
                
                let senderDoc: DocumentSnapshot
                let recipientDoc: DocumentSnapshot
                
                print("🚀 FIREBASE_DEBUG: Attempting to fetch documents within transaction...")
                
                do {
                    senderDoc = try transaction.getDocument(senderRef)
                    print("🚀 FIREBASE_DEBUG: Successfully fetched sender document")
                    print("🚀 FIREBASE_DEBUG: Sender doc exists: \(senderDoc.exists)")
                    print("🚀 FIREBASE_DEBUG: Sender doc metadata: \(senderDoc.metadata)")
                    
                    recipientDoc = try transaction.getDocument(recipientRef)
                    print("🚀 FIREBASE_DEBUG: Successfully fetched recipient document")
                    print("🚀 FIREBASE_DEBUG: Recipient doc exists: \(recipientDoc.exists)")
                    print("🚀 FIREBASE_DEBUG: Recipient doc metadata: \(recipientDoc.metadata)")
                } catch let fetchError as NSError {
                    print("❌ FIREBASE_DEBUG: Error fetching documents: \(fetchError)")
                    print("❌ FIREBASE_DEBUG: Error domain: \(fetchError.domain)")
                    print("❌ FIREBASE_DEBUG: Error code: \(fetchError.code)")
                    print("❌ FIREBASE_DEBUG: Error userInfo: \(fetchError.userInfo)")
                    errorPointer?.pointee = fetchError
                    return nil
                }
                
                guard senderDoc.exists, recipientDoc.exists else {
                    print("❌ FIREBASE_DEBUG: One or both documents don't exist")
                    print("❌ FIREBASE_DEBUG: Sender exists: \(senderDoc.exists), Recipient exists: \(recipientDoc.exists)")
                    errorPointer?.pointee = NSError(domain: "TransactionService", code: -1, userInfo: [NSLocalizedDescriptionKey: "User not found"])
                    return nil
                }
                
                print("🚀 FIREBASE_DEBUG: Both documents exist, extracting data...")
                
                // Validate sender balance
                guard let senderData = senderDoc.data() else {
                    print("❌ FIREBASE_DEBUG: Could not extract sender data")
                    errorPointer?.pointee = NSError(domain: "TransactionService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Could not extract sender data"])
                    return nil
                }
                
                guard let senderBalance = senderData["balance"] as? Double else {
                    print("❌ FIREBASE_DEBUG: Could not extract sender balance")
                    print("❌ FIREBASE_DEBUG: Sender balance field: \(senderData["balance"] ?? "missing")")
                    print("❌ FIREBASE_DEBUG: Sender balance type: \(type(of: senderData["balance"]))")
                    errorPointer?.pointee = NSError(domain: "TransactionService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Could not extract sender balance"])
                    return nil
                }
                
                print("🚀 FIREBASE_DEBUG: Sender balance: \(senderBalance)")
                print("🚀 FIREBASE_DEBUG: Transfer amount: \(amount)")
                print("🚀 FIREBASE_DEBUG: Sufficient funds: \(senderBalance >= amount)")
                
                guard senderBalance >= amount else {
                    print("❌ FIREBASE_DEBUG: Insufficient funds - Balance: \(senderBalance), Required: \(amount)")
                    errorPointer?.pointee = NSError(domain: "TransactionService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Insufficient funds"])
                    return nil
                }
                
                // Get recipient info
                guard let recipientData = recipientDoc.data() else {
                    print("❌ FIREBASE_DEBUG: Could not extract recipient data")
                    errorPointer?.pointee = NSError(domain: "TransactionService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Could not extract recipient data"])
                    return nil
                }
                
                guard let recipientBalance = recipientData["balance"] as? Double else {
                    print("❌ FIREBASE_DEBUG: Could not extract recipient balance")
                    print("❌ FIREBASE_DEBUG: Recipient balance field: \(recipientData["balance"] ?? "missing")")
                    print("❌ FIREBASE_DEBUG: Recipient balance type: \(type(of: recipientData["balance"]))")
                    errorPointer?.pointee = NSError(domain: "TransactionService", code: -1, userInfo: [NSLocalizedDescriptionKey: "Could not retrieve recipient balance"])
                    return nil
                }
                
                let recipientName = recipientData["displayName"] as? String ?? "Unknown"
                let senderName = senderData["displayName"] as? String ?? "Unknown"
                
                print("🚀 FIREBASE_DEBUG: Extracted user info:")
                print("🚀 FIREBASE_DEBUG: Sender name: \(senderName)")
                print("🚀 FIREBASE_DEBUG: Sender balance: \(senderBalance)")
                print("🚀 FIREBASE_DEBUG: Recipient name: \(recipientName)")
                print("🚀 FIREBASE_DEBUG: Recipient balance: \(recipientBalance)")
                
                let newSenderBalance = senderBalance - amount
                let newRecipientBalance = recipientBalance + amount
                
                print("🚀 FIREBASE_DEBUG: Calculated new balances:")
                print("🚀 FIREBASE_DEBUG: New sender balance: \(newSenderBalance)")
                print("🚀 FIREBASE_DEBUG: New recipient balance: \(newRecipientBalance)")
                
                // Update balances
                print("🚀 FIREBASE_DEBUG: Attempting to update sender balance...")
                transaction.updateData(["balance": newSenderBalance], forDocument: senderRef)
                print("🚀 FIREBASE_DEBUG: Sender balance update queued")
                
                print("🚀 FIREBASE_DEBUG: Attempting to update recipient balance...")
                transaction.updateData(["balance": newRecipientBalance], forDocument: recipientRef)
                print("🚀 FIREBASE_DEBUG: Recipient balance update queued")
                
                // Create transaction documents
                print("🚀 FIREBASE_DEBUG: Creating transaction objects...")
                
                let senderTransaction = Transaction(
                    name: "Money Sent", detail: "To: \(recipientName)", amount: amount, type: .transfer, category: "Transfer",
                    status: .completed, recipientId: recipientId, recipientName: recipientName, senderId: senderId, senderName: senderName, transactionHash: transactionHash, notes: notes
                )
                let recipientTransaction = Transaction(
                    name: "Money Received", detail: "From: \(senderName)", amount: amount, type: .transfer, category: "Transfer",
                    status: .completed, recipientId: recipientId, recipientName: recipientName, senderId: senderId, senderName: senderName, transactionHash: transactionHash, notes: notes
                )
                
                print("🚀 FIREBASE_DEBUG: Transaction objects created")
                print("🚀 FIREBASE_DEBUG: Sender transaction fields:")
                print("🚀 FIREBASE_DEBUG:   - name: \(senderTransaction.name)")
                print("🚀 FIREBASE_DEBUG:   - amount: \(senderTransaction.amount)")
                print("🚀 FIREBASE_DEBUG:   - type: \(senderTransaction.type)")
                print("🚀 FIREBASE_DEBUG:   - status: \(senderTransaction.status)")
                print("🚀 FIREBASE_DEBUG:   - recipientId: \(senderTransaction.recipientId ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - recipientName: \(senderTransaction.recipientName ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - senderId: \(senderTransaction.senderId ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - senderName: \(senderTransaction.senderName ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - transactionHash: \(senderTransaction.transactionHash ?? "nil")")
                
                print("🚀 FIREBASE_DEBUG: Recipient transaction fields:")
                print("🚀 FIREBASE_DEBUG:   - name: \(recipientTransaction.name)")
                print("🚀 FIREBASE_DEBUG:   - amount: \(recipientTransaction.amount)")
                print("🚀 FIREBASE_DEBUG:   - type: \(recipientTransaction.type)")
                print("🚀 FIREBASE_DEBUG:   - status: \(recipientTransaction.status)")
                print("🚀 FIREBASE_DEBUG:   - recipientId: \(recipientTransaction.recipientId ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - recipientName: \(recipientTransaction.recipientName ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - senderId: \(recipientTransaction.senderId ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - senderName: \(recipientTransaction.senderName ?? "nil")")
                print("🚀 FIREBASE_DEBUG:   - transactionHash: \(recipientTransaction.transactionHash ?? "nil")")
                
                // Save transaction documents
                let senderTransactionRef = senderRef.collection("transactions").document(transactionId)
                let recipientTransactionRef = recipientRef.collection("transactions").document(transactionId)
                
                print("🚀 FIREBASE_DEBUG: Transaction document references:")
                print("🚀 FIREBASE_DEBUG: Sender transaction path: \(senderTransactionRef.path)")
                print("🚀 FIREBASE_DEBUG: Recipient transaction path: \(recipientTransactionRef.path)")
                
                print("🚀 FIREBASE_DEBUG: Attempting to save transaction documents...")
                
                do {
                    print("🚀 FIREBASE_DEBUG: Saving sender transaction...")
                    try transaction.setData(from: senderTransaction, forDocument: senderTransactionRef)
                    print("✅ FIREBASE_DEBUG: Sender transaction save queued successfully")
                    
                    print("🚀 FIREBASE_DEBUG: Saving recipient transaction...")
                    try transaction.setData(from: recipientTransaction, forDocument: recipientTransactionRef)
                    print("✅ FIREBASE_DEBUG: Recipient transaction save queued successfully")
                } catch {
                    print("❌ FIREBASE_DEBUG: Error saving transaction documents: \(error)")
                    print("❌ FIREBASE_DEBUG: Error type: \(type(of: error))")
                    print("❌ FIREBASE_DEBUG: Error description: \(error.localizedDescription)")
                    
                    if let nsError = error as NSError? {
                        print("❌ FIREBASE_DEBUG: NSError domain: \(nsError.domain)")
                        print("❌ FIREBASE_DEBUG: NSError code: \(nsError.code)")
                        print("❌ FIREBASE_DEBUG: NSError userInfo: \(nsError.userInfo)")
                    }
                    
                    errorPointer?.pointee = error as NSError
                    return nil
                }
                
                print("🚀 FIREBASE_DEBUG: All operations queued successfully, returning transaction ID")
                return transactionId as Any
            }
            
            print("🚀 FIREBASE_DEBUG: Firestore transaction completed successfully")
            print("🚀 FIREBASE_DEBUG: Transaction result: \(result)")
            print("🚀 FIREBASE_DEBUG: Result type: \(type(of: result))")
            
            guard let resultTransactionId = result as? String, resultTransactionId == transactionId else {
                print("❌ FIREBASE_DEBUG: Transaction result validation failed")
                print("❌ FIREBASE_DEBUG: Expected: \(transactionId)")
                print("❌ FIREBASE_DEBUG: Got: \(result)")
                throw TransactionError.transactionFailed("Transaction result validation failed")
            }
            
            print("✅ FIREBASE_DEBUG: Transaction result validation passed")
            print("🚀 FIREBASE_DEBUG: Waiting 500ms before verification...")
            
            try await Task.sleep(nanoseconds: 500_000_000)
            
            print("🚀 FIREBASE_DEBUG: Starting transaction verification...")
            try await verifyTransactionSaved(transactionId: resultTransactionId, senderId: senderId, recipientId: recipientId)
            
            print("✅ FIREBASE_DEBUG: Transaction verification completed successfully")
            print("✅ FIREBASE_DEBUG: Money transfer completed successfully!")
            print("🚀 FIREBASE_DEBUG: ========== MONEY TRANSFER COMPLETED ==========")
            
            return resultTransactionId
            
        } catch {
            print("❌ FIREBASE_DEBUG: ========== TRANSACTION FAILED ==========")
            print("❌ FIREBASE_DEBUG: Error caught: \(error)")
            print("❌ FIREBASE_DEBUG: Error type: \(type(of: error))")
            print("❌ FIREBASE_DEBUG: Error description: \(error.localizedDescription)")
            
            let nsError = error as NSError
            print("❌ FIREBASE_DEBUG: NSError details:")
            print("❌ FIREBASE_DEBUG: Domain: \(nsError.domain)")
            print("❌ FIREBASE_DEBUG: Code: \(nsError.code)")
            print("❌ FIREBASE_DEBUG: UserInfo: \(nsError.userInfo)")
            
            // Detailed Firebase error analysis
            if nsError.domain == "FIRFirestoreErrorDomain" {
                print("❌ FIREBASE_DEBUG: This is a Firestore error")
                
                switch nsError.code {
                case 1:
                    print("❌ FIREBASE_DEBUG: CANCELLED - The operation was cancelled")
                case 2:
                    print("❌ FIREBASE_DEBUG: UNKNOWN - Unknown error")
                case 3:
                    print("❌ FIREBASE_DEBUG: INVALID_ARGUMENT - Invalid argument provided")
                case 4:
                    print("❌ FIREBASE_DEBUG: DEADLINE_EXCEEDED - Operation deadline exceeded")
                case 5:
                    print("❌ FIREBASE_DEBUG: NOT_FOUND - Document/collection not found")
                case 6:
                    print("❌ FIREBASE_DEBUG: ALREADY_EXISTS - Document already exists")
                case 7:
                    print("❌ FIREBASE_DEBUG: PERMISSION_DENIED - Insufficient permissions")
                    print("❌ FIREBASE_DEBUG: This means Firestore security rules are blocking the operation")
                    print("❌ FIREBASE_DEBUG: Check the Firestore rules for user updates and transaction creation")
                case 8:
                    print("❌ FIREBASE_DEBUG: RESOURCE_EXHAUSTED - Resource exhausted")
                case 9:
                    print("❌ FIREBASE_DEBUG: FAILED_PRECONDITION - Failed precondition")
                case 10:
                    print("❌ FIREBASE_DEBUG: ABORTED - Operation aborted")
                case 11:
                    print("❌ FIREBASE_DEBUG: OUT_OF_RANGE - Out of range")
                case 12:
                    print("❌ FIREBASE_DEBUG: UNIMPLEMENTED - Operation not implemented")
                case 13:
                    print("❌ FIREBASE_DEBUG: INTERNAL - Internal error")
                case 14:
                    print("❌ FIREBASE_DEBUG: UNAVAILABLE - Service unavailable")
                case 15:
                    print("❌ FIREBASE_DEBUG: DATA_LOSS - Data loss")
                case 16:
                    print("❌ FIREBASE_DEBUG: UNAUTHENTICATED - User not authenticated")
                default:
                    print("❌ FIREBASE_DEBUG: Unknown Firestore error code: \(nsError.code)")
                }
                
                // Additional Firestore error details
                if let underlyingError = nsError.userInfo[NSUnderlyingErrorKey] as? NSError {
                    print("❌ FIREBASE_DEBUG: Underlying error: \(underlyingError)")
                    print("❌ FIREBASE_DEBUG: Underlying error domain: \(underlyingError.domain)")
                    print("❌ FIREBASE_DEBUG: Underlying error code: \(underlyingError.code)")
                }
                
                if let localizedFailureReason = nsError.userInfo[NSLocalizedFailureReasonErrorKey] as? String {
                    print("❌ FIREBASE_DEBUG: Failure reason: \(localizedFailureReason)")
                }
                
                if let localizedRecoverySuggestion = nsError.userInfo[NSLocalizedRecoverySuggestionErrorKey] as? String {
                    print("❌ FIREBASE_DEBUG: Recovery suggestion: \(localizedRecoverySuggestion)")
                }
            } else if nsError.domain == "FIRAuthErrorDomain" {
                print("❌ FIREBASE_DEBUG: This is a Firebase Auth error")
                print("❌ FIREBASE_DEBUG: Auth error code: \(nsError.code)")
            } else {
                print("❌ FIREBASE_DEBUG: This is not a Firebase error")
                print("❌ FIREBASE_DEBUG: Error domain: \(nsError.domain)")
            }
            
            // Check for specific error conditions
            if nsError.localizedDescription.contains("Insufficient funds") {
                print("❌ FIREBASE_DEBUG: Throwing insufficient funds error")
                throw TransactionError.insufficientFunds
            } else if nsError.localizedDescription.contains("not found") {
                print("❌ FIREBASE_DEBUG: Throwing user not found error")
                throw TransactionError.userNotFound
            } else {
                print("❌ FIREBASE_DEBUG: Throwing generic transaction failed error")
                throw TransactionError.transactionFailed(error.localizedDescription)
            }
        }
    }
    
    
    // MARK: - Transaction Verification
    private func verifyTransactionSaved(transactionId: String, senderId: String, recipientId: String) async throws {
        print("🚀 FIREBASE_DEBUG: Verifying transaction saved...")
        print("🚀 FIREBASE_DEBUG: Transaction ID: \(transactionId)")
        print("🚀 FIREBASE_DEBUG: Sender ID: \(senderId)")
        print("🚀 FIREBASE_DEBUG: Recipient ID: \(recipientId)")
        
        // Check sender's transaction (we can only read our own transactions due to security rules)
        let senderTransactionRef = db.collection("users").document(senderId).collection("transactions").document(transactionId)
        
        print("🚀 FIREBASE_DEBUG: Checking sender transaction at: \(senderTransactionRef.path)")
        
        do {
            let senderDoc = try await senderTransactionRef.getDocument()
            
            print("🚀 FIREBASE_DEBUG: Sender transaction exists: \(senderDoc.exists)")
            
            guard senderDoc.exists else {
                print("❌ FIREBASE_DEBUG: Sender transaction not found")
                throw TransactionError.transactionFailed("Sender transaction not saved")
            }
            
            // Verify sender transaction data
            let senderTransaction = try senderDoc.data(as: Transaction.self)
            
            print("🚀 FIREBASE_DEBUG: Sender transaction verification:")
            print("🚀 FIREBASE_DEBUG:   - Type: \(senderTransaction.type)")
            print("🚀 FIREBASE_DEBUG:   - Status: \(senderTransaction.status)")
            print("🚀 FIREBASE_DEBUG:   - Amount: \(senderTransaction.amount)")
            print("🚀 FIREBASE_DEBUG:   - Recipient ID: \(senderTransaction.recipientId ?? "nil")")
            print("🚀 FIREBASE_DEBUG:   - Sender ID: \(senderTransaction.senderId ?? "nil")")
            
            // Verify sender transaction
            guard senderTransaction.type == .transfer,
                  senderTransaction.status == .completed,
                  senderTransaction.recipientId == recipientId,
                  senderTransaction.senderId == senderId else {
                print("❌ FIREBASE_DEBUG: Sender transaction data validation failed")
                throw TransactionError.transactionFailed("Sender transaction data invalid")
            }
            
            print("✅ FIREBASE_DEBUG: Sender transaction verification passed")
            
            // Note: We cannot verify the recipient's transaction due to security rules
            // that prevent reading other users' transactions. This is expected and secure.
            // The Firestore transaction ensures both documents were created atomically.
            
            print("✅ FIREBASE_DEBUG: Transaction verification completed successfully")
            
        } catch {
            print("❌ FIREBASE_DEBUG: Transaction verification failed: \(error)")
            if error is TransactionError {
                throw error
            } else {
                throw TransactionError.transactionFailed("Failed to verify transaction data: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - Safe Transaction Decoding
    private func safeDecodeTransaction(from document: DocumentSnapshot) -> Transaction? {
        guard document.exists else {
            print("TransactionService: Document does not exist: \(document.documentID)")
            return nil
        }
        
        do {
            // First try using Firestore's automatic decoding
            var transaction = try document.data(as: Transaction.self)
            transaction.id = document.documentID
            return transaction
        } catch {
            print("TransactionService: Error decoding transaction with automatic decoder: \(error)")
            
            // If automatic decoding fails, try manual decoding with fallbacks
            guard let data = document.data() else {
                print("TransactionService: No data in document: \(document.documentID)")
                return nil
            }
            
            return manualDecodeTransaction(from: data, documentId: document.documentID, documentRef: document.reference)
        }
    }
    
    private func manualDecodeTransaction(from data: [String: Any], documentId: String, documentRef: DocumentReference) -> Transaction? {
        print("TransactionService: Attempting manual decode for transaction: \(documentId)")
        
        // Extract required fields with fallbacks
        guard let name = data["name"] as? String,
              let detail = data["detail"] as? String,
              let amount = data["amount"] as? Double,
              let typeString = data["type"] as? String,
              let type = TransactionType(rawValue: typeString),
              let category = data["category"] as? String else {
            print("TransactionService: Missing required fields in transaction: \(documentId)")
            print("TransactionService: Available keys: \(Array(data.keys))")
            return nil
        }
        
        // Handle date field
        let date: Timestamp
        if let timestamp = data["date"] as? Timestamp {
            date = timestamp
        } else if let dateValue = data["date"] as? Date {
            date = Timestamp(date: dateValue)
        } else {
            print("TransactionService: Using current date as fallback for transaction: \(documentId)")
            date = Timestamp(date: Date())
        }
        
        // Handle status field with fallback
        let status: TransactionStatus
        if let statusString = data["status"] as? String,
           let parsedStatus = TransactionStatus(rawValue: statusString) {
            status = parsedStatus
        } else {
            print("TransactionService: Missing or invalid status field, using 'completed' as default for transaction: \(documentId)")
            status = .completed
            
            // Asynchronously fix the missing status field in the database
            fixTransactionMissingFields(documentRef: documentRef, data: data)
        }
        
        // Extract optional fields
        let splitId = data["splitId"] as? String
        let recipientId = data["recipientId"] as? String
        let recipientName = data["recipientName"] as? String
        let senderId = data["senderId"] as? String
        let senderName = data["senderName"] as? String
        let transactionHash = data["transactionHash"] as? String
        let notes = data["notes"] as? String
        let attachments = data["attachments"] as? [String]
        
        // Create transaction object
        var transaction = Transaction(
            name: name,
            detail: detail,
            amount: amount,
            type: type,
            category: category,
            date: date,
            status: status,
            splitId: splitId,
            recipientId: recipientId,
            recipientName: recipientName,
            senderId: senderId,
            senderName: senderName,
            transactionHash: transactionHash,
            notes: notes,
            attachments: attachments
        )
        
        transaction.id = documentId
        
        print("TransactionService: Successfully manually decoded transaction: \(documentId)")
        return transaction
    }
    
    // MARK: - Utility Functions
    private func generateTransactionHash(senderId: String, recipientId: String, amount: Double) -> String {
        let timestamp = Date().timeIntervalSince1970
        let hashString = "\(senderId)\(recipientId)\(amount)\(timestamp)"
        return String(hashString.hashValue)
    }
    
    func fetchSplitForTransaction(splitId: String, completion: @escaping (Result<Split, Error>) -> Void) {
        db.collection("splits")
            .document(splitId)
            .getDocument { document, error in
                if let error = error {
                    completion(.failure(error))
                    return
                }
                
                guard let document = document, document.exists else {
                    completion(.failure(NSError(domain: "TransactionService", code: -404, userInfo: [NSLocalizedDescriptionKey: "Split not found."])))
                    return
                }
                
                do {
                    var split = try document.data(as: Split.self)
                    split.id = document.documentID
                    completion(.success(split))
                } catch {
                    completion(.failure(error))
                }
            }
    }
    
    // MARK: - Filter Valid Transactions
    private func filterValidTransactions(_ transactions: [Transaction], completion: @escaping ([Transaction]) -> Void) {
        let splitTransactions = transactions.filter { $0.splitId != nil }

        guard !splitTransactions.isEmpty else {
            // No split transactions to validate, return all transactions
            completion(transactions)
            return
        }
        
        print("TransactionService: Validating \(splitTransactions.count) split transactions")
        
        let dispatchGroup = DispatchGroup()
        var validSplitIds = Set<String>()
        
        // Check each unique split ID
        let uniqueSplitIds = Set(splitTransactions.compactMap { $0.splitId })
        
        for splitId in uniqueSplitIds {
            dispatchGroup.enter()
            
            db.collection("splits").document(splitId).getDocument { document, error in
                defer { dispatchGroup.leave() }
                
                if let document = document, document.exists {
                    let data = document.data()
                    let status = data?["status"] as? String ?? "pending"
                    
                    // Only include splits that are still pending (not cancelled/abandoned)
                    if status == "pending" {
                        // Check if split was created recently (within last 24 hours)
                        if let createdAt = data?["createdAt"] as? Timestamp {
                            let creationDate = createdAt.dateValue()
                            let hoursSinceCreation = Date().timeIntervalSince(creationDate) / 3600
                            
                            if hoursSinceCreation < 24 {
                                validSplitIds.insert(splitId)
                                print("TransactionService: Split \(splitId) is valid (pending, created \(String(format: "%.1f", hoursSinceCreation)) hours ago)")
                            } else {
                                print("TransactionService: Split \(splitId) is too old (\(String(format: "%.1f", hoursSinceCreation)) hours), marking as invalid")
                                // Optionally clean up old pending split
                                self.cleanupOldPendingSplit(splitId: splitId)
                            }
                        } else {
                            // If no creation date, assume it's valid for now
                            validSplitIds.insert(splitId)
                        }
                    } else if status == "complete" {
                        // Include completed splits - these should show in recent activity
                        validSplitIds.insert(splitId)
                        print("TransactionService: Split \(splitId) is completed, including in valid splits")
                    } else {
                        print("TransactionService: Split \(splitId) has status '\(status)' (cancelled or other), not including pending transactions")
                    }
                } else {
                    print("TransactionService: Split \(splitId) not found, excluding related transactions")
                }
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            // Filter transactions: keep all non-split transactions and only valid split transactions
            let validTransactions = transactions.filter { transaction in
                guard let splitId = transaction.splitId else {
                    // Non-split transaction, always include
                    return true
                }

                // For split transactions, only include if the split is valid (not cancelled)
                let isValid = validSplitIds.contains(splitId)
                if !isValid {
                    print("TransactionService: Filtering out transaction for cancelled split \(splitId)")
                }
                return isValid
            }
            
            print("TransactionService: Filtered to \(validTransactions.count) valid transactions")
            completion(validTransactions)
        }
    }
    
    // MARK: - Cleanup Old Pending Split
    private func cleanupOldPendingSplit(splitId: String) {
        guard let userId = auth.currentUser?.uid else { return }
        
        print("TransactionService: Cleaning up old pending split: \(splitId)")
        
        // Update split status to cancelled
        db.collection("splits").document(splitId).updateData([
            "status": "cancelled"
        ]) { error in
            if let error = error {
                print("TransactionService: Error updating split status: \(error.localizedDescription)")
            } else {
                print("TransactionService: Successfully marked split \(splitId) as cancelled")
            }
        }
        
        // Remove pending transactions for this split
        db.collection("users").document(userId).collection("transactions")
            .whereField("splitId", isEqualTo: splitId)
            .whereField("status", isEqualTo: "pending")
            .getDocuments { snapshot, error in
                if let error = error {
                    print("TransactionService: Error fetching pending transactions for cleanup: \(error.localizedDescription)")
                    return
                }
                
                guard let documents = snapshot?.documents else { return }
                
                let batch = self.db.batch()
                for document in documents {
                    batch.deleteDocument(document.reference)
                }
                
                batch.commit { error in
                    if let error = error {
                        print("TransactionService: Error deleting pending transactions: \(error.localizedDescription)")
                    } else {
                        print("TransactionService: Successfully deleted \(documents.count) pending transactions for split \(splitId)")
                    }
                }
            }
    }
    
    // MARK: - Fix Missing Transaction Fields
    private func fixTransactionMissingFields(documentRef: DocumentReference, data: [String: Any]?) {
        guard let data = data else { return }
        
        // Check if status field is missing
        if data["status"] == nil {
            print("TransactionService: Fixing transaction missing status field")
            
            // Add default status based on transaction type and other fields
            var updateData: [String: Any] = [:]
            
            // Default to completed for most transactions
            updateData["status"] = "completed"
            
            // If it's a split transaction that's still pending, keep it pending
            if let splitId = data["splitId"] as? String, !splitId.isEmpty {
                // Check if the split is still active
                db.collection("splits").document(splitId).getDocument { [weak self] splitDoc, error in
                    if let splitDoc = splitDoc, splitDoc.exists,
                       let splitData = splitDoc.data(),
                       let splitStatus = splitData["status"] as? String,
                       splitStatus == "pending" {
                        // Keep transaction as pending if split is still pending
                        documentRef.updateData(["status": "pending"]) { error in
                            if let error = error {
                                print("TransactionService: Error updating transaction status to pending: \(error.localizedDescription)")
                            } else {
                                print("TransactionService: Successfully updated transaction status to pending")
                            }
                        }
                    } else {
                        // Split is complete or doesn't exist, mark transaction as completed
                        documentRef.updateData(["status": "completed"]) { error in
                            if let error = error {
                                print("TransactionService: Error updating transaction status to completed: \(error.localizedDescription)")
                            } else {
                                print("TransactionService: Successfully updated transaction status to completed")
                            }
                        }
                    }
                }
            } else {
                // Non-split transaction, mark as completed
                documentRef.updateData(updateData) { error in
                    if let error = error {
                        print("TransactionService: Error updating transaction fields: \(error.localizedDescription)")
                    } else {
                        print("TransactionService: Successfully updated transaction fields")
                    }
                }
            }
        }
    }
    
    // MARK: - Manual Refresh
    func refreshTransactions() {
        print("TransactionService: Manual refresh requested")
        stopListening()
        loadTransactions()
    }
    
    func stopListening() {
        print("TransactionService: Stopping listeners")
        transactionListener?.remove()
        transactionListener = nil
        transactions = []
    }
    
    deinit {
        transactionListener?.remove()
    }
}
