import Foundation
import Security
import LocalAuthentication

enum KeychainError: Error {
    case duplicateEntry
    case unknown(OSStatus)
    case itemNotFound
    case biometricAuthenticationFailed
    case deviceNotSupported
}

enum KeychainAccessLevel {
    case whenUnlocked
    case whenUnlockedThisDeviceOnly
    case biometricProtected
    case biometricProtectedThisDeviceOnly
    
    var accessibility: CFString {
        switch self {
        case .whenUnlocked:
            return kSecAttrAccessibleWhenUnlocked
        case .whenUnlockedThisDeviceOnly:
            return kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        case .biometricProtected:
            return kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        case .biometricProtectedThisDeviceOnly:
            return kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        }
    }
}

class KeychainService {
    static let shared = KeychainService()
    private let serviceName = "com.dash.app"
    
    private init() {}
    
    // MARK: - Enhanced Security Methods
    
    func saveTOTPSecret(_ secret: String, for userId: String) -> Result<Void, KeychainError> {
        let key = "totp_secret_\(userId)"
        guard let data = secret.data(using: .utf8) else {
            return .failure(.unknown(errSecParam))
        }
        
        return saveSecure(key: key, data: data, accessLevel: .biometricProtectedThisDeviceOnly)
    }
    
    func loadTOTPSecret(for userId: String) -> Result<String, KeychainError> {
        let key = "totp_secret_\(userId)"
        
        switch loadSecure(key: key) {
        case .success(let data):
            guard let secret = String(data: data, encoding: .utf8) else {
                return .failure(.unknown(errSecDecode))
            }
            return .success(secret)
        case .failure(let error):
            return .failure(error)
        }
    }
    
    func saveBackupCodes(_ codes: [String], for userId: String) -> Result<Void, KeychainError> {
        let key = "backup_codes_\(userId)"
        do {
            let data = try JSONEncoder().encode(codes)
            return saveSecure(key: key, data: data, accessLevel: .biometricProtectedThisDeviceOnly)
        } catch {
            return .failure(.unknown(errSecParam))
        }
    }
    
    func loadBackupCodes(for userId: String) -> Result<[String], KeychainError> {
        let key = "backup_codes_\(userId)"
        
        switch loadSecure(key: key) {
        case .success(let data):
            do {
                let codes = try JSONDecoder().decode([String].self, from: data)
                return .success(codes)
            } catch {
                return .failure(.unknown(errSecDecode))
            }
        case .failure(let error):
            return .failure(error)
        }
    }
    
    func saveDeviceId(_ deviceId: String) -> Result<Void, KeychainError> {
        let key = "device_id"
        guard let data = deviceId.data(using: .utf8) else {
            return .failure(.unknown(errSecParam))
        }
        
        return saveSecure(key: key, data: data, accessLevel: .whenUnlockedThisDeviceOnly)
    }
    
    func loadDeviceId() -> Result<String, KeychainError> {
        let key = "device_id"
        
        switch loadSecure(key: key) {
        case .success(let data):
            guard let deviceId = String(data: data, encoding: .utf8) else {
                return .failure(.unknown(errSecDecode))
            }
            return .success(deviceId)
        case .failure(let error):
            return .failure(error)
        }
    }
    
    // MARK: - Core Secure Storage Methods
    
    private func saveSecure(key: String, data: Data, accessLevel: KeychainAccessLevel) -> Result<Void, KeychainError> {
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: accessLevel.accessibility
        ]
        
        // Add biometric protection if required
        if case .biometricProtected = accessLevel, #available(iOS 11.3, *) {
            let access = SecAccessControlCreateWithFlags(
                nil,
                accessLevel.accessibility,
                .biometryAny,
                nil
            )
            query[kSecAttrAccessControl as String] = access
        } else if case .biometricProtectedThisDeviceOnly = accessLevel, #available(iOS 11.3, *) {
            let access = SecAccessControlCreateWithFlags(
                nil,
                accessLevel.accessibility,
                .biometryCurrentSet,
                nil
            )
            query[kSecAttrAccessControl as String] = access
        }
        
        // Delete existing item first
        _ = deleteSecure(key: key)
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        switch status {
        case errSecSuccess:
            return .success(())
        case errSecDuplicateItem:
            return .failure(.duplicateEntry)
        default:
            return .failure(.unknown(status))
        }
    }
    
    private func loadSecure(key: String) -> Result<Data, KeychainError> {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)
        
        switch status {
        case errSecSuccess:
            guard let data = dataTypeRef as? Data else {
                return .failure(.unknown(errSecDecode))
            }
            return .success(data)
        case errSecItemNotFound:
            return .failure(.itemNotFound)
        case errSecAuthFailed:
            return .failure(.biometricAuthenticationFailed)
        default:
            return .failure(.unknown(status))
        }
    }
    
    private func deleteSecure(key: String) -> Result<Void, KeychainError> {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        switch status {
        case errSecSuccess, errSecItemNotFound:
            return .success(())
        default:
            return .failure(.unknown(status))
        }
    }
    
    // MARK: - Legacy Methods (for backward compatibility)
    
    func save(key: String, data: Data) -> OSStatus {
        switch saveSecure(key: key, data: data, accessLevel: .whenUnlockedThisDeviceOnly) {
        case .success:
            return errSecSuccess
        case .failure(let error):
            if case .unknown(let status) = error {
                return status
            }
            return errSecInternalError
        }
    }

    func load(key: String) -> Data? {
        switch loadSecure(key: key) {
        case .success(let data):
            return data
        case .failure:
            return nil
        }
    }

    func delete(key: String) -> OSStatus {
        switch deleteSecure(key: key) {
        case .success:
            return errSecSuccess
        case .failure(let error):
            if case .unknown(let status) = error {
                return status
            }
            return errSecInternalError
        }
    }
    
    // MARK: - Device Security Checks
    
    func isBiometricAvailable() -> Bool {
        let context = LAContext()
        var error: NSError?
        
        return context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error)
    }
    
    func getBiometricType() -> LABiometryType {
        let context = LAContext()
        var error: NSError?
        
        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            return .none
        }
        
        return context.biometryType
    }
    
    func generateDeviceId() -> String {
        // Generate a unique device identifier
        return UUID().uuidString
    }
    
    // MARK: - Security Audit
    
    func performSecurityAudit() -> [String: Any] {
        var auditResults: [String: Any] = [:]
        
        auditResults["biometric_available"] = isBiometricAvailable()
        auditResults["biometric_type"] = getBiometricType().rawValue
        auditResults["keychain_accessible"] = true
        auditResults["audit_timestamp"] = Date().timeIntervalSince1970
        
        return auditResults
    }
}
