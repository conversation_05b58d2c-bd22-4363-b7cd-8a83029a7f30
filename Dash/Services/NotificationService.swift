//
//  NotificationService.swift
//  Dash
//
//  Created by Assistant on 29/06/2025.
//

import Foundation
import UserNotifications
import UIKit
import FirebaseAuth
import FirebaseFirestore

class NotificationService: NSObject, ObservableObject {
    static let shared = NotificationService()

    @Published var hasPermission = false
    @Published var notificationCount = 0

    private let db = Firestore.firestore()
    private var shownNotificationIds = Set<String>() // Track which notifications we've already shown
    private var notificationListener: ListenerRegistration? // Track the Firestore listener
    private var lastNotificationCheck: Date = Date() // Track when we last checked for notifications

    override init() {
        super.init()
        setupNotifications()
    }
    
    // MARK: - Setup
    private func setupNotifications() {
        UNUserNotificationCenter.current().delegate = self
        setupNotificationCategories()
        checkPermissionStatus()
        updateNotificationCount()

        // Check background app refresh status
        checkBackgroundAppRefreshStatus()

        // Start listening for broadcast notifications if user is authenticated
        if Auth.auth().currentUser != nil {
            listenForBroadcastNotifications()
        }

        // Listen for auth state changes to start/stop broadcast listening
        Auth.auth().addStateDidChangeListener { [weak self] (auth: Auth, user: FirebaseAuth.User?) in
            if user != nil {
                // Clear shown notifications when a new user logs in
                self?.clearShownNotifications()
                self?.listenForBroadcastNotifications()
                // Start periodic notification checks
                self?.schedulePeriodicNotificationCheck()
            } else {
                // Clear shown notifications when user logs out
                self?.clearShownNotifications()
                // Stop periodic notification checks
                UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["periodic-check"])
            }
        }
    }

    private func checkBackgroundAppRefreshStatus() {
        let status = UIApplication.shared.backgroundRefreshStatus
        switch status {
        case .available:
            print("NotificationService: Background App Refresh is available")
        case .denied:
            print("NotificationService: Background App Refresh is denied by user")
        case .restricted:
            print("NotificationService: Background App Refresh is restricted")
        @unknown default:
            print("NotificationService: Background App Refresh status unknown")
        }
    }

    private func setupNotificationCategories() {
        // Broadcast notification category
        let broadcastCategory = UNNotificationCategory(
            identifier: "BROADCAST_NOTIFICATION",
            actions: [],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // Transaction notification category
        let transactionCategory = UNNotificationCategory(
            identifier: "TRANSACTION_NOTIFICATION",
            actions: [],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // Security alert category
        let securityCategory = UNNotificationCategory(
            identifier: "SECURITY_ALERT",
            actions: [],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        UNUserNotificationCenter.current().setNotificationCategories([
            broadcastCategory,
            transactionCategory,
            securityCategory
        ])
    }
    
    // MARK: - Permission Management
    func requestPermission() async -> Bool {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            
            await MainActor.run {
                self.hasPermission = granted
            }
            
            return granted
        } catch {
            print("NotificationService: Error requesting permission: \(error)")
            return false
        }
    }
    
    private func checkPermissionStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            print("NotificationService: Notification permission status: \(settings.authorizationStatus.rawValue)")
            print("NotificationService: Alert setting: \(settings.alertSetting.rawValue)")
            print("NotificationService: Badge setting: \(settings.badgeSetting.rawValue)")
            print("NotificationService: Sound setting: \(settings.soundSetting.rawValue)")

            DispatchQueue.main.async {
                self.hasPermission = settings.authorizationStatus == .authorized
                print("NotificationService: Has permission: \(self.hasPermission)")
            }
        }
    }
    
    // MARK: - Local Notifications
    func scheduleLocalNotification(
        title: String,
        body: String,
        identifier: String,
        userInfo: [String: Any] = [:],
        delay: TimeInterval = 0
    ) {
        print("NotificationService: Attempting to schedule notification - Title: \(title), Body: \(body)")
        
        // Check permission first
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            print("NotificationService: Current authorization status: \(settings.authorizationStatus.rawValue)")
            
            guard settings.authorizationStatus == .authorized else {
                print("NotificationService: Notification permission not granted")
                return
            }
            
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = body
            content.sound = .default
            content.userInfo = userInfo
            
            let trigger = UNTimeIntervalNotificationTrigger(timeInterval: max(delay, 0.1), repeats: false)
            let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
            
            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("NotificationService: Error scheduling notification: \(error)")
                } else {
                    print("NotificationService: Successfully scheduled notification with ID: \(identifier)")
                    DispatchQueue.main.async {
                        self.notificationCount += 1
                    }
                }
            }
        }
    }
    
    // MARK: - Transaction Notifications
    func notifyMoneyReceived(amount: Double, from senderName: String) {
        let title = "Money Received"
        let body = "You received \(amount.toCurrency()) from \(senderName)"
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "money_received_\(UUID().uuidString)",
            userInfo: ["type": "money_received", "amount": amount, "sender": senderName]
        )
    }
    
    func notifyMoneySent(amount: Double, to recipientName: String) {
        let title = "Money Sent"
        let body = "You sent \(amount.toCurrency()) to \(recipientName)"
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "money_sent_\(UUID().uuidString)",
            userInfo: ["type": "money_sent", "amount": amount, "recipient": recipientName]
        )
    }
    
    func notifyMoneyRequested(amount: Double, from requesterName: String) {
        let title = "Money Request"
        let body = "\(requesterName) requested \(amount.toCurrency()) from you"
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "money_requested_\(UUID().uuidString)",
            userInfo: ["type": "money_requested", "amount": amount, "requester": requesterName]
        )
    }
    
    func notifyRequestPaid(amount: Double, by payerName: String) {
        let title = "Request Paid"
        let body = "\(payerName) paid your request for \(amount.toCurrency())"
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "request_paid_\(UUID().uuidString)",
            userInfo: ["type": "request_paid", "amount": amount, "payer": payerName]
        )
    }
    
    // MARK: - Split Notifications
    func notifySplitCreated(title: String, amount: Double, creatorName: String) {
        let notificationTitle = "New Split Created"
        let body = "\(creatorName) created a split '\(title)' for \(amount.toCurrency())"
        
        scheduleLocalNotification(
            title: notificationTitle,
            body: body,
            identifier: "split_created_\(UUID().uuidString)",
            userInfo: ["type": "split_created", "split_title": title, "amount": amount, "creator": creatorName]
        )
    }
    
    func notifySplitJoined(title: String, participantName: String) {
        let notificationTitle = "Split Joined"
        let body = "\(participantName) joined your split '\(title)'"
        
        scheduleLocalNotification(
            title: notificationTitle,
            body: body,
            identifier: "split_joined_\(UUID().uuidString)",
            userInfo: ["type": "split_joined", "split_title": title, "participant": participantName]
        )
    }
    
    func notifySplitCompleted(title: String, yourShare: Double) {
        let notificationTitle = "Split Completed"
        let body = "Split '\(title)' is complete. Your share: \(yourShare.toCurrency())"
        
        scheduleLocalNotification(
            title: notificationTitle,
            body: body,
            identifier: "split_completed_\(UUID().uuidString)",
            userInfo: ["type": "split_completed", "split_title": title, "share": yourShare]
        )
    }
    
    // MARK: - Account Notifications
    func notifyAccountActivity(message: String) {
        let title = "Account Activity"
        
        scheduleLocalNotification(
            title: title,
            body: message,
            identifier: "account_activity_\(UUID().uuidString)",
            userInfo: ["type": "account_activity"]
        )
    }
    
    func notifySecurityAlert(message: String) {
        let title = "Security Alert"
        
        scheduleLocalNotification(
            title: title,
            body: message,
            identifier: "security_alert_\(UUID().uuidString)",
            userInfo: ["type": "security_alert"]
        )
    }
    
    // MARK: - Enhanced Security Notifications
    
    func sendSecurityAlert(title: String, body: String, userId: String) {
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "security_alert_\(userId)_\(UUID().uuidString)",
            userInfo: ["type": "security_alert", "userId": userId, "priority": "high"]
        )
    }
    
    func notify2FASetupPrompt() {
        let title = "Enhance Your Security"
        let body = "Set up two-factor authentication to better protect your account and transactions."
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "2fa_setup_prompt_\(UUID().uuidString)",
            userInfo: ["type": "2fa_setup_prompt"],
            delay: 2.0 // Small delay to ensure user sees it
        )
    }
    
    func notify2FAEnabled() {
        let title = "Two-Factor Authentication Enabled"
        let body = "Your account is now protected with two-factor authentication."
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "2fa_enabled_\(UUID().uuidString)",
            userInfo: ["type": "2fa_enabled"]
        )
    }
    
    func notify2FADisabled() {
        let title = "Two-Factor Authentication Disabled"
        let body = "Two-factor authentication has been disabled for your account."
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "2fa_disabled_\(UUID().uuidString)",
            userInfo: ["type": "2fa_disabled"]
        )
    }
    
    func notifyBiometricEnabled() {
        let title = "Biometric Authentication Enabled"
        let body = "Your account is now secured with biometric authentication."
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "biometric_enabled_\(UUID().uuidString)",
            userInfo: ["type": "biometric_enabled"]
        )
    }
    
    func notifySecurityLevelUpgraded(to level: String) {
        let title = "Security Level Upgraded"
        let body = "Your account security level has been upgraded to \(level)."
        
        scheduleLocalNotification(
            title: title,
            body: body,
            identifier: "security_upgrade_\(UUID().uuidString)",
            userInfo: ["type": "security_upgrade", "level": level]
        )
    }
    
    // MARK: - Badge Management
    func updateBadgeCount(_ count: Int) {
        DispatchQueue.main.async {
            UIApplication.shared.applicationIconBadgeNumber = count
        }
    }
    
    func clearBadge() {
        updateBadgeCount(0)
        DispatchQueue.main.async {
            self.notificationCount = 0
        }
    }

    func clearShownNotifications() {
        shownNotificationIds.removeAll()
        print("NotificationService: Cleared shown notifications tracking")
    }

    func forceCheckForNotifications() {
        print("NotificationService: Force checking for notifications - clearing shown tracking first")
        clearShownNotifications()
        checkForNewNotifications()
    }

    func stopScheduledNotificationChecks() {
        let identifiers = ["check-30s", "check-1m", "check-5m", "check-15m", "periodic-check"]
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)
        print("NotificationService: Stopped scheduled notification checks")
    }

    // MARK: - Debug Methods
    func debugNotifications() {
        guard let currentUser = Auth.auth().currentUser else {
            print("NotificationService: DEBUG - No current user")
            return
        }

        print("NotificationService: DEBUG - Checking all notifications for user: \(currentUser.uid)")

        db.collection("notifications")
            .whereField("userId", isEqualTo: currentUser.uid)
            .order(by: "createdAt", descending: true)
            .limit(to: 10)
            .getDocuments { querySnapshot, error in
                if let error = error {
                    print("NotificationService: DEBUG - Error: \(error)")
                    return
                }

                guard let documents = querySnapshot?.documents else {
                    print("NotificationService: DEBUG - No documents found")
                    return
                }

                print("NotificationService: DEBUG - Found \(documents.count) total notifications")

                for (index, document) in documents.enumerated() {
                    let data = document.data()
                    let title = data["title"] as? String ?? "No title"
                    let isRead = data["isRead"] as? Bool ?? false
                    let createdAt = data["createdAt"] as? Timestamp
                    let dateString = createdAt?.dateValue().description ?? "No date"

                    print("NotificationService: DEBUG [\(index)]: \(document.documentID)")
                    print("  Title: \(title)")
                    print("  IsRead: \(isRead)")
                    print("  CreatedAt: \(dateString)")
                    print("  Already shown: \(self.shownNotificationIds.contains(document.documentID))")
                }
            }
    }

    // MARK: - Foreground Notification Check
    func checkForNewNotifications() {
        guard let currentUser = Auth.auth().currentUser else {
            print("NotificationService: No current user, cannot check for notifications")
            return
        }

        print("NotificationService: Checking for new notifications for user: \(currentUser.uid)")

        // Debug: Show all notifications for this user (temporarily disabled due to index requirement)
        // debugNotifications()

        // Use the last check time to only get notifications since then
        let checkSince = lastNotificationCheck
        lastNotificationCheck = Date()

        print("NotificationService: Checking for notifications since: \(checkSince)")
        print("NotificationService: Query - userId: \(currentUser.uid), isRead: false, createdAt > \(checkSince)")

        // Simplified query to avoid index requirements - just get recent unread notifications
        db.collection("notifications")
            .whereField("userId", isEqualTo: currentUser.uid)
            .whereField("isRead", isEqualTo: false)
            .order(by: "createdAt", descending: true)
            .limit(to: 20)
            .getDocuments { [weak self] querySnapshot, error in
                guard let self = self else { return }

                if let error = error {
                    print("NotificationService: Error checking for new notifications: \(error)")
                    return
                }

                guard let documents = querySnapshot?.documents else {
                    print("NotificationService: No new notifications found since last check")
                    // Fallback: check for any unread notifications in the last hour
                    self.checkForRecentNotifications()
                    return
                }

                print("NotificationService: Found \(documents.count) new notifications since last check")

                for document in documents {
                    let documentId = document.documentID
                    let data = document.data()

                    // Client-side filtering: only process notifications from the last hour
                    if let createdAt = data["createdAt"] as? Timestamp {
                        let oneHourAgo = Calendar.current.date(byAdding: .hour, value: -1, to: Date()) ?? Date()
                        if createdAt.dateValue() < oneHourAgo {
                            print("NotificationService: Skipping old notification: \(documentId)")
                            continue
                        }
                    }

                    // Skip if we've already shown this notification
                    if self.shownNotificationIds.contains(documentId) {
                        print("NotificationService: Skipping already shown notification: \(documentId)")
                        continue
                    }

                    guard let title = data["title"] as? String,
                          let message = data["message"] as? String,
                          let type = data["type"] as? String else {
                        print("NotificationService: Invalid notification data for: \(documentId)")
                        continue
                    }

                    // Check if notification has expired
                    if let expiresAt = data["expiresAt"] as? Timestamp {
                        if expiresAt.dateValue() <= Date() {
                            print("NotificationService: Notification expired: \(documentId)")
                            continue
                        }
                    }

                    let priority = data["priority"] as? String ?? "normal"
                    print("NotificationService: Showing new notification: \(title)")

                    // Schedule the notification immediately
                    self.scheduleBroadcastNotification(
                        id: documentId,
                        title: title,
                        message: message,
                        type: type,
                        priority: priority
                    )

                    // Mark as shown locally
                    self.shownNotificationIds.insert(documentId)
                }
            }
    }

    // MARK: - Fallback Notification Check
    private func checkForRecentNotifications() {
        guard let currentUser = Auth.auth().currentUser else { return }

        print("NotificationService: Fallback check - looking for notifications in last hour")

        // Check for any unread notifications in the last hour
        let oneHourAgo = Calendar.current.date(byAdding: .hour, value: -1, to: Date()) ?? Date()

        // Simplified fallback query - just get recent unread notifications
        db.collection("notifications")
            .whereField("userId", isEqualTo: currentUser.uid)
            .whereField("isRead", isEqualTo: false)
            .order(by: "createdAt", descending: true)
            .limit(to: 10)
            .getDocuments { [weak self] querySnapshot, error in
                guard let self = self else { return }

                if let error = error {
                    print("NotificationService: Error in fallback check: \(error)")
                    return
                }

                guard let documents = querySnapshot?.documents else {
                    print("NotificationService: No notifications found in last hour")
                    return
                }

                print("NotificationService: Fallback found \(documents.count) notifications in last hour")

                for document in documents {
                    let documentId = document.documentID

                    // Skip if we've already shown this notification
                    if self.shownNotificationIds.contains(documentId) {
                        print("NotificationService: Fallback skipping already shown: \(documentId)")
                        continue
                    }

                    let data = document.data()
                    guard let title = data["title"] as? String,
                          let message = data["message"] as? String,
                          let type = data["type"] as? String else {
                        print("NotificationService: Fallback invalid data for: \(documentId)")
                        continue
                    }

                    // Check if notification has expired
                    if let expiresAt = data["expiresAt"] as? Timestamp {
                        if expiresAt.dateValue() <= Date() {
                            print("NotificationService: Fallback notification expired: \(documentId)")
                            continue
                        }
                    }

                    let priority = data["priority"] as? String ?? "normal"
                    print("NotificationService: Fallback showing notification: \(title)")

                    // Schedule the notification
                    self.scheduleBroadcastNotification(
                        id: documentId,
                        title: title,
                        message: message,
                        type: type,
                        priority: priority
                    )

                    // Mark as shown locally
                    self.shownNotificationIds.insert(documentId)
                }
            }
    }

    // MARK: - Aggressive Notification Checking
    func startAggressiveNotificationChecking() {
        guard let currentUser = Auth.auth().currentUser else { return }

        print("NotificationService: Starting aggressive notification checking")

        // Check immediately (without clearing shown notifications)
        checkForNewNotifications()

        // Schedule multiple checks at different intervals
        scheduleNotificationCheck(delay: 30, identifier: "check-30s") // 30 seconds
        scheduleNotificationCheck(delay: 60, identifier: "check-1m")  // 1 minute
        scheduleNotificationCheck(delay: 300, identifier: "check-5m") // 5 minutes
        scheduleNotificationCheck(delay: 900, identifier: "check-15m") // 15 minutes
    }

    private func scheduleNotificationCheck(delay: TimeInterval, identifier: String) {
        let content = UNMutableNotificationContent()
        content.title = "" // Empty title
        content.body = "" // Empty body
        content.sound = nil // Silent
        content.userInfo = ["type": "notification_check", "delay": delay, "silent": true]

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: delay, repeats: false)
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("NotificationService: Error scheduling check (\(identifier)): \(error)")
            } else {
                print("NotificationService: Scheduled silent notification check in \(delay)s")
            }
        }
    }

    // MARK: - Periodic Notification Scheduling
    func schedulePeriodicNotificationCheck() {
        guard let currentUser = Auth.auth().currentUser else { return }

        print("NotificationService: Scheduling periodic notification checks")

        // Cancel any existing periodic checks
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: ["periodic-check"])

        // Schedule a repeating notification check every 15 minutes
        let content = UNMutableNotificationContent()
        content.title = "" // Empty title
        content.body = "" // Empty body
        content.sound = nil // Silent
        content.userInfo = ["type": "periodic_check", "userId": currentUser.uid, "silent": true]

        // Create a repeating trigger for every 15 minutes
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 15 * 60, repeats: true)

        let request = UNNotificationRequest(
            identifier: "periodic-check",
            content: content,
            trigger: trigger
        )

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("NotificationService: Error scheduling periodic check: \(error)")
            } else {
                print("NotificationService: Scheduled periodic notification check")
            }
        }
    }

    // MARK: - Notification Count Management
    func updateNotificationCount() {
        UNUserNotificationCenter.current().getDeliveredNotifications { notifications in
            DispatchQueue.main.async {
                self.notificationCount = notifications.count
            }
        }
    }

    // MARK: - Broadcast Notifications
    func listenForBroadcastNotifications() {
        guard let currentUser = Auth.auth().currentUser else {
            print("NotificationService: No current user, cannot listen for notifications")
            return
        }

        print("NotificationService: Starting to listen for broadcast notifications for user: \(currentUser.uid)")

        // Stop any existing listener
        stopNotificationListener()

        // Listen for new notifications from admin
        print("NotificationService: Setting up Firebase listener for user: \(currentUser.uid)")
        notificationListener = db.collection("notifications")
            .whereField("userId", isEqualTo: currentUser.uid)
            .whereField("isRead", isEqualTo: false)
            .addSnapshotListener { [weak self] (querySnapshot: QuerySnapshot?, error: Error?) in
                guard let self = self else { return }

                if let error = error {
                    print("NotificationService: Error listening for broadcast notifications: \(error)")
                    return
                }

                guard let documents = querySnapshot?.documents else {
                    print("NotificationService: No documents found in notifications query")
                    return
                }

                print("NotificationService: Found \(documents.count) unread notifications")

                // Debug: Print all notification documents
                for document in documents {
                    let data = document.data()
                    print("NotificationService: Document ID: \(document.documentID)")
                    print("NotificationService: Document data: \(data)")
                }

                for document in documents {
                    let data = document.data()
                    let documentId = document.documentID
                    print("NotificationService: Processing notification: \(documentId)")

                    // Skip if we've already shown this notification
                    if self.shownNotificationIds.contains(documentId) {
                        print("NotificationService: Notification \(documentId) already shown, skipping")
                        continue
                    }

                    guard let title = data["title"] as? String,
                          let message = data["message"] as? String,
                          let type = data["type"] as? String else {
                        print("NotificationService: Missing required fields in notification: \(documentId)")
                        continue
                    }

                    // Check if notification has expired (if expiresAt field exists)
                    if let expiresAt = data["expiresAt"] as? Timestamp {
                        if expiresAt.dateValue() <= Date() {
                            print("NotificationService: Notification \(documentId) has expired, marking as read")
                            // Notification has expired, mark as read and skip
                            document.reference.updateData(["isRead": true])
                            continue
                        }
                    }

                    let priority = data["priority"] as? String ?? "normal"
                    print("NotificationService: Scheduling notification: \(title)")

                    // Schedule local notification
                    self.scheduleBroadcastNotification(
                        id: documentId,
                        title: title,
                        message: message,
                        type: type,
                        priority: priority
                    )

                    // Mark as shown locally (so we don't show it again) but keep as unread in Firestore
                    self.shownNotificationIds.insert(documentId)
                    print("NotificationService: Marked notification \(documentId) as shown locally")
                }
            }

        // Also clean up expired notifications periodically
        cleanupExpiredNotifications()
    }

    func stopNotificationListener() {
        notificationListener?.remove()
        notificationListener = nil
        print("NotificationService: Stopped notification listener")
    }

    func restartNotificationListener() {
        guard Auth.auth().currentUser != nil else {
            print("NotificationService: No current user, cannot restart listener")
            return
        }
        print("NotificationService: Restarting notification listener")
        listenForBroadcastNotifications()
    }

    private func scheduleBroadcastNotification(id: String, title: String, message: String, type: String, priority: String) {
        print("NotificationService: scheduleBroadcastNotification called with:")
        print("  - ID: \(id)")
        print("  - Title: \(title)")
        print("  - Message: \(message)")
        print("  - Type: \(type)")
        print("  - Priority: \(priority)")

        // Check permission first
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            print("NotificationService: Current notification settings:")
            print("  - Authorization status: \(settings.authorizationStatus.rawValue)")
            print("  - Alert setting: \(settings.alertSetting.rawValue)")
            print("  - Badge setting: \(settings.badgeSetting.rawValue)")
            print("  - Sound setting: \(settings.soundSetting.rawValue)")

            guard settings.authorizationStatus == .authorized else {
                print("NotificationService: Notification permission not granted, cannot schedule broadcast notification")
                return
            }

            let content = UNMutableNotificationContent()
            content.title = title
            content.body = message
            content.sound = priority == "urgent" ? .defaultCritical : .default

            // Set category based on type
            content.categoryIdentifier = "BROADCAST_NOTIFICATION"

            // Add custom data
            content.userInfo = [
                "type": type,
                "priority": priority,
                "source": "admin_broadcast",
                "notificationId": id
            ]

            // Set badge count
            content.badge = NSNumber(value: self.notificationCount + 1)

            // Create trigger (immediate)
            let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)

            // Create request
            let request = UNNotificationRequest(
                identifier: "broadcast_\(id)",
                content: content,
                trigger: trigger
            )

            print("NotificationService: About to schedule notification with identifier: broadcast_\(id)")

            // Schedule notification
            UNUserNotificationCenter.current().add(request) { error in
                if let error = error {
                    print("NotificationService: Error scheduling broadcast notification: \(error)")
                } else {
                    print("NotificationService: Successfully scheduled broadcast notification: \(title)")
                    DispatchQueue.main.async {
                        self.notificationCount += 1
                    }
                }
            }
        }
    }

    // MARK: - Cleanup Functions
    private func cleanupExpiredNotifications() {
        guard let currentUser = Auth.auth().currentUser else { return }

        // Query for expired notifications
        db.collection("notifications")
            .whereField("userId", isEqualTo: currentUser.uid)
            .whereField("expiresAt", isLessThan: Timestamp(date: Date()))
            .whereField("isRead", isEqualTo: false)
            .getDocuments { [weak self] (querySnapshot, error) in
                guard let self = self else { return }

                if let error = error {
                    print("NotificationService: Error fetching expired notifications: \(error)")
                    return
                }

                guard let documents = querySnapshot?.documents else { return }

                // Mark expired notifications as read
                let batch = self.db.batch()
                for document in documents {
                    batch.updateData(["isRead": true], forDocument: document.reference)
                }

                if !documents.isEmpty {
                    batch.commit { error in
                        if let error = error {
                            print("NotificationService: Error marking expired notifications as read: \(error)")
                        } else {
                            print("NotificationService: Marked \(documents.count) expired notifications as read")
                        }
                    }
                }
            }
    }

    // MARK: - Test Notification
    func sendTestNotification() {
        print("NotificationService: Sending test notification")
        scheduleLocalNotification(
            title: "Test Notification",
            body: "This is a test notification from Dash",
            identifier: "test_\(UUID().uuidString)",
            userInfo: ["type": "test"]
        )
    }

    func sendTestBroadcastNotification() {
        print("NotificationService: Sending test broadcast notification")
        scheduleBroadcastNotification(
            id: "test_broadcast_\(UUID().uuidString)",
            title: "Test Broadcast",
            message: "This is a test broadcast notification",
            type: "test",
            priority: "normal"
        )
    }
    
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationService: UNUserNotificationCenterDelegate {
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        let userInfo = notification.request.content.userInfo
        let identifier = notification.request.identifier

        print("NotificationService: willPresent notification - ID: \(identifier), UserInfo: \(userInfo)")

        // Handle notification check notifications silently - NEVER show these to user
        if let type = userInfo["type"] as? String,
           (type == "periodic_check" || type == "notification_check") {
            print("NotificationService: Silent background check triggered: \(type)")
            checkForNewNotifications()
            // Absolutely do not show this to the user
            completionHandler([])
            return
        }

        // Handle silent notifications
        if let silent = userInfo["silent"] as? Bool, silent == true {
            print("NotificationService: Silent notification, not showing to user")
            completionHandler([])
            return
        }

        // Show other notifications even when app is in foreground
        print("NotificationService: Showing notification to user: \(notification.request.content.title)")
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        handleNotificationTap(userInfo: userInfo)
        completionHandler()
    }
    
    private func handleNotificationTap(userInfo: [AnyHashable: Any]) {
        guard let type = userInfo["type"] as? String else { return }
        
        // Handle different notification types
        switch type {
        case "money_received", "money_sent":
            // Navigate to transactions
            NotificationCenter.default.post(name: .navigateToTransactions, object: nil)
            
        case "money_requested", "request_paid":
            // Navigate to money requests
            NotificationCenter.default.post(name: .navigateToRequests, object: nil)
            
        case "split_created", "split_joined", "split_completed":
            // Navigate to splits
            NotificationCenter.default.post(name: .navigateToSplits, object: nil)
            
        case "account_activity", "security_alert":
            // Navigate to settings
            NotificationCenter.default.post(name: .navigateToSettings, object: nil)
            
        default:
            break
        }
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let navigateToTransactions = Notification.Name("navigateToTransactions")
    static let navigateToRequests = Notification.Name("navigateToRequests")
    static let navigateToSplits = Notification.Name("navigateToSplits")
    static let navigateToSettings = Notification.Name("navigateToSettings")
}
