import Foundation
import FirebaseFirestore
import LocalAuthentication
import UIKit

enum SecurityEvent: String, CaseIterable {
    case login = "login"
    case loginFailed = "login_failed"
    case logout = "logout"
    case twoFactorEnabled = "2fa_enabled"
    case twoFactorDisabled = "2fa_disabled"
    case twoFactorVerified = "2fa_verified"
    case twoFactorFailed = "2fa_failed"
    case passwordChanged = "password_changed"
    case accountLocked = "account_locked"
    case accountUnlocked = "account_unlocked"
    case suspiciousActivity = "suspicious_activity"
    case deviceRegistered = "device_registered"
    case deviceRemoved = "device_removed"
    case biometricEnabled = "biometric_enabled"
    case biometricDisabled = "biometric_disabled"
    case transactionAttempt = "transaction_attempt"
    case transactionBlocked = "transaction_blocked"
    case securitySettingsChanged = "security_settings_changed"
    case pinAuthenticated = "pin_authenticated"
    case pinFailed = "pin_failed"
    case pinReset = "pin_reset"
}

struct SecurityAuditLog: Codable {
    let id: String
    let userId: String
    let event: String
    let timestamp: Date
    let deviceId: String?
    let ipAddress: String?
    let userAgent: String?
    let location: String?
    let metadata: [String: Any]?
    let riskScore: Int
    let success: Bool
    
    init(userId: String, event: SecurityEvent, deviceId: String? = nil, metadata: [String: Any]? = nil, riskScore: Int = 0, success: Bool = true) {
        self.id = UUID().uuidString
        self.userId = userId
        self.event = event.rawValue
        self.timestamp = Date()
        self.deviceId = deviceId
        self.ipAddress = nil // Would be populated by backend
        self.userAgent = SecurityAuditService.getUserAgent()
        self.location = nil // Would be populated by backend
        self.metadata = metadata
        self.riskScore = riskScore
        self.success = success
    }
    
    enum CodingKeys: String, CodingKey {
        case id, userId, event, timestamp, deviceId, ipAddress, userAgent, location, riskScore, success
        case metadata
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        userId = try container.decode(String.self, forKey: .userId)
        event = try container.decode(String.self, forKey: .event)
        timestamp = try container.decode(Date.self, forKey: .timestamp)
        deviceId = try container.decodeIfPresent(String.self, forKey: .deviceId)
        ipAddress = try container.decodeIfPresent(String.self, forKey: .ipAddress)
        userAgent = try container.decodeIfPresent(String.self, forKey: .userAgent)
        location = try container.decodeIfPresent(String.self, forKey: .location)
        riskScore = try container.decode(Int.self, forKey: .riskScore)
        success = try container.decode(Bool.self, forKey: .success)
        
        // Handle metadata as a dictionary
        if let metadataDict = try? container.decodeIfPresent([String: String].self, forKey: .metadata) {
            metadata = metadataDict
        } else {
            metadata = nil
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(userId, forKey: .userId)
        try container.encode(event, forKey: .event)
        try container.encode(timestamp, forKey: .timestamp)
        try container.encodeIfPresent(deviceId, forKey: .deviceId)
        try container.encodeIfPresent(ipAddress, forKey: .ipAddress)
        try container.encodeIfPresent(userAgent, forKey: .userAgent)
        try container.encodeIfPresent(location, forKey: .location)
        try container.encode(riskScore, forKey: .riskScore)
        try container.encode(success, forKey: .success)
        
        // Convert metadata to string dictionary for Firestore compatibility
        if let metadata = metadata {
            let stringMetadata = metadata.compactMapValues { value -> String? in
                if let stringValue = value as? String {
                    return stringValue
                } else if let numberValue = value as? NSNumber {
                    return numberValue.stringValue
                } else {
                    return String(describing: value)
                }
            }
            try container.encode(stringMetadata, forKey: .metadata)
        }
    }
}

struct SecurityThreat {
    let type: String
    let severity: Int // 1-10
    let description: String
    let recommendation: String
    let timestamp: Date
}

class SecurityAuditService {
    static let shared = SecurityAuditService()
    private let db = Firestore.firestore()
    private let keychain = KeychainService.shared
    
    private init() {}
    
    // MARK: - Audit Logging
    
    func logSecurityEvent(_ event: SecurityEvent, for userId: String, metadata: [String: Any]? = nil, success: Bool = true) {
        let deviceId = getCurrentDeviceId()
        let riskScore = calculateRiskScore(for: event, metadata: metadata, success: success)
        
        let auditLog = SecurityAuditLog(
            userId: userId,
            event: event,
            deviceId: deviceId,
            metadata: metadata,
            riskScore: riskScore,
            success: success
        )
        
        // Store locally for immediate access
        storeAuditLogLocally(auditLog)
        
        // Store in Firestore
        storeAuditLogRemotely(auditLog)
        
        // Check for suspicious patterns
        if riskScore > 7 {
            handleHighRiskEvent(auditLog)
        }
    }
    
    private func storeAuditLogLocally(_ log: SecurityAuditLog) {
        // Store in UserDefaults for quick access (last 100 events)
        var logs = getLocalAuditLogs()
        logs.append(log)
        
        // Keep only last 100 events
        if logs.count > 100 {
            logs = Array(logs.suffix(100))
        }
        
        if let data = try? JSONEncoder().encode(logs) {
            UserDefaults.standard.set(data, forKey: "security_audit_logs")
        }
    }
    
    private func storeAuditLogRemotely(_ log: SecurityAuditLog) {
        do {
            let data = try Firestore.Encoder().encode(log)
            db.collection("security_audit_logs").document(log.id).setData(data) { error in
                if let error = error {
                    print("Error storing audit log: \(error)")
                }
            }
        } catch {
            print("Error encoding audit log: \(error)")
        }
    }
    
    private func getLocalAuditLogs() -> [SecurityAuditLog] {
        guard let data = UserDefaults.standard.data(forKey: "security_audit_logs"),
              let logs = try? JSONDecoder().decode([SecurityAuditLog].self, from: data) else {
            return []
        }
        return logs
    }
    
    // MARK: - Risk Assessment
    
    private func calculateRiskScore(for event: SecurityEvent, metadata: [String: Any]?, success: Bool) -> Int {
        var score = 0
        
        // Base scores for different events
        switch event {
        case .loginFailed:
            score += 3
        case .twoFactorFailed:
            score += 4
        case .accountLocked:
            score += 8
        case .suspiciousActivity:
            score += 9
        case .transactionBlocked:
            score += 6
        case .passwordChanged:
            score += success ? 1 : 5
        default:
            score += success ? 0 : 2
        }
        
        // Increase score for failed events
        if !success {
            score += 2
        }
        
        // Check for rapid successive events
        let recentEvents = getRecentEvents(for: event, within: 300) // 5 minutes
        if recentEvents.count > 3 {
            score += 3
        }
        
        // Check for unusual time patterns
        let hour = Calendar.current.component(.hour, from: Date())
        if hour < 6 || hour > 22 { // Outside normal hours
            score += 1
        }
        
        return min(score, 10) // Cap at 10
    }
    
    private func getRecentEvents(for event: SecurityEvent, within seconds: TimeInterval) -> [SecurityAuditLog] {
        let logs = getLocalAuditLogs()
        let cutoff = Date().addingTimeInterval(-seconds)
        
        return logs.filter { log in
            log.event == event.rawValue && log.timestamp > cutoff
        }
    }
    
    private func handleHighRiskEvent(_ log: SecurityAuditLog) {
        // Prevent infinite loops by checking if this is already a suspicious activity event
        guard log.event != SecurityEvent.suspiciousActivity.rawValue else {
            print("DEBUG: Preventing recursive suspicious activity logging")
            return
        }
        
        // Rate limiting: Check if we've sent a security alert recently
        let lastAlertKey = "last_security_alert_\(log.userId)"
        let now = Date().timeIntervalSince1970
        
        if let lastAlert = UserDefaults.standard.object(forKey: lastAlertKey) as? TimeInterval {
            let timeSinceLastAlert = now - lastAlert
            if timeSinceLastAlert < 300 { // 5 minutes cooldown
                print("DEBUG: Security alert rate limited for user \(log.userId)")
                return
            }
        }
        
        // Update last alert timestamp
        UserDefaults.standard.set(now, forKey: lastAlertKey)
        
        // Send notification to user (only once per cooldown period)
        NotificationService.shared.sendSecurityAlert(
            title: "Security Alert",
            body: "Suspicious activity detected on your account. Please review your recent activity.",
            userId: log.userId
        )
        
        // Log additional security event (but don't trigger another high-risk check)
        let auditLog = SecurityAuditLog(
            userId: log.userId,
            event: .suspiciousActivity,
            metadata: [
                "original_event": log.event,
                "risk_score": log.riskScore
            ],
            riskScore: 5, // Lower risk score to prevent recursion
            success: true
        )
        
        // Store locally and remotely without triggering risk assessment
        storeAuditLogLocally(auditLog)
        storeAuditLogRemotely(auditLog)
    }
    
    // MARK: - Device Management
    
    private func getCurrentDeviceId() -> String {
        switch keychain.loadDeviceId() {
        case .success(let deviceId):
            return deviceId
        case .failure:
            let newDeviceId = keychain.generateDeviceId()
            _ = keychain.saveDeviceId(newDeviceId)
            return newDeviceId
        }
    }
    
    static func getUserAgent() -> String {
        let device = UIDevice.current
        let systemVersion = device.systemVersion
        let model = device.model
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        
        return "Dash/\(appVersion) (\(model); iOS \(systemVersion))"
    }
    
    // MARK: - Security Health Check
    
    func performSecurityHealthCheck(for user: User) -> [SecurityThreat] {
        var threats: [SecurityThreat] = []
        
        // Check 2FA status
        if !user.twoFactorEnabled {
            threats.append(SecurityThreat(
                type: "missing_2fa",
                severity: 7,
                description: "Two-factor authentication is not enabled",
                recommendation: "Enable 2FA to significantly improve account security",
                timestamp: Date()
            ))
        }
        
        // Check biometric authentication
        if !user.biometricEnabled && keychain.isBiometricAvailable() {
            threats.append(SecurityThreat(
                type: "missing_biometric",
                severity: 5,
                description: "Biometric authentication is not enabled",
                recommendation: "Enable Face ID or Touch ID for convenient secure access",
                timestamp: Date()
            ))
        }
        
        // Check password age
        if let lastPasswordChange = user.lastPasswordChange {
            let daysSinceChange = Date().timeIntervalSince(lastPasswordChange) / 86400
            if daysSinceChange > 90 {
                threats.append(SecurityThreat(
                    type: "old_password",
                    severity: 4,
                    description: "Password hasn't been changed in \(Int(daysSinceChange)) days",
                    recommendation: "Consider changing your password regularly for better security",
                    timestamp: Date()
                ))
            }
        }
        
        // Check for recent failed login attempts
        let recentFailedLogins = getRecentEvents(for: .loginFailed, within: 86400) // 24 hours
        if recentFailedLogins.count > 5 {
            threats.append(SecurityThreat(
                type: "multiple_failed_logins",
                severity: 8,
                description: "\(recentFailedLogins.count) failed login attempts in the last 24 hours",
                recommendation: "Review your account activity and consider changing your password",
                timestamp: Date()
            ))
        }
        
        // Check security level
        if user.securityLevel == .basic {
            threats.append(SecurityThreat(
                type: "low_security_level",
                severity: 3,
                description: "Account is using basic security level",
                recommendation: "Upgrade to enhanced or maximum security level for better protection",
                timestamp: Date()
            ))
        }
        
        return threats.sorted { $0.severity > $1.severity }
    }
    
    // MARK: - Compliance Reporting
    
    func generateComplianceReport(for userId: String, from startDate: Date, to endDate: Date) -> [String: Any] {
        let logs = getAuditLogs(for: userId, from: startDate, to: endDate)
        
        var eventCounts: [String: Int] = [:]
        var riskEvents: [SecurityAuditLog] = []
        var failedEvents: [SecurityAuditLog] = []
        
        for log in logs {
            eventCounts[log.event, default: 0] += 1
            
            if log.riskScore > 5 {
                riskEvents.append(log)
            }
            
            if !log.success {
                failedEvents.append(log)
            }
        }
        
        return [
            "period": [
                "start": startDate.timeIntervalSince1970,
                "end": endDate.timeIntervalSince1970
            ],
            "total_events": logs.count,
            "event_breakdown": eventCounts,
            "high_risk_events": riskEvents.count,
            "failed_events": failedEvents.count,
            "success_rate": logs.isEmpty ? 1.0 : Double(logs.count - failedEvents.count) / Double(logs.count),
            "average_risk_score": logs.isEmpty ? 0 : logs.map { $0.riskScore }.reduce(0, +) / logs.count,
            "compliance_score": calculateComplianceScore(logs: logs)
        ]
    }
    
    private func getAuditLogs(for userId: String, from startDate: Date, to endDate: Date) -> [SecurityAuditLog] {
        // In a real implementation, this would query Firestore
        // For now, return local logs filtered by date
        return getLocalAuditLogs().filter { log in
            log.userId == userId && log.timestamp >= startDate && log.timestamp <= endDate
        }
    }
    
    private func calculateComplianceScore(logs: [SecurityAuditLog]) -> Int {
        guard !logs.isEmpty else { return 100 }
        
        let failedEvents = logs.filter { !$0.success }.count
        let highRiskEvents = logs.filter { $0.riskScore > 7 }.count
        
        let failureRate = Double(failedEvents) / Double(logs.count)
        let riskRate = Double(highRiskEvents) / Double(logs.count)
        
        let score = 100 - Int((failureRate * 50) + (riskRate * 30))
        return max(score, 0)
    }
    
    // MARK: - Data Retention
    
    func cleanupOldAuditLogs(olderThan days: Int = 90) {
        let cutoffDate = Date().addingTimeInterval(-Double(days * 86400))
        
        // Clean up local logs
        let logs = getLocalAuditLogs()
        let recentLogs = logs.filter { $0.timestamp > cutoffDate }
        
        if let data = try? JSONEncoder().encode(recentLogs) {
            UserDefaults.standard.set(data, forKey: "security_audit_logs")
        }
        
        // Clean up Firestore logs (would be done by backend in production)
        db.collection("security_audit_logs")
            .whereField("timestamp", isLessThan: cutoffDate)
            .getDocuments { snapshot, error in
                guard let documents = snapshot?.documents else { return }
                
                let batch = self.db.batch()
                for document in documents {
                    batch.deleteDocument(document.reference)
                }
                
                batch.commit { error in
                    if let error = error {
                        print("Error cleaning up audit logs: \(error)")
                    }
                }
            }
    }
}
