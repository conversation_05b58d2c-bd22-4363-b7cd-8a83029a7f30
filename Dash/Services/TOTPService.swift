import Foundation
import CryptoKit
import UIKit
import CoreImage

enum TOTPError: Error {
    case invalidSecret
    case secretNotFound
    case keychainError
    case invalidCode
    case rateLimitExceeded
    case backupCodeAlreadyUsed
}

extension TOTPError: LocalizedError {
    var errorDescription: String? {
        switch self {
        case .invalidSecret:
            return "Invalid authentication secret. Please contact support."
        case .secretNotFound:
            return "Authentication setup not found. Please set up two-factor authentication again."
        case .keychainError:
            return "Unable to access secure storage. Please try again."
        case .invalidCode:
            return "Invalid verification code. Please check your authenticator app."
        case .rateLimitExceeded:
            return "Too many attempts. Please wait a moment before trying again."
        case .backupCodeAlreadyUsed:
            return "This backup code has already been used. Please use a different code."
        }
    }
}

struct TOTPAttempt {
    let timestamp: Date
    let success: Bool
    let ipAddress: String?
}

class TOTPService {
    static let shared = TOTPService()
    private let keychain = KeychainService.shared
    private var attemptHistory: [String: [TOTPAttempt]] = [:]
    private let maxAttemptsPerMinute = 5
    private let maxAttemptsPerHour = 20
    
    private init() {}

    // MARK: - Secret Management
    
    func generateSecret() -> String {
        // Use standard 20-byte (160-bit) secret for TOTP compatibility
        var key = Data(count: 20)
        _ = key.withUnsafeMutableBytes { SecRandomCopyBytes(kSecRandomDefault, 20, $0.baseAddress!) }
        let secret = key.base32EncodedString
        print("DEBUG: Generated TOTP secret, length: \(secret.count)")
        return secret
    }
    
    func hasExistingSecret(for userId: String) -> Bool {
        switch loadSecret(for: userId) {
        case .success(let secret):
            print("DEBUG: Found existing secret for user \(userId), length: \(secret.count)")
            return true
        case .failure(let error):
            print("DEBUG: No existing secret found for user \(userId): \(error)")
            return false
        }
    }
    
    func saveSecret(_ secret: String, for userId: String) -> Result<Void, TOTPError> {
        switch keychain.saveTOTPSecret(secret, for: userId) {
        case .success:
            return .success(())
        case .failure(let error):
            print("DEBUG: Failed to save TOTP secret with biometric protection: \(error)")
            // Fallback to device-only protection if biometric fails
            let key = "totp_secret_\(userId)"
            guard let data = secret.data(using: .utf8) else {
                return .failure(.keychainError)
            }
            
            let status = keychain.save(key: key, data: data)
            if status == errSecSuccess {
                print("DEBUG: Successfully saved TOTP secret with fallback method")
                return .success(())
            } else {
                print("DEBUG: Failed to save TOTP secret even with fallback: \(status)")
                return .failure(.keychainError)
            }
        }
    }
    
    func loadSecret(for userId: String) -> Result<String, TOTPError> {
        print("DEBUG: Loading TOTP secret for user: \(userId)")

        switch keychain.loadTOTPSecret(for: userId) {
        case .success(let secret):
            print("DEBUG: Successfully loaded TOTP secret with biometric protection, length: \(secret.count)")
            return .success(secret)
        case .failure(.itemNotFound):
            print("DEBUG: TOTP secret not found with biometric protection, trying fallback")
            // Try fallback method
            let key = "totp_secret_\(userId)"
            if let data = keychain.load(key: key),
               let secret = String(data: data, encoding: .utf8) {
                print("DEBUG: Successfully loaded TOTP secret with fallback method, length: \(secret.count)")
                return .success(secret)
            }
            print("DEBUG: TOTP secret not found in fallback either")
            return .failure(.secretNotFound)
        case .failure(let error):
            print("DEBUG: Failed to load TOTP secret with biometric protection: \(error)")
            // Try fallback method
            let key = "totp_secret_\(userId)"
            if let data = keychain.load(key: key),
               let secret = String(data: data, encoding: .utf8) {
                print("DEBUG: Successfully loaded TOTP secret with fallback method, length: \(secret.count)")
                return .success(secret)
            }
            print("DEBUG: Failed to load TOTP secret with both methods")
            return .failure(.keychainError)
        }
    }
    
    // MARK: - Backup Codes
    
    func generateBackupCodes() -> [String] {
        var codes: [String] = []
        for _ in 0..<10 {
            let code = String(format: "%08d", Int.random(in: 10000000...99999999))
            codes.append(code)
        }
        return codes
    }
    
    func saveBackupCodes(_ codes: [String], for userId: String) -> Result<Void, TOTPError> {
        switch keychain.saveBackupCodes(codes, for: userId) {
        case .success:
            return .success(())
        case .failure(let error):
            print("DEBUG: Failed to save backup codes with biometric protection: \(error)")
            // Fallback to device-only protection if biometric fails
            let key = "backup_codes_\(userId)"
            do {
                let data = try JSONEncoder().encode(codes)
                let status = keychain.save(key: key, data: data)
                if status == errSecSuccess {
                    print("DEBUG: Successfully saved backup codes with fallback method")
                    return .success(())
                } else {
                    print("DEBUG: Failed to save backup codes even with fallback: \(status)")
                    return .failure(.keychainError)
                }
            } catch {
                print("DEBUG: Failed to encode backup codes: \(error)")
                return .failure(.keychainError)
            }
        }
    }
    
    func loadBackupCodes(for userId: String) -> Result<[String], TOTPError> {
        switch keychain.loadBackupCodes(for: userId) {
        case .success(let codes):
            return .success(codes)
        case .failure(.itemNotFound):
            // Try fallback method
            let key = "backup_codes_\(userId)"
            if let data = keychain.load(key: key) {
                do {
                    let codes = try JSONDecoder().decode([String].self, from: data)
                    print("DEBUG: Successfully loaded backup codes with fallback method")
                    return .success(codes)
                } catch {
                    print("DEBUG: Failed to decode backup codes from fallback: \(error)")
                }
            }
            return .success([]) // Return empty array if no backup codes exist
        case .failure(let error):
            print("DEBUG: Failed to load backup codes with biometric protection: \(error)")
            // Try fallback method
            let key = "backup_codes_\(userId)"
            if let data = keychain.load(key: key) {
                do {
                    let codes = try JSONDecoder().decode([String].self, from: data)
                    print("DEBUG: Successfully loaded backup codes with fallback method")
                    return .success(codes)
                } catch {
                    print("DEBUG: Failed to decode backup codes from fallback: \(error)")
                }
            }
            return .failure(.keychainError)
        }
    }
    
    func verifyBackupCode(_ code: String, for userId: String) -> Result<Bool, TOTPError> {
        switch loadBackupCodes(for: userId) {
        case .success(let codes):
            if codes.contains(code) {
                // Remove used backup code
                let remainingCodes = codes.filter { $0 != code }
                _ = saveBackupCodes(remainingCodes, for: userId)
                return .success(true)
            } else {
                return .success(false)
            }
        case .failure(let error):
            return .failure(error)
        }
    }

    // MARK: - QR Code Generation
    
    func generateQRCode(for email: String, secret: String, issuer: String = "Dash") -> UIImage? {
        let urlString = "otpauth://totp/\(issuer):\(email)?secret=\(secret)&issuer=\(issuer)&algorithm=SHA1&digits=6&period=30"
        return generateQRCode(from: urlString)
    }
    
    private func generateQRCode(from string: String) -> UIImage? {
        let data = string.data(using: .utf8)
        if let filter = CIFilter(name: "CIQRCodeGenerator") {
            filter.setValue(data, forKey: "inputMessage")
            let transform = CGAffineTransform(scaleX: 10, y: 10) // Increased scale for better quality
            if let output = filter.outputImage?.transformed(by: transform) {
                let context = CIContext()
                if let cgImage = context.createCGImage(output, from: output.extent) {
                    return UIImage(cgImage: cgImage)
                }
            }
        }
        return nil
    }

    // MARK: - TOTP Verification
    
    func verify(code: String, for userId: String) -> Result<Bool, TOTPError> {
        print("DEBUG: TOTP verification started for user: \(userId), code: \(code)")

        // Check rate limiting
        if let rateLimitError = checkRateLimit(for: userId) {
            print("DEBUG: TOTP verification failed - rate limit exceeded")
            return .failure(rateLimitError)
        }

        // Load secret from keychain
        switch loadSecret(for: userId) {
        case .success(let secret):
            print("DEBUG: Successfully loaded TOTP secret, length: \(secret.count)")
            let isValid = verifyTOTP(code: code, secret: secret)
            print("DEBUG: TOTP verification result: \(isValid)")
            recordAttempt(for: userId, success: isValid)
            return .success(isValid)
        case .failure(let error):
            print("DEBUG: Failed to load TOTP secret: \(error)")
            recordAttempt(for: userId, success: false)
            return .failure(error)
        }
    }
    
    func verifyWithBackup(code: String, for userId: String) -> Result<Bool, TOTPError> {
        // Check rate limiting
        if let rateLimitError = checkRateLimit(for: userId) {
            return .failure(rateLimitError)
        }
        
        switch verifyBackupCode(code, for: userId) {
        case .success(let isValid):
            recordAttempt(for: userId, success: isValid)
            return .success(isValid)
        case .failure(let error):
            recordAttempt(for: userId, success: false)
            return .failure(error)
        }
    }
    
    func verifyTOTP(code: String, secret: String) -> Bool {
        print("DEBUG: verifyTOTP called with code: \(code), secret length: \(secret.count)")

        guard let secretData = secret.base32DecodedData else {
            print("DEBUG: Failed to decode base32 secret")
            return false
        }

        print("DEBUG: Secret decoded successfully, data length: \(secretData.count)")

        let currentTime = Int64(Date().timeIntervalSince1970 / 30)
        print("DEBUG: Current time counter: \(currentTime)")

        // Check current time window and adjacent windows for clock skew tolerance
        for timeOffset in [-1, 0, 1] {
            let timeCounter = currentTime + Int64(timeOffset)
            let timeData = withUnsafeBytes(of: timeCounter.bigEndian) { Data($0) }

            let hash = HMAC<Insecure.SHA1>.authenticationCode(for: timeData, using: SymmetricKey(data: secretData))

            let truncatedHash = hash.withUnsafeBytes { ptr -> UInt32 in
                let offset = ptr[19] & 0x0f
                let truncated = ptr.baseAddress!.advanced(by: Int(offset)).assumingMemoryBound(to: UInt32.self)
                return truncated.pointee.bigEndian & 0x7fffffff
            }

            let codeInt = Int(truncatedHash % 1_000_000)
            let generatedCode = String(format: "%06d", codeInt)

            print("DEBUG: Time offset \(timeOffset), generated code: \(generatedCode)")

            if code == generatedCode {
                print("DEBUG: Code match found at time offset \(timeOffset)")
                return true
            }
        }

        print("DEBUG: No matching code found")
        return false
    }
    
    // MARK: - Rate Limiting
    
    private func checkRateLimit(for userId: String) -> TOTPError? {
        let now = Date()
        let oneMinuteAgo = now.addingTimeInterval(-60)
        let oneHourAgo = now.addingTimeInterval(-3600)
        
        let userAttempts = attemptHistory[userId] ?? []
        
        let recentAttempts = userAttempts.filter { $0.timestamp > oneMinuteAgo }
        let hourlyAttempts = userAttempts.filter { $0.timestamp > oneHourAgo }
        
        if recentAttempts.count >= maxAttemptsPerMinute {
            return .rateLimitExceeded
        }
        
        if hourlyAttempts.count >= maxAttemptsPerHour {
            return .rateLimitExceeded
        }
        
        return nil
    }
    
    private func recordAttempt(for userId: String, success: Bool) {
        let attempt = TOTPAttempt(timestamp: Date(), success: success, ipAddress: nil)
        
        if attemptHistory[userId] == nil {
            attemptHistory[userId] = []
        }
        
        attemptHistory[userId]?.append(attempt)
        
        // Clean up old attempts (keep only last 24 hours)
        let oneDayAgo = Date().addingTimeInterval(-86400)
        attemptHistory[userId] = attemptHistory[userId]?.filter { $0.timestamp > oneDayAgo }
    }
    
    // MARK: - Debug Methods

    func generateCurrentCode(for secret: String) -> String? {
        guard let secretData = secret.base32DecodedData else {
            print("DEBUG: Failed to decode secret for code generation")
            return nil
        }

        let currentTime = Int64(Date().timeIntervalSince1970 / 30)
        let timeData = withUnsafeBytes(of: currentTime.bigEndian) { Data($0) }

        let hash = HMAC<Insecure.SHA1>.authenticationCode(for: timeData, using: SymmetricKey(data: secretData))

        let truncatedHash = hash.withUnsafeBytes { ptr -> UInt32 in
            let offset = ptr[19] & 0x0f
            let truncated = ptr.baseAddress!.advanced(by: Int(offset)).assumingMemoryBound(to: UInt32.self)
            return truncated.pointee.bigEndian & 0x7fffffff
        }

        let codeInt = Int(truncatedHash % 1_000_000)
        let generatedCode = String(format: "%06d", codeInt)

        print("DEBUG: Generated current TOTP code: \(generatedCode) for time: \(currentTime)")
        return generatedCode
    }

    func testTOTPImplementation(for userId: String) {
        print("DEBUG: Testing TOTP implementation for user: \(userId)")

        switch loadSecret(for: userId) {
        case .success(let secret):
            print("DEBUG: Loaded secret successfully")
            if let currentCode = generateCurrentCode(for: secret) {
                print("DEBUG: Current expected code: \(currentCode)")
                let isValid = verifyTOTP(code: currentCode, secret: secret)
                print("DEBUG: Self-verification result: \(isValid)")
            }
        case .failure(let error):
            print("DEBUG: Failed to load secret for testing: \(error)")
        }
    }

    // MARK: - Security Analytics
    
    func getSecurityMetrics(for userId: String) -> [String: Any] {
        let userAttempts = attemptHistory[userId] ?? []
        let now = Date()
        
        let last24Hours = userAttempts.filter { now.timeIntervalSince($0.timestamp) < 86400 }
        let successfulAttempts = last24Hours.filter { $0.success }
        let failedAttempts = last24Hours.filter { !$0.success }
        
        return [
            "total_attempts_24h": last24Hours.count,
            "successful_attempts_24h": successfulAttempts.count,
            "failed_attempts_24h": failedAttempts.count,
            "success_rate": last24Hours.isEmpty ? 0 : Double(successfulAttempts.count) / Double(last24Hours.count),
            "last_attempt": userAttempts.last?.timestamp.timeIntervalSince1970 ?? 0
        ]
    }
    
    // MARK: - Cleanup
    
    func removeSecret(for userId: String) -> Result<Void, TOTPError> {
        let status = keychain.delete(key: "totp_secret_\(userId)")
        if status == errSecSuccess {
            return .success(())
        } else {
            return .failure(.keychainError)
        }
    }
    
    func removeBackupCodes(for userId: String) -> Result<Void, TOTPError> {
        let status = keychain.delete(key: "backup_codes_\(userId)")
        if status == errSecSuccess {
            return .success(())
        } else {
            return .failure(.keychainError)
        }
    }
    
    func deleteTOTPData(for userId: String) -> Result<Void, TOTPError> {
        // Remove from keychain
        _ = keychain.delete(key: "totp_secret_\(userId)")
        _ = keychain.delete(key: "backup_codes_\(userId)")
        
        // Remove from memory
        attemptHistory.removeValue(forKey: userId)
        
        return .success(())
    }
}

// MARK: - Extensions

extension Data {
    var base32EncodedString: String {
        let alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567"
        var result = ""
        var bits = 0
        var bitCount = 0
        
        for byte in self {
            bits = (bits << 8) | Int(byte)
            bitCount += 8
            
            while bitCount >= 5 {
                let index = (bits >> (bitCount - 5)) & 31
                result.append(alphabet[alphabet.index(alphabet.startIndex, offsetBy: index)])
                bitCount -= 5
            }
        }
        
        if bitCount > 0 {
            let index = (bits << (5 - bitCount)) & 31
            result.append(alphabet[alphabet.index(alphabet.startIndex, offsetBy: index)])
        }
        
        return result
    }
}

extension String {
    var base32DecodedData: Data? {
        print("DEBUG: Decoding base32 string: \(self)")
        let alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567"
        var result = Data()
        var bits = 0
        var bitCount = 0

        let cleanedString = self.uppercased().replacingOccurrences(of: " ", with: "")
        print("DEBUG: Cleaned base32 string: \(cleanedString)")

        for char in cleanedString {
            if let index = alphabet.firstIndex(of: char) {
                let value = alphabet.distance(from: alphabet.startIndex, to: index)
                bits = (bits << 5) | value
                bitCount += 5

                if bitCount >= 8 {
                    let byte = (bits >> (bitCount - 8)) & 0xff
                    result.append(UInt8(byte))
                    bitCount -= 8
                }
            } else {
                print("DEBUG: Invalid base32 character found: \(char)")
                return nil
            }
        }

        print("DEBUG: Base32 decoded successfully, result length: \(result.count)")
        return result
    }
}
