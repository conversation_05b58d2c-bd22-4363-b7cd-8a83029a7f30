import Foundation
import FirebaseFirestore
import Combine

@MainActor
class SubscriptionService: ObservableObject {
    static let shared = SubscriptionService()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    // MARK: - Subscription Management
    
    /// Upgrade or change user's subscription tier
    func upgradeSubscription(for userId: String, to newTier: SubscriptionTier) async throws {
        isLoading = true
        errorMessage = nil
        
        do {
            // Get current user data
            let userRef = db.collection("users").document(userId)
            let userDoc = try await userRef.getDocument()
            
            guard let userData = userDoc.data(),
                  let currentBalance = userData["balance"] as? Double else {
                throw SubscriptionError.userNotFound
            }
            
            // Check if user has sufficient balance (if not free tier)
            if newTier.monthlyPrice > 0 && currentBalance < newTier.monthlyPrice {
                throw SubscriptionError.insufficientBalance
            }
            
            // Create new subscription
            let newSubscription = Subscription(tier: newTier)
            
            // Prepare update data
            var updateData: [String: Any] = [
                "subscription": [
                    "tier": newTier.rawValue,
                    "status": newSubscription.status.rawValue,
                    "startDate": Timestamp(date: newSubscription.startDate),
                    "endDate": Timestamp(date: newSubscription.endDate),
                    "autoRenew": newSubscription.autoRenew,
                    "priceAtPurchase": newSubscription.priceAtPurchase
                ]
            ]
            
            // Add payment dates if not free tier
            if newTier != .classic {
                updateData["subscription.lastPaymentDate"] = Timestamp(date: newSubscription.startDate)
                updateData["subscription.nextPaymentDate"] = Timestamp(date: newSubscription.endDate)
                
                // Deduct subscription cost from balance
                updateData["balance"] = currentBalance - newTier.monthlyPrice
            }
            
            // Update user document
            try await userRef.updateData(updateData)
            
            // Create transaction record for non-free tiers
            if newTier != .classic {
                try await createSubscriptionTransaction(
                    userId: userId,
                    tier: newTier,
                    amount: newTier.monthlyPrice
                )
            }
            
            successMessage = "Successfully upgraded to \(newTier.displayName)!"
            
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
        
        isLoading = false
    }
    
    /// Cancel user's subscription (downgrade to Classic)
    func cancelSubscription(for userId: String) async throws {
        isLoading = true
        errorMessage = nil
        
        do {
            let userRef = db.collection("users").document(userId)
            
            // Update subscription to cancelled status
            let updateData: [String: Any] = [
                "subscription.status": SubscriptionStatus.cancelled.rawValue,
                "subscription.autoRenew": false
            ]
            
            try await userRef.updateData(updateData)
            
            successMessage = "Subscription cancelled. You'll continue to have access until your current period ends."
            
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
        
        isLoading = false
    }
    
    /// Process auto-renewal for subscriptions
    func processAutoRenewals() async {
        do {
            // Query for subscriptions that need renewal
            let query = db.collection("users")
                .whereField("subscription.autoRenew", isEqualTo: true)
                .whereField("subscription.status", isEqualTo: SubscriptionStatus.active.rawValue)
                .whereField("subscription.endDate", isLessThanOrEqualTo: Timestamp(date: Date()))
            
            let snapshot = try await query.getDocuments()
            
            for document in snapshot.documents {
                let userId = document.documentID
                let data = document.data()
                
                guard let subscriptionData = data["subscription"] as? [String: Any],
                      let tierString = subscriptionData["tier"] as? String,
                      let tier = SubscriptionTier(rawValue: tierString),
                      let currentBalance = data["balance"] as? Double else {
                    continue
                }
                
                // Skip if Classic tier (free)
                if tier == .classic {
                    continue
                }
                
                // Check if user has sufficient balance for renewal
                if currentBalance >= tier.monthlyPrice {
                    try await renewSubscription(userId: userId, tier: tier, currentBalance: currentBalance)
                } else {
                    try await expireSubscription(userId: userId)
                }
            }
            
        } catch {
            print("Error processing auto-renewals: \(error)")
        }
    }
    
    // MARK: - Private Methods
    
    private func renewSubscription(userId: String, tier: SubscriptionTier, currentBalance: Double) async throws {
        let userRef = db.collection("users").document(userId)
        let newEndDate = Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date()
        
        let updateData: [String: Any] = [
            "subscription.endDate": Timestamp(date: newEndDate),
            "subscription.lastPaymentDate": Timestamp(date: Date()),
            "subscription.nextPaymentDate": Timestamp(date: newEndDate),
            "balance": currentBalance - tier.monthlyPrice
        ]
        
        try await userRef.updateData(updateData)
        
        // Create transaction record
        try await createSubscriptionTransaction(
            userId: userId,
            tier: tier,
            amount: tier.monthlyPrice
        )
    }
    
    private func expireSubscription(userId: String) async throws {
        let userRef = db.collection("users").document(userId)
        
        // Downgrade to Classic tier
        let classicSubscription = Subscription(tier: .classic)
        
        let updateData: [String: Any] = [
            "subscription": [
                "tier": SubscriptionTier.classic.rawValue,
                "status": SubscriptionStatus.active.rawValue,
                "startDate": Timestamp(date: classicSubscription.startDate),
                "endDate": Timestamp(date: classicSubscription.endDate),
                "autoRenew": true,
                "priceAtPurchase": 0.0
            ]
        ]
        
        try await userRef.updateData(updateData)
    }
    
    private func createSubscriptionTransaction(userId: String, tier: SubscriptionTier, amount: Double) async throws {
        let transactionData: [String: Any] = [
            "userId": userId,
            "type": "subscription",
            "amount": -amount, // Negative because it's a charge
            "description": "\(tier.displayName) Subscription",
            "timestamp": Timestamp(date: Date()),
            "status": "completed"
        ]
        
        try await db.collection("transactions").addDocument(data: transactionData)
    }
    
    // MARK: - Helper Methods
    
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
}

// MARK: - Subscription Errors

enum SubscriptionError: LocalizedError {
    case userNotFound
    case insufficientBalance
    case invalidTier
    case subscriptionNotFound
    
    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "User not found"
        case .insufficientBalance:
            return "Insufficient balance to upgrade subscription"
        case .invalidTier:
            return "Invalid subscription tier"
        case .subscriptionNotFound:
            return "Subscription not found"
        }
    }
}
