//
//  AuthDebugService.swift
//  Dash
//
//  Created by Assistant on 04/07/2025.
//

import Foundation
import SwiftUI

class AuthDebugService {
    static let shared = AuthDebugService()
    
    private init() {}
    
    // MARK: - Debug Logging
    
    func logAuthFlow(_ message: String, level: LogLevel = .info) {
        let timestamp = DateFormatter.debugFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] [AUTH] [\(level.rawValue)] \(message)"
        
        print(logMessage)
        
        // Store in UserDefaults for debugging (keep last 100 entries)
        var logs = getStoredLogs()
        logs.append(logMessage)
        
        if logs.count > 100 {
            logs = Array(logs.suffix(100))
        }
        
        UserDefaults.standard.set(logs, forKey: "auth_debug_logs")
    }
    
    func logSecurityFlow(_ message: String, level: LogLevel = .info) {
        let timestamp = DateFormatter.debugFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] [SECURITY] [\(level.rawValue)] \(message)"
        
        print(logMessage)
        
        // Store in UserDefaults for debugging
        var logs = getStoredLogs()
        logs.append(logMessage)
        
        if logs.count > 100 {
            logs = Array(logs.suffix(100))
        }
        
        UserDefaults.standard.set(logs, forKey: "auth_debug_logs")
    }
    
    func logNotificationFlow(_ message: String, level: LogLevel = .info) {
        let timestamp = DateFormatter.debugFormatter.string(from: Date())
        let logMessage = "[\(timestamp)] [NOTIFICATION] [\(level.rawValue)] \(message)"
        
        print(logMessage)
        
        // Store in UserDefaults for debugging
        var logs = getStoredLogs()
        logs.append(logMessage)
        
        if logs.count > 100 {
            logs = Array(logs.suffix(100))
        }
        
        UserDefaults.standard.set(logs, forKey: "auth_debug_logs")
    }
    
    private func getStoredLogs() -> [String] {
        return UserDefaults.standard.stringArray(forKey: "auth_debug_logs") ?? []
    }
    
    // MARK: - State Monitoring
    
    @MainActor
    func getCurrentAuthState() -> AuthStateSnapshot {
        let authViewModel = AuthViewModel.shared
        let securityViewModel = SecurityViewModel()
        
        return AuthStateSnapshot(
            timestamp: Date(),
            userSession: authViewModel.userSession?.uid,
            currentUser: authViewModel.currentUser?.email,
            authenticationState: authViewModel.authenticationState,
            isPinSet: securityViewModel.isPinSet,
            isSecurityAuthenticated: securityViewModel.isAuthenticated,
            isPromptingForPin: securityViewModel.isPromptingForPin,
            failedPinAttempts: securityViewModel.failedPinAttempts,
            isLockedOut: securityViewModel.isLockedOut,
            twoFactorEnabled: authViewModel.currentUser?.twoFactorEnabled ?? false
        )
    }
    
    func logStateTransition(from oldState: AuthStateSnapshot?, to newState: AuthStateSnapshot, reason: String) {
        let message = """
        State Transition: \(reason)
        From: \(oldState?.description ?? "nil")
        To: \(newState.description)
        """
        logAuthFlow(message, level: .debug)
    }
    
    // MARK: - Error Tracking
    
    func logError(_ error: Error, context: String) {
        let message = "Error in \(context): \(error.localizedDescription)"
        logAuthFlow(message, level: .error)
    }
    
    func logCriticalError(_ message: String, context: String) {
        let fullMessage = "CRITICAL ERROR in \(context): \(message)"
        logAuthFlow(fullMessage, level: .critical)
    }
    
    // MARK: - Performance Monitoring
    
    func measureAuthOperation<T>(_ operation: () throws -> T, name: String) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try operation()
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        logAuthFlow("Operation '\(name)' completed in \(String(format: "%.3f", timeElapsed))s", level: .performance)
        
        return result
    }
    
    // MARK: - Debug UI Helpers
    
    @MainActor
    func getDebugInfo() -> String {
        let state = getCurrentAuthState()
        let logs = getStoredLogs().suffix(20).joined(separator: "\n")
        
        return """
        === DASH AUTH DEBUG INFO ===
        Generated: \(DateFormatter.debugFormatter.string(from: Date()))
        
        Current State:
        \(state.description)
        
        Recent Logs:
        \(logs)
        
        === END DEBUG INFO ===
        """
    }
    
    func clearDebugLogs() {
        UserDefaults.standard.removeObject(forKey: "auth_debug_logs")
        logAuthFlow("Debug logs cleared", level: .info)
    }
}

// MARK: - Supporting Types

enum LogLevel: String {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case critical = "CRITICAL"
    case performance = "PERF"
}

struct AuthStateSnapshot {
    let timestamp: Date
    let userSession: String?
    let currentUser: String?
    let authenticationState: AuthenticationState
    let isPinSet: Bool
    let isSecurityAuthenticated: Bool
    let isPromptingForPin: Bool
    let failedPinAttempts: Int
    let isLockedOut: Bool
    let twoFactorEnabled: Bool
    
    var description: String {
        return """
        AuthState(
          session: \(userSession ?? "nil"),
          user: \(currentUser ?? "nil"),
          authState: \(authenticationState),
          pinSet: \(isPinSet),
          securityAuth: \(isSecurityAuthenticated),
          promptingPin: \(isPromptingForPin),
          failedAttempts: \(failedPinAttempts),
          lockedOut: \(isLockedOut),
          2FA: \(twoFactorEnabled)
        )
        """
    }
}

extension DateFormatter {
    static let debugFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter
    }()
}
