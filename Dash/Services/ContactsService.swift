//
//  ContactsService.swift
//  Dash
//
//  Created by Assistant on 28/06/2025.
//

import Foundation
import Contacts
import FirebaseFirestore
import Combine

class ContactsService: ObservableObject {
    static let shared = ContactsService()
    
    @Published var contactUsers: [User] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let db = Firestore.firestore()
    private let contactStore = CNContactStore()
    private var cachedContacts: [CNContact] = []
    private var lastContactsFetch: Date?
    private let cacheExpiryInterval: TimeInterval = 300 // 5 minutes
    
    private init() {}
    
    // MARK: - Request Contacts Permission
    func requestContactsPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            contactStore.requestAccess(for: .contacts) { granted, error in
                if let error = error {
                    print("ContactsService: Error requesting contacts permission: \(error.localizedDescription)")
                }
                continuation.resume(returning: granted)
            }
        }
    }
    
    // MARK: - Fetch Contact Users
    func fetchContactUsers() async {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        // Request permission first
        let hasPermission = await requestContactsPermission()
        guard hasPermission else {
            await MainActor.run {
                isLoading = false
                errorMessage = "Contacts permission is required to send money to your contacts."
            }
            return
        }
        
        do {
            // Get all contacts (with caching)
            let contacts = try await getDeviceContactsWithCache()
            
            // Extract email addresses and phone numbers from contacts
            let contactEmails = extractEmailsFromContacts(contacts)
            let contactPhoneNumbers = extractPhoneNumbersFromContacts(contacts)
            
            // Find users in Firebase that match contact emails or phone numbers (in parallel)
            async let emailUsers = findUsersForEmails(contactEmails)
            async let phoneUsers = findUsersForPhoneNumbers(contactPhoneNumbers)
            
            let (emailResults, phoneResults) = try await (emailUsers, phoneUsers)
            
            // Combine and deduplicate users
            let allUsers = combineAndDeduplicateUsers(emailUsers: emailResults, phoneUsers: phoneResults)
            
            await MainActor.run {
                self.contactUsers = allUsers
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.isLoading = false
                self.errorMessage = "Error loading contacts: \(error.localizedDescription)"
            }
        }
    }
    
    // MARK: - Get Device Contacts with Cache
    private func getDeviceContactsWithCache() async throws -> [CNContact] {
        // Check if we have cached contacts that are still valid
        if let lastFetch = lastContactsFetch,
           Date().timeIntervalSince(lastFetch) < cacheExpiryInterval,
           !cachedContacts.isEmpty {
            print("ContactsService: Using cached contacts (\(cachedContacts.count) contacts)")
            return cachedContacts
        }
        
        // Fetch fresh contacts
        let contacts = try await getDeviceContacts()
        
        // Cache the results
        cachedContacts = contacts
        lastContactsFetch = Date()
        
        print("ContactsService: Fetched and cached \(contacts.count) contacts")
        return contacts
    }
    
    // MARK: - Get Device Contacts
    private func getDeviceContacts() async throws -> [CNContact] {
        return try await withCheckedThrowingContinuation { continuation in
            let keys = [CNContactGivenNameKey, CNContactFamilyNameKey, CNContactEmailAddressesKey, CNContactPhoneNumbersKey] as [CNKeyDescriptor]
            let request = CNContactFetchRequest(keysToFetch: keys)
            
            var contacts: [CNContact] = []
            
            do {
                try contactStore.enumerateContacts(with: request) { contact, _ in
                    // Only include contacts that have email or phone number
                    if !contact.emailAddresses.isEmpty || !contact.phoneNumbers.isEmpty {
                        contacts.append(contact)
                    }
                }
                continuation.resume(returning: contacts)
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }
    
    // MARK: - Extract Emails from Contacts
    private func extractEmailsFromContacts(_ contacts: [CNContact]) -> [String] {
        var emails: [String] = []
        
        for contact in contacts {
            for emailAddress in contact.emailAddresses {
                let email = emailAddress.value as String
                emails.append(email.lowercased())
            }
        }
        
        return Array(Set(emails)) // Remove duplicates
    }
    
    // MARK: - Extract Phone Numbers from Contacts
    private func extractPhoneNumbersFromContacts(_ contacts: [CNContact]) -> [String] {
        var phoneNumbers: [String] = []
        
        for contact in contacts {
            for phoneNumber in contact.phoneNumbers {
                let number = phoneNumber.value.stringValue
                // Normalize phone number by removing spaces, dashes, parentheses
                let normalizedNumber = normalizePhoneNumber(number)
                if !normalizedNumber.isEmpty {
                    phoneNumbers.append(normalizedNumber)
                }
            }
        }
        
        return Array(Set(phoneNumbers)) // Remove duplicates
    }
    
    // MARK: - Normalize Phone Number
    private func normalizePhoneNumber(_ phoneNumber: String) -> String {
        // Remove all non-digit characters except +
        let cleaned = phoneNumber.components(separatedBy: CharacterSet(charactersIn: "0123456789+").inverted).joined()
        return cleaned
    }
    
    // MARK: - Find Users for Emails
    private func findUsersForEmails(_ emails: [String]) async throws -> [User] {
        guard !emails.isEmpty else { return [] }
        
        print("ContactsService: Searching for \(emails.count) email addresses")
        
        // Firebase has a limit of 10 items for 'in' queries, so we need to batch them
        let batchSize = 10
        var allUsers: [User] = []
        
        // Process batches in parallel for better performance
        let batches = stride(from: 0, to: emails.count, by: batchSize).map { i in
            let endIndex = min(i + batchSize, emails.count)
            return Array(emails[i..<endIndex])
        }
        
        let batchResults = try await withThrowingTaskGroup(of: [User].self) { group in
            for emailBatch in batches {
                group.addTask {
                    let querySnapshot = try await self.db.collection("users")
                        .whereField("email", in: emailBatch)
                        .getDocuments()
                    
                    return querySnapshot.documents.compactMap { document -> User? in
                        do {
                            var user = try document.data(as: User.self)
                            user.id = document.documentID
                            return user
                        } catch {
                            print("ContactsService: Error decoding user: \(error)")
                            return nil
                        }
                    }
                }
            }
            
            var results: [User] = []
            for try await batchResult in group {
                results.append(contentsOf: batchResult)
            }
            return results
        }
        
        allUsers = batchResults
        print("ContactsService: Found \(allUsers.count) users by email")
        return allUsers
    }
    
    // MARK: - Find Users for Phone Numbers
    private func findUsersForPhoneNumbers(_ phoneNumbers: [String]) async throws -> [User] {
        guard !phoneNumbers.isEmpty else { return [] }
        
        print("ContactsService: Searching for \(phoneNumbers.count) phone numbers")
        
        // Firebase has a limit of 10 items for 'in' queries, so we need to batch them
        let batchSize = 10
        
        // Process batches in parallel for better performance
        let batches = stride(from: 0, to: phoneNumbers.count, by: batchSize).map { i in
            let endIndex = min(i + batchSize, phoneNumbers.count)
            return Array(phoneNumbers[i..<endIndex])
        }
        
        let batchResults = try await withThrowingTaskGroup(of: [User].self) { group in
            for phoneNumberBatch in batches {
                group.addTask {
                    let querySnapshot = try await self.db.collection("users")
                        .whereField("phoneNumber", in: phoneNumberBatch)
                        .getDocuments()
                    
                    return querySnapshot.documents.compactMap { document -> User? in
                        do {
                            var user = try document.data(as: User.self)
                            user.id = document.documentID
                            return user
                        } catch {
                            print("ContactsService: Error decoding user: \(error)")
                            return nil
                        }
                    }
                }
            }
            
            var results: [User] = []
            for try await batchResult in group {
                results.append(contentsOf: batchResult)
            }
            return results
        }
        
        print("ContactsService: Found \(batchResults.count) users by phone number")
        return batchResults
    }
    
    // MARK: - Combine and Deduplicate Users
    private func combineAndDeduplicateUsers(emailUsers: [User], phoneUsers: [User]) -> [User] {
        var allUsers = emailUsers
        var existingUserIds = Set(emailUsers.compactMap { $0.uid })
        
        // Add phone users that aren't already in the email users list
        for phoneUser in phoneUsers {
            if let uid = phoneUser.uid, !existingUserIds.contains(uid) {
                allUsers.append(phoneUser)
                existingUserIds.insert(uid)
            }
        }
        
        return allUsers
    }
    
    // MARK: - Search Contact Users
    func searchContactUsers(query: String, excludingCurrentUser currentUser: User? = nil) -> [User] {
        var filteredUsers = contactUsers
        
        // Filter out current user if provided
        if let currentUser = currentUser {
            filteredUsers = filteredUsers.filter { user in
                // Filter by uid, email, phone number, or id to ensure current user is excluded
                return user.uid != currentUser.uid && 
                       user.email?.lowercased() != currentUser.email?.lowercased() &&
                       user.phoneNumber != currentUser.phoneNumber &&
                       user.id != currentUser.id
            }
        }
        
        // Apply search query filter
        guard !query.isEmpty else { return filteredUsers }
        
        return filteredUsers.filter { user in
            let displayName = user.displayName?.lowercased() ?? ""
            let email = user.email?.lowercased() ?? ""
            let phoneNumber = user.phoneNumber ?? ""
            let searchQuery = query.lowercased()
            
            return displayName.contains(searchQuery) || 
                   email.contains(searchQuery) || 
                   phoneNumber.contains(searchQuery)
        }
    }
    
    // MARK: - Find User by Email
    func findUserByEmail(_ email: String) async throws -> User? {
        let searchEmail = email.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        print("ContactsService: Searching for user with email: '\(searchEmail)'")
        
        // First try exact match
        var querySnapshot = try await db.collection("users")
            .whereField("email", isEqualTo: searchEmail)
            .limit(to: 1)
            .getDocuments()
        
        print("ContactsService: Exact match query returned \(querySnapshot.documents.count) documents")
        
        // If no exact match, try getting all users and search manually (for debugging)
        if querySnapshot.documents.isEmpty {
            print("ContactsService: No exact match found, checking all users for debugging...")
            let allUsersSnapshot = try await db.collection("users").getDocuments()
            print("ContactsService: Total users in database: \(allUsersSnapshot.documents.count)")
            
            for doc in allUsersSnapshot.documents {
                if let userEmail = doc.data()["email"] as? String {
                    print("ContactsService: Found user with email: '\(userEmail)'")
                    if userEmail.lowercased() == searchEmail {
                        print("ContactsService: Found matching user via manual search!")
                        querySnapshot = try await db.collection("users")
                            .whereField("email", isEqualTo: userEmail)
                            .limit(to: 1)
                            .getDocuments()
                        break
                    }
                }
            }
        }
        
        guard let document = querySnapshot.documents.first else {
            print("ContactsService: No user found with email: '\(searchEmail)'")
            return nil
        }
        
        do {
            var user = try document.data(as: User.self)
            user.id = document.documentID
            print("ContactsService: Successfully found user: \(user.displayName ?? "No name") (\(user.email ?? "No email"))")
            return user
        } catch {
            print("ContactsService: Error decoding user: \(error)")
            throw error
        }
    }
    
    // MARK: - Find User by Phone Number
    func findUserByPhoneNumber(_ phoneNumber: String) async throws -> User? {
        let normalizedPhoneNumber = normalizePhoneNumber(phoneNumber)
        print("ContactsService: Searching for user with phone number: '\(normalizedPhoneNumber)'")
        
        let querySnapshot = try await db.collection("users")
            .whereField("phoneNumber", isEqualTo: normalizedPhoneNumber)
            .limit(to: 1)
            .getDocuments()
        
        print("ContactsService: Phone number query returned \(querySnapshot.documents.count) documents")
        
        guard let document = querySnapshot.documents.first else {
            print("ContactsService: No user found with phone number: '\(normalizedPhoneNumber)'")
            return nil
        }
        
        do {
            var user = try document.data(as: User.self)
            user.id = document.documentID
            print("ContactsService: Successfully found user by phone: \(user.displayName ?? "No name") (\(user.phoneNumber ?? "No phone"))")
            return user
        } catch {
            print("ContactsService: Error decoding user: \(error)")
            throw error
        }
    }
    
    // MARK: - Check Contacts Permission Status
    func getContactsPermissionStatus() -> CNAuthorizationStatus {
        return CNContactStore.authorizationStatus(for: .contacts)
    }
}
