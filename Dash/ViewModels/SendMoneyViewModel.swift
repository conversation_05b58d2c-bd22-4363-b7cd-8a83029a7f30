import Foundation
import FirebaseFirestore
import Combine
import FirebaseAuth

class SendMoneyViewModel: ObservableObject {
    @Published var searchText = ""
    @Published var amountString: String = "0"
    @Published var selectedUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    @Published var manualEmail: String = ""
    @Published var isSearchingManualUser = false
    @Published var foundUser: User? // User found via manual search, but not auto-selected

    @Published private var contactsService = ContactsService.shared
    private var cancellables = Set<AnyCancellable>()

    var amount: Double {
        (Double(amountString) ?? 0) / 100.0
    }

    var filteredUsers: [User] {
        return contactsService.searchContactUsers(query: searchText, excludingCurrentUser: getCurrentUser())
    }
    
    var contactUsers: [User] {
        return contactsService.contactUsers
    }

    init() {
        setupSubscribers()
        loadContactUsers()
    }
    
    private func setupSubscribers() {
        // Subscribe to contacts service loading state
        contactsService.$isLoading
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLoading, on: self)
            .store(in: &cancellables)
        
        // Subscribe to contacts service error messages
        contactsService.$errorMessage
            .receive(on: DispatchQueue.main)
            .assign(to: \.errorMessage, on: self)
            .store(in: &cancellables)
    }

    func loadContactUsers() {
        Task {
            await contactsService.fetchContactUsers()
        }
    }
    
    private func getCurrentUser() -> User? {
        // Get current user from Firebase Auth
        guard let currentUserUID = Auth.auth().currentUser?.uid else { return nil }
        
        // Try to find the current user in the contacts list first
        if let currentUser = contactsService.contactUsers.first(where: { $0.uid == currentUserUID }) {
            return currentUser
        }
        
        // If not found in contacts, create a basic user object for filtering
        if let email = Auth.auth().currentUser?.email {
            return User(id: currentUserUID, uid: currentUserUID, email: email, displayName: nil, balance: nil, phoneNumber: nil)
        }
        
        return nil
    }
    
    func searchUserByEmailOrPhone() {
        guard !manualEmail.isEmpty else {
            errorMessage = "Please enter an email address or phone number."
            return
        }
        
        let isEmail = manualEmail.contains("@") && manualEmail.contains(".")
        let isPhone = manualEmail.allSatisfy { $0.isNumber || $0 == "+" || $0 == "-" || $0 == " " || $0 == "(" || $0 == ")" }
        
        // Basic validation
        guard isEmail || isPhone else {
            errorMessage = "Please enter a valid email address or phone number."
            return
        }
        
        // Check if user is trying to send to themselves
        if let currentUser = getCurrentUser() {
            if isEmail && manualEmail.lowercased() == currentUser.email?.lowercased() {
                errorMessage = "You cannot send money to yourself."
                return
            }
            if isPhone && manualEmail == currentUser.phoneNumber {
                errorMessage = "You cannot send money to yourself."
                return
            }
        }
        
        isSearchingManualUser = true
        errorMessage = nil
        foundUser = nil // Clear any previous found user
        
        Task {
            do {
                let user: User?
                if isEmail {
                    user = try await contactsService.findUserByEmail(manualEmail)
                } else {
                    user = try await contactsService.findUserByPhoneNumber(manualEmail)
                }
                
                await MainActor.run {
                    self.isSearchingManualUser = false
                    
                    if let user = user {
                        // Double-check that the found user is not the current user
                        if let currentUser = self.getCurrentUser(),
                           (user.uid == currentUser.uid || 
                            user.email?.lowercased() == currentUser.email?.lowercased() ||
                            user.phoneNumber == currentUser.phoneNumber ||
                            user.id == currentUser.id) {
                            self.errorMessage = "You cannot send money to yourself."
                            self.foundUser = nil
                        } else {
                            // Don't auto-select, just show as found user
                            self.foundUser = user
                            self.errorMessage = nil
                        }
                    } else {
                        let searchType = isEmail ? "email" : "phone number"
                        self.errorMessage = "No user found with \(searchType): \(self.manualEmail)"
                        self.foundUser = nil
                    }
                }
            } catch {
                await MainActor.run {
                    self.isSearchingManualUser = false
                    self.errorMessage = "Error searching for user: \(error.localizedDescription)"
                    self.foundUser = nil
                }
            }
        }
    }
    
    func selectFoundUser() {
        if let foundUser = foundUser {
            selectedUser = foundUser
            manualEmail = ""
            self.foundUser = nil
        }
    }
    
    func clearFoundUser() {
        foundUser = nil
        manualEmail = ""
    }

    func appendDigit(_ digit: String) {
        if amountString == "0" {
            amountString = digit
        } else {
            amountString += digit
        }
    }

    func deleteDigit() {
        amountString = String(amountString.dropLast())
        if amountString.isEmpty {
            amountString = "0"
        }
    }

    @MainActor
    func sendMoney(authViewModel: AuthViewModel) {
        guard let toUser = selectedUser else {
            errorMessage = "Please select a user to send to."
            return
        }
        
        guard let fromUser = authViewModel.currentUser else {
            errorMessage = "You must be logged in to send money."
            return
        }
        
        guard amount > 0 else {
            errorMessage = "Please enter an amount."
            return
        }
        
        guard let fromUserBalance = fromUser.balance, fromUserBalance >= amount else {
            errorMessage = "You do not have enough money to send."
            return
        }
        
        guard let toUserUID = toUser.uid else {
            errorMessage = "Recipient user ID not found."
            return
        }
        
        isLoading = true
        errorMessage = nil
        successMessage = nil
        
        Task {
            do {
                let transactionId = try await TransactionService.shared.sendMoney(
                    to: toUserUID,
                    amount: amount,
                    description: "Money transfer"
                )
                
                await MainActor.run {
                    self.isLoading = false
                    self.successMessage = "Successfully sent \(self.amount.toCurrency()) to \(toUser.displayName ?? toUser.email ?? "user")"
                    
                    // Update the current user's balance in the auth view model
                    if var currentUser = authViewModel.currentUser {
                        currentUser.balance = (currentUser.balance ?? 0) - self.amount
                        authViewModel.currentUser = currentUser
                    }
                    
                    // Refresh the user's data from Firestore to ensure balance is up to date
                    authViewModel.refreshCurrentUser()
                    
                    // Refresh transactions to ensure the new transaction appears in history
                    TransactionService.shared.refreshTransactions()
                    
                    // Reset form
                    self.amountString = "0"
                    self.selectedUser = nil
                    self.searchText = ""
                    self.manualEmail = ""
                    self.foundUser = nil
                }
            } catch let error as TransactionError {
                await MainActor.run {
                    self.isLoading = false
                    
                    // Provide user-friendly error messages
                    switch error {
                    case .userNotAuthenticated:
                        self.errorMessage = "Please log in again to send money."
                    case .insufficientFunds:
                        self.errorMessage = "You don't have enough funds to complete this transaction."
                    case .userNotFound:
                        self.errorMessage = "The recipient could not be found. Please try again."
                    case .invalidAmount:
                        self.errorMessage = "Please enter a valid amount."
                    case .networkError:
                        self.errorMessage = "Network error. Please check your connection and try again."
                    case .transactionFailed(let message):
                        self.errorMessage = "Transaction failed: \(message)"
                    }
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                    self.errorMessage = "An unexpected error occurred. Please try again."
                }
            }
        }
    }
}
