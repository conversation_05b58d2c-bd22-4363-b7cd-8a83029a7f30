import Foundation
import LocalAuthentication
import Combine

@MainActor
class SecurityViewModel: ObservableObject {
    @Published var isPinSet: Bool = false
    @Published var isAuthenticated: Bool = false
    @Published var isPromptingForPin: Bool = false
    @Published var failedPinAttempts: Int = 0
    @Published var isLockedOut: Bool = false
    @Published var requiresReauthentication: Bool = false
    @Published var requiresPinReset: Bool = false

    private var cancellables = Set<AnyCancellable>()

    private var pinKey: String? {
        // We need a logged in user to associate the PIN with.
        guard let userId = AuthViewModel.shared.userSession?.uid else { return nil }
        return "user-pin-\(userId)"
    }
    
    private var pinAttemptsKey: String? {
        guard let userId = AuthViewModel.shared.userSession?.uid else { return nil }
        return "pin-attempts-\(userId)"
    }
    
    private let maxPinAttempts = 5

    init() {
        checkPinStatus()
        loadFailedAttempts()
        checkPinResetRequired()
        setupUserSessionObserver()
    }

    private func setupUserSessionObserver() {
        // Observe changes to the user session
        AuthViewModel.shared.$userSession
            .sink { [weak self] userSession in
                DispatchQueue.main.async {
                    print("DEBUG: SecurityViewModel - User session changed: \(userSession?.uid ?? "nil")")

                    if userSession != nil {
                        // User logged in - check PIN reset requirements FIRST
                        self?.checkPinResetRequired()

                        // If PIN reset is required, don't load failed attempts or check PIN status
                        if self?.requiresPinReset == true {
                            print("DEBUG: PIN reset required - skipping PIN status check and failed attempts")
                            self?.isPinSet = false
                            self?.failedPinAttempts = 0
                            self?.isLockedOut = false
                        } else {
                            // Normal login flow
                            self?.checkPinStatus()
                            self?.loadFailedAttempts()
                        }

                        self?.isLockedOut = false

                        print("DEBUG: After login - isPinSet: \(self?.isPinSet ?? false), requiresPinReset: \(self?.requiresPinReset ?? false)")
                    } else {
                        // User logged out - don't reset requiresPinReset flag
                        print("DEBUG: User logged out - preserving PIN reset requirement")
                    }
                }
            }
            .store(in: &cancellables)
    }
    
    func checkPinStatus() {
        guard let pinKey = pinKey else {
            print("DEBUG: checkPinStatus - No pinKey available, setting isPinSet to false")
            self.isPinSet = false
            return
        }

        let pinExists = KeychainService.shared.load(key: pinKey) != nil
        print("DEBUG: checkPinStatus - pinKey: \(pinKey), pinExists: \(pinExists)")
        self.isPinSet = pinExists
    }

    func savePin(_ pin: String) {
        guard let pinKey = pinKey, let data = pin.data(using: .utf8) else { return }
        let status = KeychainService.shared.save(key: pinKey, data: data)
        if status == noErr {
            self.isPinSet = true
            self.isAuthenticated = true
            self.failedPinAttempts = 0
            self.isLockedOut = false
            self.requiresReauthentication = false
            self.isPromptingForPin = false

            // Clear PIN reset requirement when new PIN is set
            clearPinResetRequirement()

            // Save the reset failed attempts
            saveFailedAttempts()
        }
    }

    func authenticateWithPin(_ pin: String) -> Bool {
        guard let pinKey = pinKey, let savedPinData = KeychainService.shared.load(key: pinKey) else {
            return false
        }
        let savedPin = String(data: savedPinData, encoding: .utf8)
        let success = pin == savedPin
        
        if success {
            print("DEBUG: PIN authentication successful")
            self.isAuthenticated = true
            self.failedPinAttempts = 0
            self.isLockedOut = false
            self.requiresReauthentication = false
            self.isPromptingForPin = false
            saveFailedAttempts()

            // Log successful authentication
            if let userId = AuthViewModel.shared.userSession?.uid {
                SecurityAuditService.shared.logSecurityEvent(.pinAuthenticated, for: userId)
            }

            // Check if 2FA is required after PIN authentication
            DispatchQueue.main.async {
                self.checkFor2FARequirement()
            }
        } else {
            print("DEBUG: PIN authentication failed")
            self.failedPinAttempts += 1
            saveFailedAttempts()

            // Log failed attempt
            if let userId = AuthViewModel.shared.userSession?.uid {
                SecurityAuditService.shared.logSecurityEvent(.pinFailed, for: userId, metadata: [
                    "attempts": failedPinAttempts
                ])
            }

            print("DEBUG: Failed attempts: \(self.failedPinAttempts)/\(maxPinAttempts)")

            if self.failedPinAttempts >= maxPinAttempts {
                print("DEBUG: Max attempts reached, calling handleMaxPinAttemptsReached")
                handleMaxPinAttemptsReached()
            }
        }
        
        return success
    }

    func authenticateWithBiometrics() {
        let context = LAContext()
        var error: NSError?

        if context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) {
            let reason = "Log in to your account"
            context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, authenticationError in
                DispatchQueue.main.async {
                    if success {
                        self.isAuthenticated = true
                    }
                }
            }
        }
    }
    
    func authenticateWithBiometrics(completion: @escaping (Bool) -> Void) {
        let context = LAContext()
        var error: NSError?

        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            print("DEBUG: Biometric authentication not available: \(error?.localizedDescription ?? "Unknown error")")
            // Biometrics not available, fall back to PIN
            DispatchQueue.main.async {
                self.isPromptingForPin = true
                completion(false)
            }
            return
        }

        let reason = "Use Face ID or Touch ID to unlock your account"
        context.evaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, localizedReason: reason) { success, authenticationError in
            DispatchQueue.main.async {
                if success {
                    print("DEBUG: Biometric authentication successful")
                    self.isAuthenticated = true
                    completion(true)
                } else {
                    print("DEBUG: Biometric authentication failed: \(authenticationError?.localizedDescription ?? "Unknown error")")
                    self.isPromptingForPin = true
                    completion(false)
                }
            }
        }
    }
    
    func logout() {
        self.isAuthenticated = false
        self.requiresReauthentication = false
        self.isLockedOut = false
        self.failedPinAttempts = 0
        self.isPromptingForPin = false
        // Don't reset requiresPinReset here - it needs to persist for when user logs back in

        // Clear any stored PIN attempts for the previous user
        if let attemptsKey = pinAttemptsKey {
            _ = KeychainService.shared.delete(key: attemptsKey)
        }
    }
    
    private func loadFailedAttempts() {
        guard let key = pinAttemptsKey else {
            print("DEBUG: loadFailedAttempts - No pinAttemptsKey available")
            return
        }

        if let data = KeychainService.shared.load(key: key),
           let attemptsString = String(data: data, encoding: .utf8),
           let attempts = Int(attemptsString) {
            print("DEBUG: loadFailedAttempts - Loaded \(attempts) failed attempts for key \(key)")
            self.failedPinAttempts = attempts
            self.isLockedOut = attempts >= maxPinAttempts
            print("DEBUG: loadFailedAttempts - isLockedOut set to \(self.isLockedOut)")
        } else {
            print("DEBUG: loadFailedAttempts - No failed attempts data found for key \(key)")
            self.failedPinAttempts = 0
            self.isLockedOut = false
        }
    }
    
    private func saveFailedAttempts() {
        guard let key = pinAttemptsKey,
              let data = String(failedPinAttempts).data(using: .utf8) else { return }
        _ = KeychainService.shared.save(key: key, data: data)
    }
    
    private func handleMaxPinAttemptsReached() {
        print("DEBUG: Max PIN attempts reached, handling lockout")
        self.isLockedOut = true

        // Dismiss any active keyboard/input focus
        self.isPromptingForPin = false

        // Mark PIN reset required IMMEDIATELY before any logout
        if let userId = AuthViewModel.shared.userSession?.uid {
            markPinResetRequired(for: userId)

            // Log security event
            SecurityAuditService.shared.logSecurityEvent(.accountLocked, for: userId, metadata: [
                "reason": "max_pin_attempts"
            ])
        }

        // Delay logout to allow UI to update and show lockout message
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            print("DEBUG: Executing delayed logout after PIN lockout")
            self.resetPinAndLogout()
        }
    }
    
    func resetPinAndLogout() {
        print("DEBUG: resetPinAndLogout called")

        // Log security event before clearing user session
        if let userId = AuthViewModel.shared.userSession?.uid {
            print("DEBUG: resetPinAndLogout - Processing for user \(userId)")
            SecurityAuditService.shared.logSecurityEvent(.pinReset, for: userId, metadata: [
                "reason": "max_attempts_reached"
            ])

            // Mark that PIN reset is required for this user (again, to be sure)
            markPinResetRequired(for: userId)
        } else {
            print("DEBUG: resetPinAndLogout - No user session available")
        }
        
        // Remove PIN from keychain
        if let pinKey = pinKey {
            let deleteStatus = KeychainService.shared.delete(key: pinKey)
            print("DEBUG: resetPinAndLogout - Deleted PIN with key \(pinKey), status: \(deleteStatus)")
        }

        // Reset PIN attempts
        if let attemptsKey = pinAttemptsKey {
            let deleteStatus = KeychainService.shared.delete(key: attemptsKey)
            print("DEBUG: resetPinAndLogout - Deleted PIN attempts with key \(attemptsKey), status: \(deleteStatus)")
        }
        
        // Reset state immediately
        self.isPinSet = false
        self.isAuthenticated = false
        self.failedPinAttempts = 0
        self.requiresReauthentication = false
        self.requiresPinReset = true
        
        // Keep isLockedOut true temporarily to trigger proper navigation
        // It will be reset when the user session changes
        
        // Logout user immediately
        AuthViewModel.shared.signOut()
    }
    
    private func markPinResetRequired(for userId: String) {
        let resetKey = "pin-reset-required-\(userId)"
        let data = "true".data(using: .utf8)!
        let status = KeychainService.shared.save(key: resetKey, data: data)
        print("DEBUG: markPinResetRequired - Marked PIN reset required for user \(userId), keychain status: \(status)")

        // Verify the flag was saved correctly
        if let savedData = KeychainService.shared.load(key: resetKey),
           let savedValue = String(data: savedData, encoding: .utf8) {
            print("DEBUG: markPinResetRequired - Verification: saved value is '\(savedValue)'")
        } else {
            print("DEBUG: markPinResetRequired - ERROR: Failed to verify saved PIN reset flag")
        }
    }
    
    func checkPinResetRequired() {
        guard let userId = AuthViewModel.shared.userSession?.uid else {
            print("DEBUG: checkPinResetRequired - No user session, setting requiresPinReset to false")
            self.requiresPinReset = false
            return
        }

        let resetKey = "pin-reset-required-\(userId)"
        if let data = KeychainService.shared.load(key: resetKey),
           let value = String(data: data, encoding: .utf8),
           value == "true" {
            print("DEBUG: checkPinResetRequired - PIN reset required for user \(userId)")
            self.requiresPinReset = true
        } else {
            print("DEBUG: checkPinResetRequired - No PIN reset required for user \(userId)")
            self.requiresPinReset = false
        }
    }
    
    func clearPinResetRequirement() {
        guard let userId = AuthViewModel.shared.userSession?.uid else { return }
        let resetKey = "pin-reset-required-\(userId)"
        _ = KeychainService.shared.delete(key: resetKey)
        self.requiresPinReset = false
    }
    
    func requireReauthentication() {
        print("DEBUG: SecurityViewModel requireReauthentication called")
        self.isAuthenticated = false
        self.requiresReauthentication = true
        self.isPromptingForPin = false

        // Try biometric authentication first
        authenticateWithBiometrics { success in
            print("DEBUG: Biometric authentication result: \(success)")
            if !success {
                print("DEBUG: Biometric failed, showing PIN prompt")
                self.isPromptingForPin = true
            } else {
                print("DEBUG: Biometric succeeded, clearing requiresReauthentication")
                self.requiresReauthentication = false
            }
        }
    }
    
    func resetAuthenticationState() {
        self.isAuthenticated = false
        self.requiresReauthentication = false
        self.isPromptingForPin = false
    }
    
    func getRemainingAttempts() -> Int {
        return max(0, maxPinAttempts - failedPinAttempts)
    }

    // Debug method to manually trigger PIN reset for testing
    func debugTriggerPinReset() {
        print("DEBUG: Manually triggering PIN reset for testing")
        self.failedPinAttempts = maxPinAttempts
        handleMaxPinAttemptsReached()
    }

    // Debug method to manually clear all PIN data for testing
    func debugClearAllPinData() {
        print("DEBUG: Manually clearing all PIN data for testing")

        guard let userId = AuthViewModel.shared.userSession?.uid else {
            print("DEBUG: No user session for clearing PIN data")
            return
        }

        // Clear PIN
        let pinKey = "user-pin-\(userId)"
        let pinDeleteStatus = KeychainService.shared.delete(key: pinKey)
        print("DEBUG: Deleted PIN, status: \(pinDeleteStatus)")

        // Clear failed attempts
        let attemptsKey = "pin-attempts-\(userId)"
        let attemptsDeleteStatus = KeychainService.shared.delete(key: attemptsKey)
        print("DEBUG: Deleted failed attempts, status: \(attemptsDeleteStatus)")

        // Clear PIN reset flag
        let resetKey = "pin-reset-required-\(userId)"
        let resetDeleteStatus = KeychainService.shared.delete(key: resetKey)
        print("DEBUG: Deleted PIN reset flag, status: \(resetDeleteStatus)")

        // Reset state
        self.isPinSet = false
        self.failedPinAttempts = 0
        self.isLockedOut = false
        self.requiresPinReset = false
        self.isAuthenticated = false
        self.requiresReauthentication = false

        print("DEBUG: All PIN data cleared and state reset")
    }
    
    private func checkFor2FARequirement() {
        print("DEBUG: Checking for 2FA requirement after PIN authentication")
        
        // Trigger AuthViewModel to check authentication state
        // This will handle the transition to 2FA if needed
        AuthViewModel.shared.checkAuthenticationState()
    }
}
