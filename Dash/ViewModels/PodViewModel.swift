import Foundation
import Firebase
import FirebaseFirestore
import Combine

@MainActor
class PodViewModel: ObservableObject {
    static let shared = PodViewModel()
    
    @Published var pods: [Pod] = []
    @Published var currentPod: Pod?
    @Published var podTransactions: [PodTransaction] = []
    @Published var paymentRequests: [PaymentRequest] = []
    @Published var podInvitations: [PodInvitation] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()
    private var podListeners: [String: ListenerRegistration] = [:]
    
    private init() {}
    
    // MARK: - Pod Management
    
    func fetchUserPods(userId: String) {
        isLoading = true
        errorMessage = nil

        print("🔵 POD_DEBUG: Fetching pods for user: \(userId)")

        // Fetch all pods and filter client-side to include both created and member pods
        // This is a temporary solution - in production, you'd want to use array-contains queries
        db.collection("pods")
            .whereField("status", isEqualTo: "active")
            .addSnapshotListener { [weak self] snapshot, error in
                DispatchQueue.main.async {
                    self?.isLoading = false

                    if let error = error {
                        print("🔴 POD_DEBUG: Error fetching pods: \(error.localizedDescription)")
                        self?.errorMessage = "Failed to fetch pods: \(error.localizedDescription)"
                        return
                    }

                    guard let documents = snapshot?.documents else {
                        print("🔴 POD_DEBUG: No documents found")
                        self?.pods = []
                        return
                    }

                    print("🔵 POD_DEBUG: Processing \(documents.count) total pods")

                    let allPods = documents.compactMap { document -> Pod? in
                        do {
                            return try document.data(as: Pod.self)
                        } catch {
                            print("🔴 POD_DEBUG: Error decoding pod: \(error)")
                            return nil
                        }
                    }

                    // Filter pods where user is either creator or member
                    let userPods = allPods.filter { pod in
                        let isCreator = pod.createdBy == userId
                        let isMember = pod.members.contains { $0.userId == userId && $0.isActive }
                        let result = isCreator || isMember

                        if result {
                            print("🟢 POD_DEBUG: User is part of pod: \(pod.name) (creator: \(isCreator), member: \(isMember))")
                        }

                        return result
                    }.sorted { $0.updatedAt > $1.updatedAt }

                    self?.pods = userPods
                    print("🟢 POD_DEBUG: Fetched \(userPods.count) pods for user (out of \(allPods.count) total)")

                    // Recalculate balances for all user pods to ensure they're up to date
                    Task {
                        for pod in userPods {
                            if let podId = pod.id {
                                await self?.calculateMemberBalances(for: podId)
                            }
                        }
                    }
                }
            }
    }
    
    func createPod(_ pod: Pod) async -> Bool {
        isLoading = true
        errorMessage = nil

        print("🔵 POD_DEBUG: Starting pod creation...")
        print("🔵 POD_DEBUG: Pod name: \(pod.name)")
        print("🔵 POD_DEBUG: Created by: \(pod.createdBy)")
        print("🔵 POD_DEBUG: Created by name: \(pod.createdByName)")
        print("🔵 POD_DEBUG: Members count: \(pod.members.count)")
        print("🔵 POD_DEBUG: Status: \(pod.status)")

        do {
            let documentRef = try db.collection("pods").addDocument(from: pod)
            print("🟢 POD_DEBUG: Pod created successfully with ID: \(documentRef.documentID)")

            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Pod created successfully!"
            }
            return true
        } catch {
            print("🔴 POD_DEBUG: Pod creation failed")
            print("🔴 POD_DEBUG: Error: \(error)")
            print("🔴 POD_DEBUG: Error type: \(type(of: error))")
            print("🔴 POD_DEBUG: Error description: \(error.localizedDescription)")

            let nsError = error as NSError
            print("🔴 POD_DEBUG: NSError details:")
            print("🔴 POD_DEBUG: Domain: \(nsError.domain)")
            print("🔴 POD_DEBUG: Code: \(nsError.code)")
            print("🔴 POD_DEBUG: UserInfo: \(nsError.userInfo)")

            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to create pod: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    func updatePod(_ pod: Pod) async -> Bool {
        guard let podId = pod.id else { return false }
        
        isLoading = true
        errorMessage = nil
        
        do {
            try db.collection("pods").document(podId).setData(from: pod, merge: true)
            
            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Pod updated successfully!"
            }
            return true
        } catch {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to update pod: \(error.localizedDescription)"
            }
            return false
        }
    }

    func updatePodFields(podId: String, fields: [String: Any]) async -> Bool {
        isLoading = true
        errorMessage = nil

        do {
            try await db.collection("pods").document(podId).updateData(fields)

            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Pod updated successfully!"
            }
            return true
        } catch {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to update pod: \(error.localizedDescription)"
            }
            return false
        }
    }

    func deletePod(podId: String) async -> Bool {
        isLoading = true
        errorMessage = nil
        
        do {
            // Archive the pod instead of deleting
            try await db.collection("pods").document(podId).updateData([
                "status": PodStatus.archived.rawValue,
                "updatedAt": Timestamp(date: Date())
            ])
            
            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Pod archived successfully!"
            }
            return true
        } catch {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to archive pod: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    // MARK: - Member Management
    
    func addMemberToPod(podId: String, member: PodMember) async -> Bool {
        isLoading = true
        errorMessage = nil

        print("🔵 POD_DEBUG: Adding member \(member.displayName) to pod \(podId)")

        do {
            // Get the current pod first
            let podDoc = try await db.collection("pods").document(podId).getDocument()
            guard var pod = try? podDoc.data(as: Pod.self) else {
                print("🔴 POD_DEBUG: Pod not found: \(podId)")
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = "Pod not found"
                }
                return false
            }

            print("🟢 POD_DEBUG: Found pod: \(pod.name), current members: \(pod.members.count)")

            // Check if user is already a member
            if pod.members.contains(where: { $0.userId == member.userId }) {
                print("🟡 POD_DEBUG: User \(member.userId) is already a member of pod \(podId)")
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.successMessage = "You are already a member of this pod!"
                }
                return true
            }

            // Add member to the pod
            pod.addMember(member)
            print("🔵 POD_DEBUG: Added member, new member count: \(pod.members.count)")

            // Update the pod
            try db.collection("pods").document(podId).setData(from: pod)
            print("🟢 POD_DEBUG: Successfully updated pod in Firestore")

            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Member added successfully!"
            }
            return true
        } catch {
            print("🔴 POD_DEBUG: Failed to add member: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to add member: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    func removeMemberFromPod(podId: String, userId: String) async -> Bool {
        guard let pod = pods.first(where: { $0.id == podId }) else { return false }
        
        var updatedPod = pod
        updatedPod.removeMember(userId: userId)
        
        return await updatePod(updatedPod)
    }
    
    func updateMemberRole(podId: String, userId: String, newRole: PodMemberRole) async -> Bool {
        guard let podIndex = pods.firstIndex(where: { $0.id == podId }),
              let memberIndex = pods[podIndex].members.firstIndex(where: { $0.userId == userId }) else {
            return false
        }
        
        var updatedPod = pods[podIndex]
        updatedPod.members[memberIndex].role = newRole
        updatedPod.updatedAt = Date()
        
        return await updatePod(updatedPod)
    }
    
    // MARK: - Transaction Management
    
    func fetchPodTransactions(podId: String) {
        print("🔵 POD_DEBUG: Fetching transactions for pod: \(podId)")

        // Temporary: Use simple query without ordering to avoid index requirement
        // TODO: Switch back to ordered query once Firestore indexes are built
        db.collection("podTransactions")
            .whereField("podId", isEqualTo: podId)
            .addSnapshotListener { [weak self] snapshot, error in
                DispatchQueue.main.async {
                    if let error = error {
                        print("🔴 POD_DEBUG: Error fetching transactions: \(error.localizedDescription)")
                        self?.errorMessage = "Failed to fetch transactions: \(error.localizedDescription)"
                        return
                    }

                    guard let documents = snapshot?.documents else {
                        print("🟡 POD_DEBUG: No transactions found for pod")
                        self?.podTransactions = []
                        return
                    }

                    print("🟢 POD_DEBUG: Found \(documents.count) transactions")

                    // Sort manually in memory for now
                    self?.podTransactions = documents.compactMap { document in
                        try? document.data(as: PodTransaction.self)
                    }.sorted { $0.createdAt > $1.createdAt }
                }
            }
    }
    
    func addTransaction(_ transaction: PodTransaction) async -> Bool {
        isLoading = true
        errorMessage = nil

        print("🔵 POD_DEBUG: Adding transaction to pod: \(transaction.podId)")
        print("🔵 POD_DEBUG: Transaction type: \(transaction.type)")
        print("🔵 POD_DEBUG: Amount: \(transaction.amount)")
        print("🔵 POD_DEBUG: Paid by: \(transaction.paidBy)")
        print("🔵 POD_DEBUG: Split among: \(transaction.splitAmong)")

        do {
            let _ = try db.collection("podTransactions").addDocument(from: transaction)

            // Update pod totals and member balances
            try await updatePodTotals(podId: transaction.podId)
            await calculateMemberBalances(for: transaction.podId)

            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Transaction added successfully!"
            }
            return true
        } catch {
            print("🔴 POD_DEBUG: Error adding transaction: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to add transaction: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    private func updatePodTotals(podId: String) async throws {
        let transactions = try await db.collection("podTransactions")
            .whereField("podId", isEqualTo: podId)
            .getDocuments()
        
        var totalExpenses: Double = 0
        var totalPayments: Double = 0
        
        for document in transactions.documents {
            if let transaction = try? document.data(as: PodTransaction.self) {
                switch transaction.type {
                case .expense:
                    totalExpenses += transaction.amount
                case .payment, .settlement:
                    totalPayments += transaction.amount
                }
            }
        }
        
        try await db.collection("pods").document(podId).updateData([
            "totalExpenses": totalExpenses,
            "totalPayments": totalPayments,
            "updatedAt": Timestamp(date: Date())
        ])
    }
    
    // MARK: - Payment Requests
    
    func createPaymentRequest(_ request: PaymentRequest) async -> Bool {
        isLoading = true
        errorMessage = nil
        
        do {
            let _ = try db.collection("paymentRequests").addDocument(from: request)
            
            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Payment request sent!"
            }
            return true
        } catch {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to send payment request: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    func fetchPaymentRequests(userId: String) {
        print("🔵 POD_DEBUG: Fetching payment requests for user: \(userId)")

        // Temporary: Use simple query to avoid composite index requirement
        db.collection("paymentRequests")
            .whereField("toUserId", isEqualTo: userId)
            .addSnapshotListener { [weak self] snapshot, error in
                DispatchQueue.main.async {
                    if let error = error {
                        print("🔴 POD_DEBUG: Error fetching payment requests: \(error.localizedDescription)")
                        self?.errorMessage = "Failed to fetch payment requests: \(error.localizedDescription)"
                        return
                    }

                    guard let documents = snapshot?.documents else {
                        self?.paymentRequests = []
                        return
                    }

                    // Filter and sort manually in memory
                    self?.paymentRequests = documents.compactMap { document in
                        try? document.data(as: PaymentRequest.self)
                    }.filter { $0.status == .pending }
                     .sorted { $0.createdAt > $1.createdAt }
                }
            }
    }
    
    func respondToPaymentRequest(requestId: String, response: PaymentRequestStatus) async -> Bool {
        isLoading = true
        errorMessage = nil
        
        do {
            try await db.collection("paymentRequests").document(requestId).updateData([
                "status": response.rawValue,
                "respondedAt": Timestamp(date: Date())
            ])
            
            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Payment request \(response.displayName.lowercased())!"
            }
            return true
        } catch {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to respond to payment request: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    // MARK: - Invitations
    
    func createInvitation(_ invitation: PodInvitation) async -> Bool {
        isLoading = true
        errorMessage = nil

        print("🔵 POD_DEBUG: Creating invitation with code: \(invitation.inviteCode)")
        print("🔵 POD_DEBUG: Pod ID: \(invitation.podId)")
        print("🔵 POD_DEBUG: Pod Name: \(invitation.podName)")
        print("🔵 POD_DEBUG: Invited by: \(invitation.invitedByName)")
        print("🔵 POD_DEBUG: Expires at: \(invitation.expiresAt)")

        do {
            let documentRef = try db.collection("podInvitations").addDocument(from: invitation)
            print("🟢 POD_DEBUG: Invitation created successfully with document ID: \(documentRef.documentID)")

            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Invitation sent!"
            }
            return true
        } catch {
            print("🔴 POD_DEBUG: Failed to create invitation: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to send invitation: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    func fetchUserInvitations(userId: String) {
        print("🔵 POD_DEBUG: Fetching invitations for user: \(userId)")

        // Temporary: Use simple query to avoid composite index requirement
        db.collection("podInvitations")
            .whereField("invitedUserId", isEqualTo: userId)
            .addSnapshotListener { [weak self] snapshot, error in
                DispatchQueue.main.async {
                    if let error = error {
                        print("🔴 POD_DEBUG: Error fetching invitations: \(error.localizedDescription)")
                        self?.errorMessage = "Failed to fetch invitations: \(error.localizedDescription)"
                        return
                    }

                    guard let documents = snapshot?.documents else {
                        self?.podInvitations = []
                        return
                    }

                    // Filter and sort manually in memory
                    self?.podInvitations = documents.compactMap { document in
                        try? document.data(as: PodInvitation.self)
                    }.filter { $0.status == .pending }
                     .sorted { $0.createdAt > $1.createdAt }
                }
            }
    }
    
    func respondToInvitation(invitationId: String, response: PodInvitationStatus, userId: String, userName: String) async -> Bool {
        isLoading = true
        errorMessage = nil

        print("🔵 POD_DEBUG: Responding to invitation \(invitationId) with response: \(response.rawValue)")
        print("🔵 POD_DEBUG: User ID: \(userId), User Name: \(userName)")

        do {
            // First, fetch the invitation to get pod details
            let invitationDoc = try await db.collection("podInvitations").document(invitationId).getDocument()
            guard let invitationData = try? invitationDoc.data(as: PodInvitation.self) else {
                print("🔴 POD_DEBUG: Failed to fetch invitation data")
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = "Failed to fetch invitation details"
                }
                return false
            }

            print("🟢 POD_DEBUG: Successfully fetched invitation for pod: \(invitationData.podId)")

            // Update invitation status
            try await db.collection("podInvitations").document(invitationId).updateData([
                "status": response.rawValue,
                "respondedAt": Timestamp(date: Date())
            ])

            print("🟢 POD_DEBUG: Successfully updated invitation status to: \(response.rawValue)")

            // If accepted, add user to pod
            if response == .accepted {
                // Fetch actual user data from Firestore
                var userEmail: String? = nil
                var userPhoneNumber: String? = nil

                do {
                    let userDoc = try await db.collection("users").document(userId).getDocument()
                    if let userData = userDoc.data() {
                        userEmail = userData["email"] as? String
                        if let phoneNum = userData["phoneNumber"] as? Int {
                            userPhoneNumber = String(phoneNum)
                        }
                    }
                } catch {
                    print("🔴 POD_DEBUG: Failed to fetch user data: \(error.localizedDescription)")
                }

                // Use actual user data instead of invitation placeholder data
                let newMember = PodMember(
                    userId: userId,
                    displayName: userName,
                    email: userEmail,
                    phoneNumber: userPhoneNumber
                )

                print("🔵 POD_DEBUG: Adding user to pod: \(invitationData.podId)")
                let addMemberSuccess = await addMemberToPod(podId: invitationData.podId, member: newMember)
                print("🔵 POD_DEBUG: Add member result: \(addMemberSuccess)")
            }

            DispatchQueue.main.async {
                self.isLoading = false
                self.successMessage = "Invitation \(response.displayName.lowercased())!"
            }
            return true
        } catch {
            print("🔴 POD_DEBUG: Failed to respond to invitation: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to respond to invitation: \(error.localizedDescription)"
            }
            return false
        }
    }
    
    // MARK: - Settlement Methods

    func settleUserBalance(podId: String, payerId: String, amount: Double) async -> Bool {
        print("🚨 SETTLEMENT_ENTRY: settleUserBalance called with podId: \(podId), payerId: \(payerId), amount: \(amount)")

        isLoading = true
        errorMessage = nil

        guard let currentUser = Auth.auth().currentUser else {
            print("🔴 SETTLEMENT_ERROR: User not authenticated")
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "User not authenticated"
            }
            return false
        }

        let currentUserId = currentUser.uid

        // Get the current user's display name from our User model or fallback to email
        var currentUserName = currentUser.displayName ?? currentUser.email ?? "Unknown User"

        // Try to get the display name from our User model if available
        if let userDoc = try? await db.collection("users").document(currentUserId).getDocument(),
           let userData = userDoc.data(),
           let displayName = userData["displayName"] as? String {
            currentUserName = displayName
        }

        print("🔵 POD_DEBUG: ========== STARTING SETTLEMENT ==========")
        print("🔵 POD_DEBUG: Pod ID: \(podId)")
        print("🔵 POD_DEBUG: Payer ID: \(payerId)")
        print("🔵 POD_DEBUG: Amount: \(amount)")
        print("🔵 POD_DEBUG: Current User ID: \(currentUserId)")
        print("🔵 POD_DEBUG: Current User Name: \(currentUserName)")

        do {
            // Get the pod to find who should receive the settlement
            guard let pod = try? await db.collection("pods").document(podId).getDocument().data(as: Pod.self) else {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = "Could not find pod for settlement"
                }
                return false
            }

            // Find the member who is owed the most money (most negative balance)
            let membersWithNegativeBalance = pod.members.filter { $0.balance < 0 && $0.userId != payerId }
            guard let receiver = membersWithNegativeBalance.min(by: { $0.balance < $1.balance }) else {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = "No one to settle with"
                }
                return false
            }

            print("🔵 POD_DEBUG: Settlement receiver: \(receiver.displayName) (\(receiver.userId))")
            print("🔵 POD_DEBUG: Payer: \(currentUserName) (\(payerId))")
            print("🔵 POD_DEBUG: Amount: \(amount)")

            // Get the payer's name from the pod members
            let payerMember = pod.members.first { $0.userId == payerId }
            let payerName = payerMember?.displayName ?? currentUserName

            // Create a settlement transaction
            let settlement = PodTransaction(
                podId: podId,
                createdBy: currentUserId,
                createdByName: currentUserName,
                type: .settlement,
                amount: amount,
                description: "Settlement payment from \(payerName) to \(receiver.displayName)",
                splitAmong: [receiver.userId], // The receiver
                paidBy: payerId, // The person settling their debt
                paidByName: payerName
            )

            print("🔵 POD_DEBUG: Created settlement transaction")
            let success = await addTransaction(settlement)
            print("🔵 POD_DEBUG: Settlement transaction result: \(success)")
            print("🔵 POD_DEBUG: About to check if success is true: \(success)")

            if success {
                print("🔵 POD_DEBUG: Pod transaction successful, now creating main transactions...")
                print("🔵 POD_DEBUG: Creating transactions for payer: \(payerId) (\(currentUserName))")
                print("🔵 POD_DEBUG: Creating transactions for receiver: \(receiver.userId) (\(receiver.displayName))")

                // Also create transactions in the main transactions collection for both users
                await createSettlementTransactions(
                    payerId: payerId,
                    payerName: currentUserName,
                    receiverId: receiver.userId,
                    receiverName: receiver.displayName,
                    amount: amount,
                    podName: pod.name
                )

                print("🔵 POD_DEBUG: Main transactions created, now updating user balances...")

                // Update user balances in the main users collection
                await updateUserBalancesForSettlement(
                    payerId: payerId,
                    receiverId: receiver.userId,
                    amount: amount
                )

                print("🔵 POD_DEBUG: User balances updated, refreshing transaction service...")

                // Force refresh the transaction service to pick up new transactions
                DispatchQueue.main.async {
                    TransactionService.shared.refreshTransactions()
                    self.successMessage = "Settlement completed successfully!"
                }
            } else {
                print("🔴 POD_DEBUG: Pod transaction failed, skipping main transactions and balance updates")
            }

            return success
        } catch {
            print("🔴 POD_DEBUG: Settlement error: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "Failed to process settlement: \(error.localizedDescription)"
            }
            return false
        }
    }

    private func createSettlementTransactions(payerId: String, payerName: String, receiverId: String, receiverName: String, amount: Double, podName: String) async {
        print("💰 SETTLEMENT: Creating main transactions - \(payerName) → \(receiverName): $\(amount)")

        do {
            let timestamp = Timestamp(date: Date())

            // Generate a unique transaction hash for security
            let transactionHash = UUID().uuidString + "_SETTLEMENT_" + String(Int(timestamp.seconds))

            // Create "Settlement Sent" transaction for the payer
            let payerTransaction = Transaction(
                name: "Settlement Sent",
                detail: "Settlement payment to \(receiverName) in \(podName)",
                amount: amount,
                type: .transfer,
                category: "Settlement",
                date: timestamp,
                status: .completed,
                recipientId: receiverId,
                recipientName: receiverName,
                senderId: payerId,
                senderName: payerName,
                transactionHash: transactionHash
            )

            // Create "Settlement Received" transaction for the receiver
            let receiverTransaction = Transaction(
                name: "Settlement Received",
                detail: "Settlement payment from \(payerName) in \(podName)",
                amount: amount,
                type: .transfer,
                category: "Settlement",
                date: timestamp,
                status: .completed,
                recipientId: receiverId,
                recipientName: receiverName,
                senderId: payerId,
                senderName: payerName,
                transactionHash: transactionHash
            )

            // Add transactions to user-specific subcollections (not global collection)
            let payerDocRef = try db.collection("users").document(payerId).collection("transactions").addDocument(from: payerTransaction)
            let receiverDocRef = try db.collection("users").document(receiverId).collection("transactions").addDocument(from: receiverTransaction)

            print("✅ SETTLEMENT: Main transactions created - Payer: \(payerDocRef.documentID), Receiver: \(receiverDocRef.documentID)")
            print("✅ SETTLEMENT: Transactions saved to user-specific collections for activity feed")

        } catch {
            print("❌ SETTLEMENT: Failed to create main transactions: \(error.localizedDescription)")
        }
    }

    private func updateUserBalancesForSettlement(payerId: String, receiverId: String, amount: Double) async {
        print("💰 SETTLEMENT: Updating user balances - $\(amount)")

        do {
            // Use a transaction to ensure both balance updates succeed or fail together
            try await db.runTransaction { transaction, errorPointer in
                // Get current balances
                let payerRef = self.db.collection("users").document(payerId)
                let receiverRef = self.db.collection("users").document(receiverId)

                do {
                    let payerDoc = try transaction.getDocument(payerRef)
                    let receiverDoc = try transaction.getDocument(receiverRef)

                    let currentPayerBalance = payerDoc.data()?["balance"] as? Double ?? 0.0
                    let currentReceiverBalance = receiverDoc.data()?["balance"] as? Double ?? 0.0

                    // Update balances
                    let newPayerBalance = currentPayerBalance - amount  // Payer sends money
                    let newReceiverBalance = currentReceiverBalance + amount  // Receiver gets money

                    print("💰 SETTLEMENT: Payer: $\(currentPayerBalance) → $\(newPayerBalance)")
                    print("💰 SETTLEMENT: Receiver: $\(currentReceiverBalance) → $\(newReceiverBalance)")

                    transaction.updateData(["balance": newPayerBalance], forDocument: payerRef)
                    transaction.updateData(["balance": newReceiverBalance], forDocument: receiverRef)

                    return nil
                } catch let fetchError as NSError {
                    errorPointer?.pointee = fetchError
                    return nil
                }
            }

            print("✅ SETTLEMENT: User balances updated successfully")

        } catch {
            print("❌ SETTLEMENT: Failed to update user balances: \(error.localizedDescription)")
        }
    }

    // MARK: - Utility Methods

    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    func calculateMemberBalances(for podId: String) async {
        // Calculate member balances silently

        do {
            // Get all transactions for this pod
            let transactionsSnapshot = try await db.collection("podTransactions")
                .whereField("podId", isEqualTo: podId)
                .getDocuments()

            // Get the current pod using the proper Firestore decoding method
            let podDocSnapshot = try await db.collection("pods").document(podId).getDocument()

            guard podDocSnapshot.exists else {
                return
            }

            guard var pod = try? podDocSnapshot.data(as: Pod.self) else {
                return
            }

            // Initialize all member balances to 0
            var memberBalances: [String: Double] = [:]
            for member in pod.members {
                memberBalances[member.userId] = 0.0
            }

            // Process each transaction
            for document in transactionsSnapshot.documents {
                guard let transaction = try? document.data(as: PodTransaction.self) else { continue }

                switch transaction.type {
                case .expense:
                    // For expenses: split the cost among all participants
                    let amountPerPerson = transaction.amount / Double(transaction.splitAmong.count)

                    // Each person in splitAmong owes their share (positive balance = owes money)
                    for userId in transaction.splitAmong {
                        memberBalances[userId, default: 0] += amountPerPerson
                    }

                    // The person who paid gets credited for the full amount (negative balance = owed money)
                    memberBalances[transaction.paidBy, default: 0] -= transaction.amount

                case .payment:
                    // For payments: reduce the amount owed by the payer
                    memberBalances[transaction.paidBy, default: 0] -= transaction.amount

                case .settlement:
                    // For settlements: this is a direct payment from payer to receiver
                    // The payer (transaction.paidBy) reduces their debt
                    memberBalances[transaction.paidBy, default: 0] -= transaction.amount

                    // The receiver (in splitAmong) gets credited
                    for receiverId in transaction.splitAmong {
                        memberBalances[receiverId, default: 0] += transaction.amount
                    }
                }
            }

            // Update member balances in the pod
            for i in 0..<pod.members.count {
                let userId = pod.members[i].userId
                let newBalance = memberBalances[userId] ?? 0.0
                pod.members[i].balance = newBalance
            }

            // Update the pod in Firestore
            try await db.collection("pods").document(podId).setData(from: pod, merge: true)

            // Update local pods array and trigger UI refresh
            DispatchQueue.main.async {
                if let index = self.pods.firstIndex(where: { $0.id == podId }) {
                    self.pods[index] = pod
                } else {
                    self.pods.append(pod)
                }

                // Force UI refresh by updating the published property
                self.objectWillChange.send()
            }

            // Debug print for member balances
            for member in pod.members {
                print("🟢 POD_DEBUG: \(member.displayName): balance = \(member.balance)")
            }

        } catch {
            print("🔴 POD_DEBUG: Error calculating member balances: \(error.localizedDescription)")
        }
    }
}
