import Foundation
import FirebaseAuth
import FirebaseCore
import FirebaseFirestore
import GoogleSignIn
import Combine
// import FirebaseFirestoreSwift

enum AuthenticationState {
    case unauthenticated
    case pendingEmailVerification
    case pendingTwoFactor
    case authenticated
}

@MainActor
class AuthViewModel: ObservableObject {
    static let shared = AuthViewModel()

    @Published var userSession: FirebaseAuth.User?
    @Published var currentUser: User?
    @Published var errorMessage: String?
    @Published var successMessage: String?
    @Published var verificationID: String?
    @Published var authenticationState: AuthenticationState = .unauthenticated
    @Published var failedLoginAttempts: Int = 0
    @Published var isLockedOut: Bool = false
    @Published var isLoading: Bool = false
    
    private var cancellables = Set<AnyCancellable>()
    private let maxLoginAttempts = 5
    
    // Key for storing 2FA authentication state
    private var twoFactorAuthKey: String? {
        guard let userId = userSession?.uid else { return nil }
        return "2fa-authenticated-\(userId)"
    }

    private init() {
        self.userSession = Auth.auth().currentUser
        setupSubscribers()
        // Restore 2FA authentication state if user session exists
        if userSession != nil {
            restoreTwoFactorAuthenticationState()
        }
    }
    
    private func setupSubscribers() {
        Auth.auth().addStateDidChangeListener { [weak self] _, user in
            self?.userSession = user
        }
        
        $userSession.sink { [weak self] user in
            guard let uid = user?.uid else {
                self?.currentUser = nil
                return
            }
            self?.fetchUser(withUid: uid)
        }
        .store(in: &cancellables)
    }
    
    func signInWithGoogle() {
        self.errorMessage = nil
        guard let clientID = FirebaseApp.app()?.options.clientID else { return }

        let config = GIDConfiguration(clientID: clientID)

        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let rootViewController = windowScene.windows.first?.rootViewController else {
            print("DEBUG: Could not find root view controller.")
            return
        }

        GIDSignIn.sharedInstance.signIn(withPresenting: rootViewController) { [weak self] result, error in
            guard let self = self else { return }
            if let error = error {
                print("DEBUG: Google sign in error: \(error.localizedDescription)")
                self.errorMessage = error.localizedDescription
                return
            }

            guard let user = result?.user, let idToken = user.idToken?.tokenString else {
                print("DEBUG: Google sign in - user or idToken is nil")
                return
            }
            let accessToken = user.accessToken.tokenString

            let credential = GoogleAuthProvider.credential(withIDToken: idToken, accessToken: accessToken)
            
            Auth.auth().signIn(with: credential) { result, error in
                if let error = error {
                    print("DEBUG: Firebase sign in error with Google creds: \(error.localizedDescription)")
                    self.errorMessage = error.localizedDescription
                    return
                }

                guard let user = result?.user else { return }

                // Check email verification status for Google sign-in
                // Note: Google accounts are typically pre-verified, but we should still check
                if !user.isEmailVerified {
                    print("DEBUG: Google user email not verified, blocking login")
                    self.userSession = user
                    self.authenticationState = .pendingEmailVerification
                    self.errorMessage = "Please verify your email address before signing in."
                    return
                }

                self.userSession = user

                // The sink on userSession will automatically trigger fetchUser.
                // We still need to create the user document if it's new.
                let db = Firestore.firestore()
                let userRef = db.collection("users").document(user.uid)
                userRef.getDocument { (document, error) in
                    if let document = document, document.exists {
                        self.fetchUser(withUid: user.uid)
                    } else {
                        self.createNewUserDocument(withUid: user.uid)
                    }
                }
            }
        }
    }
    
    func signIn(withEmail email: String, password: String) {
        self.errorMessage = nil
        self.isLoading = true
        Auth.auth().signIn(withEmail: email, password: password) { [weak self] result, error in
            if let error = error {
                print("DEBUG: Failed to sign in with error \(error.localizedDescription)")
                self?.errorMessage = error.localizedDescription
                self?.isLoading = false
                return
            }

            guard let user = result?.user else { return }

            // Check email verification status first
            if !user.isEmailVerified {
                print("DEBUG: User email not verified, blocking login")
                self?.userSession = user // Set session so we can send verification emails
                self?.authenticationState = .pendingEmailVerification

                // Automatically send verification email if user tries to sign in with unverified email
                self?.sendEmailVerification { success in
                    DispatchQueue.main.async {
                        if success {
                            self?.errorMessage = "Please verify your email address before signing in. We've sent a new verification email to your inbox."
                        } else {
                            self?.errorMessage = "Please verify your email address before signing in. Check your inbox for a verification email."
                        }
                        self?.isLoading = false
                    }
                }
                return
            }

            self?.userSession = user

            // Check if user document exists in Firestore, create if missing
            let db = Firestore.firestore()
            let userRef = db.collection("users").document(user.uid)
            userRef.getDocument { (document, error) in
                if let document = document, document.exists {
                    // Document exists, fetchUser will be called by the sink
                    print("DEBUG: User document exists for \(user.email ?? "unknown email")")

                    // Immediately check authentication state to handle 2FA
                    DispatchQueue.main.async {
                        self?.checkAuthenticationState()
                        self?.isLoading = false
                    }
                } else {
                    // Document doesn't exist, create it
                    print("DEBUG: User document missing for \(user.email ?? "unknown email"), creating new document")
                    self?.createNewUserDocument(withUid: user.uid)
                }
            }
        }
    }
    
    func signUp(withEmail email: String, password: String, displayName: String, phoneNumber: String? = nil) {
        // Clear any previous error messages and reset state
        self.errorMessage = nil
        self.isLoading = true

        // Clear any previous authentication state that might be causing issues
        self.authenticationState = .unauthenticated

        // Check if phone number is already in use
        if let phoneNumber = phoneNumber, !phoneNumber.isEmpty {
            checkPhoneNumberAvailability(phoneNumber) { [weak self] isAvailable in
                if !isAvailable {
                    self?.errorMessage = "This phone number is already registered with another account."
                    self?.isLoading = false
                    return
                }

                // Phone number is available, proceed with signup
                self?.createUserAccount(email: email, password: password, displayName: displayName, phoneNumber: phoneNumber)
            }
        } else {
            // No phone number provided, proceed with signup
            createUserAccount(email: email, password: password, displayName: displayName, phoneNumber: phoneNumber)
        }
    }
    
    private func createUserAccount(email: String, password: String, displayName: String, phoneNumber: String?) {
        Auth.auth().createUser(withEmail: email, password: password) { [weak self] result, error in
            if let error = error {
                print("DEBUG: Failed to create user with error \(error.localizedDescription)")
                self?.errorMessage = error.localizedDescription
                self?.isLoading = false
                return
            }

            guard let user = result?.user else {
                self?.isLoading = false
                return
            }
            self?.userSession = user

            // Send email verification immediately after account creation
            self?.sendEmailVerification { success in
                if success {
                    print("DEBUG: Email verification sent successfully")
                    // Set authentication state to pending email verification
                    DispatchQueue.main.async {
                        self?.authenticationState = .pendingEmailVerification
                        self?.successMessage = "Account created! Please check your email to verify your account."
                    }
                } else {
                    print("DEBUG: Failed to send email verification")
                    self?.errorMessage = "Account created but failed to send verification email. Please try signing in to resend."
                }
            }

            // Create user data with all required fields for Firestore rules
            var userData: [String: Any] = [
                "uid": user.uid,
                "email": email,
                "displayName": displayName,
                "balance": 0.0,
                "twoFactorEnabled": false,
                "securityLevel": "basic",
                "failedLoginAttempts": 0,
                "biometricEnabled": false,
                "sessionTimeout": 900,
                "requireTwoFactorForTransactions": false,
                "backupCodesUsed": [],
                "securityNotificationsEnabled": true,
                "trustedDevices": [],
                "subscription": [
                    "tier": "classic",
                    "status": "active",
                    "startDate": Timestamp(date: Date()),
                    "endDate": Timestamp(date: Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date()),
                    "autoRenew": true,
                    "priceAtPurchase": 0.0
                ]
            ]

            // Add phone number if provided
            if let phoneNumber = phoneNumber, !phoneNumber.isEmpty {
                userData["phoneNumber"] = phoneNumber
            }

            Firestore.firestore().collection("users").document(user.uid).setData(userData) { error in
                if let error = error {
                    print("DEBUG: Failed to upload user data with error \(error.localizedDescription)")
                    self?.errorMessage = "Failed to initialize user data: \(error.localizedDescription)"
                    self?.isLoading = false
                    return
                }
                print("DEBUG: Successfully created user document for \(email)")
                self?.isLoading = false
                // Don't call fetchUser() yet - user needs to verify email first
            }
        }
    }
    
    private func checkPhoneNumberAvailability(_ phoneNumber: String, completion: @escaping (Bool) -> Void) {
        let db = Firestore.firestore()
        db.collection("users")
            .whereField("phoneNumber", isEqualTo: phoneNumber)
            .getDocuments { snapshot, error in
                if let error = error {
                    print("DEBUG: Error checking phone number availability: \(error.localizedDescription)")
                    completion(true) // Allow signup if we can't check (fail open)
                    return
                }
                
                let isAvailable = snapshot?.documents.isEmpty ?? true
                completion(isAvailable)
            }
    }
    
    func sendVerificationCode(phoneNumber: String) {
        PhoneAuthProvider.provider().verifyPhoneNumber(phoneNumber, uiDelegate: nil) { [weak self] (verificationID, error) in
            if let error = error {
                self?.errorMessage = error.localizedDescription
                return
            }
            self?.verificationID = verificationID
        }
    }

    func signIn(with verificationID: String, verificationCode: String) {
        let credential = PhoneAuthProvider.provider().credential(withVerificationID: verificationID, verificationCode: verificationCode)

        Auth.auth().signIn(with: credential) { [weak self] (authResult, error) in
            if let error = error {
                self?.errorMessage = error.localizedDescription
                return
            }
            self?.userSession = authResult?.user
        }
    }

    // MARK: - Email Verification Methods

    func sendEmailVerification(completion: @escaping (Bool) -> Void) {
        guard let user = userSession else {
            print("DEBUG: No user session available for email verification")
            DispatchQueue.main.async {
                self.errorMessage = "No user session found. Please sign in again."
            }
            completion(false)
            return
        }

        // Check if email is already verified
        if user.isEmailVerified {
            print("DEBUG: Email is already verified")
            DispatchQueue.main.async {
                self.successMessage = "Your email is already verified!"
                self.checkAuthenticationState()
            }
            completion(true)
            return
        }

        print("DEBUG: Sending email verification to: \(user.email ?? "unknown")")
        user.sendEmailVerification { error in
            if let error = error {
                print("DEBUG: Failed to send email verification: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    // Handle specific Firebase Auth errors
                    if error.localizedDescription.contains("too-many-requests") {
                        self.errorMessage = "Too many verification emails sent. Please wait a few minutes before requesting another."
                    } else if error.localizedDescription.contains("invalid-email") {
                        self.errorMessage = "Invalid email address. Please contact support."
                    } else {
                        self.errorMessage = "Failed to send verification email. Please try again later."
                    }
                }
                completion(false)
            } else {
                print("DEBUG: Email verification sent successfully to: \(user.email ?? "unknown")")
                DispatchQueue.main.async {
                    self.successMessage = "Verification email sent to \(user.email ?? "your email")! Please check your inbox and spam folder."
                    self.errorMessage = nil
                }
                completion(true)
            }
        }
    }

    func checkEmailVerificationStatus() {
        guard let user = userSession else {
            print("DEBUG: No user session available for email verification check")
            DispatchQueue.main.async {
                self.errorMessage = "No user session found. Please sign in again."
            }
            return
        }

        print("DEBUG: Checking email verification status for: \(user.email ?? "unknown")")

        // Reload user to get the latest email verification status from Firebase
        user.reload { [weak self] error in
            if let error = error {
                print("DEBUG: Failed to reload user: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self?.errorMessage = "Failed to check verification status. Please try again."
                }
                return
            }

            DispatchQueue.main.async {
                if user.isEmailVerified {
                    print("DEBUG: Email verified successfully for: \(user.email ?? "unknown")")
                    self?.successMessage = "Email verified successfully! Welcome to Dash!"
                    self?.errorMessage = nil
                    // Clear any previous authentication state and proceed with normal flow
                    self?.checkAuthenticationState()
                } else {
                    print("DEBUG: Email still not verified for: \(user.email ?? "unknown")")
                    self?.errorMessage = "Email not yet verified. Please check your inbox (including spam folder) and click the verification link."
                }
            }
        }
    }
    
    func signOut() {
        do {
            // Clear saved 2FA authentication state on explicit sign out
            clearTwoFactorAuthenticationState()

            // Reset authentication state for sign out before clearing user data
            resetAuthenticationStateForSignOut()

            // Clear notification service state
            NotificationService.shared.clearShownNotifications()

            try Auth.auth().signOut()
            self.userSession = nil
            self.currentUser = nil
            self.authenticationState = .unauthenticated
            self.failedLoginAttempts = 0
            self.isLockedOut = false
        } catch {
            print("DEBUG: Failed to sign out with error \(error.localizedDescription)")
        }
    }
    
    func verifyTwoFactorCode(_ code: String) {
        guard let userId = userSession?.uid else {
            self.errorMessage = "User session not found"
            return
        }

        // Debug: Test TOTP implementation
        TOTPService.shared.testTOTPImplementation(for: userId)

        switch TOTPService.shared.verify(code: code, for: userId) {
        case .success(let isValid):
            if isValid {
                self.authenticationState = .authenticated
                self.errorMessage = nil
                self.failedLoginAttempts = 0
                SecurityAuditService.shared.logSecurityEvent(.twoFactorVerified, for: userId)
                
                // Save 2FA authentication state to persist across app sessions
                saveTwoFactorAuthenticationState()
            } else {
                self.failedLoginAttempts += 1
                if self.failedLoginAttempts >= maxLoginAttempts {
                    self.handleMaxAttemptsReached()
                } else {
                    self.errorMessage = "Invalid verification code. \(maxLoginAttempts - failedLoginAttempts) attempts remaining."
                }
                SecurityAuditService.shared.logSecurityEvent(.twoFactorFailed, for: userId, metadata: [
                    "attempts": failedLoginAttempts
                ])
            }
        case .failure(let error):
            print("DEBUG: TOTP verification failed with error: \(error)")
            print("DEBUG: Error localized description: \(error.localizedDescription)")
            self.errorMessage = error.localizedDescription
            SecurityAuditService.shared.logSecurityEvent(.twoFactorFailed, for: userId, success: false)
        }
    }
    
    func handleMaxAttemptsReached() {
        self.isLockedOut = true
        self.errorMessage = "Too many failed attempts. You have been logged out for security."

        // Log security event
        if let userId = userSession?.uid {
            SecurityAuditService.shared.logSecurityEvent(.accountLocked, for: userId, metadata: [
                "reason": "max_2fa_attempts"
            ])
        }

        // Sign out user
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.signOut()
        }
    }

    // MARK: - Debug/Reset Functions

    func resetTwoFactorForDebugging() {
        guard let userId = userSession?.uid else {
            print("DEBUG: No user session found for 2FA reset")
            return
        }

        print("DEBUG: Starting 2FA reset for user: \(userId)")

        // Clear TOTP data from keychain
        let deleteResult = TOTPService.shared.deleteTOTPData(for: userId)
        switch deleteResult {
        case .success:
            print("DEBUG: Successfully deleted TOTP data from keychain")
        case .failure(let error):
            print("DEBUG: Failed to delete TOTP data: \(error)")
        }

        // Update Firestore to disable 2FA
        let db = Firestore.firestore()
        db.collection("users").document(userId).updateData([
            "twoFactorEnabled": false,
            "twoFactorSetupDate": FieldValue.delete(),
            "requireTwoFactorForTransactions": false
        ]) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    print("DEBUG: Failed to reset 2FA in Firestore: \(error)")
                    self?.errorMessage = "Failed to reset 2FA: \(error.localizedDescription)"
                } else {
                    print("DEBUG: Successfully reset 2FA in Firestore")

                    // Update local state
                    self?.authenticationState = .authenticated
                    self?.errorMessage = nil
                    self?.failedLoginAttempts = 0
                    self?.isLockedOut = false

                    // Update local user state
                    self?.updateLocalTwoFactorState(enabled: false, setupDate: nil)

                    // Refresh user data
                    self?.refreshCurrentUser()

                    print("DEBUG: 2FA reset complete - you can now access the app")
                }
            }
        }
    }
    
    func checkAuthenticationState() {
        guard let user = currentUser else {
            print("DEBUG: No current user, setting to unauthenticated")
            self.authenticationState = .unauthenticated
            return
        }

        // First check if email is verified
        guard let firebaseUser = userSession, firebaseUser.isEmailVerified else {
            print("DEBUG: Email not verified, setting to pendingEmailVerification")
            self.authenticationState = .pendingEmailVerification
            return
        }

        print("DEBUG: Checking authentication state for user: \(user.email ?? "unknown")")
        print("DEBUG: User 2FA enabled: \(user.twoFactorEnabled)")
        print("DEBUG: Current auth state: \(authenticationState)")

        // If user has 2FA enabled, check if we have a saved authentication state
        if user.twoFactorEnabled == true {
            // Try to restore saved 2FA authentication state
            restoreTwoFactorAuthenticationState()

            // If still not authenticated after restore attempt, require 2FA
            if authenticationState != .authenticated {
                print("DEBUG: 2FA verification required - setting to pendingTwoFactor")
                self.authenticationState = .pendingTwoFactor
            }
        } else {
            print("DEBUG: No 2FA required - setting to authenticated")
            self.authenticationState = .authenticated
        }
    }
    
    func resetAuthenticationState() {
        // Only reset to pending 2FA on explicit sign out, not on app backgrounding
        // This method should only be called when the user explicitly signs out
        // or when there's a security issue that requires re-authentication
        if currentUser?.twoFactorEnabled == true {
            self.authenticationState = .pendingTwoFactor
            print("DEBUG: Reset to pending 2FA state (explicit reset)")
        } else {
            self.authenticationState = .authenticated
            print("DEBUG: Reset to authenticated state (no 2FA)")
        }
        self.failedLoginAttempts = 0
        self.isLockedOut = false
    }
    func resetAuthenticationStateForSignOut() {
        // This method is specifically for sign out scenarios
        // Always reset to pending 2FA if user has it enabled
        if currentUser?.twoFactorEnabled == true {
            self.authenticationState = .pendingTwoFactor
            print("DEBUG: Reset to pending 2FA state for sign out")
        } else {
            self.authenticationState = .authenticated
            print("DEBUG: Reset to authenticated state for sign out (no 2FA)")
        }
        self.failedLoginAttempts = 0
        self.isLockedOut = false
    }

    // MARK: - Error State Management
    func clearErrorState() {
        self.errorMessage = nil
        self.successMessage = nil
    }
    
    func updateLocalTwoFactorState(enabled: Bool, setupDate: Date? = nil) {
        // Update local user state only - Firebase updates should be handled elsewhere
        currentUser?.twoFactorEnabled = enabled
        if let setupDate = setupDate {
            currentUser?.twoFactorSetupDate = setupDate
        }
        
        // Update authentication state based on new 2FA status
        checkAuthenticationState()
    }
    
    func updatePhoneNumber(_ phoneNumber: String) {
        guard let uid = userSession?.uid else { return }
        
        // Check if phone number is already in use by another user
        checkPhoneNumberAvailability(phoneNumber) { [weak self] isAvailable in
            if !isAvailable {
                self?.errorMessage = "This phone number is already registered with another account."
                return
            }
            
            // Phone number is available, proceed with update
            let db = Firestore.firestore()
            db.collection("users").document(uid).updateData(["phoneNumber": phoneNumber]) { [weak self] error in
                if let error = error {
                    self?.errorMessage = error.localizedDescription
                } else {
                    self?.successMessage = "Phone number has been updated."
                    // Update the current user object
                    self?.currentUser?.phoneNumber = phoneNumber
                }
            }
        }
    }
    
    func isPhoneNumberRequired() -> Bool {
        return currentUser?.phoneNumber?.isEmpty ?? true
    }
    
    func resetPassword(withEmail email: String) {
        self.errorMessage = nil
        self.successMessage = nil
        
        Auth.auth().sendPasswordReset(withEmail: email) { [weak self] error in
            if let error = error {
                self?.errorMessage = error.localizedDescription
            } else {
                self?.successMessage = "A password reset link has been sent to your email."
            }
        }
    }
    
    func fetchUser(withUid uid: String) {
        print("DEBUG: Starting fetchUser for uid: \(uid)")
        
        Firestore.firestore().collection("users").document(uid).addSnapshotListener { [weak self] snapshot, error in
            guard let self = self else { return }

            if let error = error {
                print("DEBUG: Failed to fetch user with error: \(error.localizedDescription)")
                print("DEBUG: Error code: \((error as NSError).code)")
                print("DEBUG: Error domain: \((error as NSError).domain)")

                // Only set error message if we're not currently in a loading state (sign up/sign in process)
                if !self.isLoading {
                    self.errorMessage = "Failed to fetch user data: \(error.localizedDescription)"
                }
                return
            }

            guard let snapshot = snapshot else {
                print("DEBUG: Snapshot is nil for uid: \(uid)")

                // Only set error message if we're not currently in a loading state
                if !self.isLoading {
                    self.errorMessage = "Failed to retrieve user data"
                }
                return
            }
            
            guard snapshot.exists else {
                print("DEBUG: User document does not exist for uid: \(uid)")
                print("DEBUG: Creating new user document...")
                self.createNewUserDocument(withUid: uid)
                return
            }
            
            print("DEBUG: User document exists, attempting to decode...")
            print("DEBUG: Document data: \(snapshot.data() ?? [:])")
            
            do {
                let user = try snapshot.data(as: User.self)
                print("DEBUG: Successfully decoded user: \(user.email ?? "no email")")
                self.currentUser = user
                self.errorMessage = nil // Clear any previous errors
                
                // Check authentication state after user is loaded
                self.checkAuthenticationState()
            } catch {
                print("DEBUG: Failed to decode user: \(error.localizedDescription)")
                print("DEBUG: Decoding error type: \(type(of: error))")
                
                // Try to decode manually to see what's missing
                if let data = snapshot.data() {
                    print("DEBUG: Available fields in document:")
                    for (key, value) in data {
                        print("DEBUG: - \(key): \(value) (\(type(of: value)))")
                    }
                }
                
                self.errorMessage = "Failed to decode user data: \(error.localizedDescription)"
            }
        }
    }

    func refreshCurrentUser() {
        guard let uid = userSession?.uid else { return }
        
        print("DEBUG: Refreshing current user data for uid: \(uid)")
        
        Firestore.firestore().collection("users").document(uid).getDocument { [weak self] snapshot, error in
            guard let self = self else { return }
            
            if let error = error {
                print("DEBUG: Failed to refresh user with error: \(error.localizedDescription)")
                return
            }
            
            guard let snapshot = snapshot, snapshot.exists else {
                print("DEBUG: User document does not exist during refresh for uid: \(uid)")
                return
            }
            
            do {
                let user = try snapshot.data(as: User.self)
                print("DEBUG: Successfully refreshed user: \(user.email ?? "no email"), balance: \(user.balance ?? 0), 2FA enabled: \(user.twoFactorEnabled)")
                self.currentUser = user

                // Check authentication state after refresh to handle any 2FA status changes
                self.checkAuthenticationState()
            } catch {
                print("DEBUG: Failed to decode user during refresh: \(error.localizedDescription)")
            }
        }
    }

    private func createNewUserDocument(withUid uid: String) {
        guard let email = Auth.auth().currentUser?.email else {
            self.errorMessage = "Could not retrieve user email."
            self.isLoading = false
            return
        }
        
        // Create user data with all required fields for Firestore rules
        var userData: [String: Any] = [
            "uid": uid,
            "email": email,
            "displayName": Auth.auth().currentUser?.displayName ?? "New User",
            "balance": 0.0,
            "twoFactorEnabled": false,
            "securityLevel": "basic",
            "failedLoginAttempts": 0,
            "biometricEnabled": false,
            "sessionTimeout": 900,
            "requireTwoFactorForTransactions": false,
            "backupCodesUsed": [],
            "securityNotificationsEnabled": true,
            "trustedDevices": [],
            "subscription": [
                "tier": "classic",
                "status": "active",
                "startDate": Timestamp(date: Date()),
                "endDate": Timestamp(date: Calendar.current.date(byAdding: .day, value: 30, to: Date()) ?? Date()),
                "autoRenew": true,
                "priceAtPurchase": 0.0
            ]
        ]
        
        // Add phone number if available
        if let phoneNumber = Auth.auth().currentUser?.phoneNumber, !phoneNumber.isEmpty {
            userData["phoneNumber"] = phoneNumber
        }
        
        Firestore.firestore().collection("users").document(uid).setData(userData) { [weak self] error in
            if let error = error {
                print("DEBUG: Failed to create new user document: \(error.localizedDescription)")
                self?.errorMessage = "Failed to initialize user data: \(error.localizedDescription)"
                self?.isLoading = false
            } else {
                print("DEBUG: Successfully created user document for \(email)")
                self?.isLoading = false
                // The fetchUser will be called automatically by the sink
            }
        }
    }
    
    // MARK: - 2FA Authentication State Persistence
    
    private func saveTwoFactorAuthenticationState() {
        guard let key = twoFactorAuthKey else { return }
        let data = "authenticated".data(using: .utf8)!
        _ = KeychainService.shared.save(key: key, data: data)
        print("DEBUG: Saved 2FA authentication state for user")
    }
    
    private func restoreTwoFactorAuthenticationState() {
        guard let key = twoFactorAuthKey,
              let data = KeychainService.shared.load(key: key),
              let value = String(data: data, encoding: .utf8),
              value == "authenticated" else {
            print("DEBUG: No saved 2FA authentication state found")
            return
        }
        
        // Only restore if user has 2FA enabled and current state is not already authenticated
        if currentUser?.twoFactorEnabled == true && authenticationState != .authenticated {
            print("DEBUG: Restoring 2FA authentication state - setting to authenticated")
            self.authenticationState = .authenticated
        }
    }
    
    private func clearTwoFactorAuthenticationState() {
        guard let key = twoFactorAuthKey else { return }
        _ = KeychainService.shared.delete(key: key)
        print("DEBUG: Cleared 2FA authentication state for user")
    }
}
