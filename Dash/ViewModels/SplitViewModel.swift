import Foundation
import FirebaseFirestore
import Combine
import FirebaseAuth

class SplitViewModel: ObservableObject {
    static let shared = SplitViewModel()

    @Published var split: Split?
    @Published var errorMessage: String?
    @Published var participantsForEditing: [Participant] = []
    @Published var isSplitFinalized = false
    @Published var hasJoinedSplit = false

    private var db = Firestore.firestore()
    private var listener: ListenerRegistration?
    private var cancellables = Set<AnyCancellable>()

    init() {
        $participantsForEditing
            .debounce(for: .seconds(1), scheduler: RunLoop.main)
            .sink { [weak self] participants in
                guard let self = self, let splitId = self.split?.id else { return }
                if !participants.isEmpty {
                    self.updateSplit(splitId: splitId, participants: participants)
                }
            }
            .store(in: &cancellables)
    }

    func stopListening() {
        print("Stopping listener.")
        listener?.remove()
        listener = nil
    }

    func listenToSplit(splitId: String) {
        stopListening() // Ensure we are not listening to an old split

        guard !splitId.isEmpty else {
            print("Error: Split ID is empty.")
            self.errorMessage = "Error: Split ID is empty."
            return
        }
        print("Attempting to listen to split with ID: \(splitId)")

        self.listener = db.collection("splits").document(splitId).addSnapshotListener { [weak self] (documentSnapshot, error) in
            DispatchQueue.main.async {
                guard let self = self else {
                    print("ViewModel is no longer available.")
                    return
                }

                if let error = error {
                    print("Firestore listener error: \(error.localizedDescription)")
                    self.errorMessage = "Listener error: \(error.localizedDescription)"
                    return
                }

                guard let document = documentSnapshot else {
                    print("Document snapshot was nil.")
                    self.errorMessage = "Document snapshot was nil."
                    return
                }
                
                guard document.exists else {
                    print("Document does not exist at path: splits/\(splitId)")
                    self.errorMessage = "Split data not found."
                    self.split = nil // Clear split data if not found
                    return
                }

                print("Received data update. Attempting to decode...")
                do {
                    var decodedSplit = try document.data(as: Split.self)
                    decodedSplit.id = document.documentID // Ensure the ID is set
                    self.split = decodedSplit
                    print("Successfully decoded split. ID: \(decodedSplit.id ?? "N/A"), Participants: \(decodedSplit.participants.count)")
                    print("Participant names: \(decodedSplit.participants.map { $0.name }.joined(separator: ", "))")
                } catch {
                    print("Failed to decode split: \(error)")
                    self.errorMessage = "Failed to decode split: \(error.localizedDescription)"
                }
            }
        }
    }

    func createSplitInFirestore(totalAmount: Decimal, numberOfParticipants: Int, participants: [Participant], completion: @escaping (Result<Split, Error>) -> Void) {
        guard let creatorId = Auth.auth().currentUser?.uid else {
            completion(.failure(NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "User not authenticated."])))
            return
        }
        
        let targetTotal = Double(truncating: totalAmount as NSNumber)
        
        // No validation needed - people will join and set amounts later
        
        let newSplit = Split(
            id: nil, // Firestore will generate this
            creatorId: creatorId,
            totalAmount: targetTotal,
            currency: "AUD",
            numberOfParticipants: numberOfParticipants,
            participants: participants,
            paidParticipants: [:],
            createdAt: Date(),
            status: .pending
        )
        
        do {
            let documentRef = try db.collection("splits").addDocument(from: newSplit)
            
            documentRef.getDocument { [weak self] (document, error) in
                DispatchQueue.main.async {
                    if let document = document, document.exists {
                        do {
                            var createdSplit = try document.data(as: Split.self)
                            createdSplit.id = document.documentID // Manually assign the ID
                            print("🎯 SPLIT CREATED - Split ID: \(document.documentID)")
                            print("SplitViewModel: Created split with ID: \(document.documentID)")
                            
                            // Update the split property on main thread
                            self?.split = createdSplit
                            
                            // Create a pending transaction for the creator
                            self?.createPendingTransactionForCreator(splitId: document.documentID, split: createdSplit)
                            
                    // Removed notification for split creation
                            
                            completion(.success(createdSplit))
                        } catch {
                            print("SplitViewModel: Failed to decode created split: \(error)")
                            completion(.failure(error))
                        }
                    } else if let error = error {
                        print("SplitViewModel: Error fetching created document: \(error)")
                        completion(.failure(error))
                    } else {
                        print("SplitViewModel: Document does not exist after creation")
                        completion(.failure(NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "Document does not exist after creation."])))
                    }
                }
            }
        } catch let error {
            completion(.failure(error))
        }
    }
    
    private func createPendingTransactionForCreator(splitId: String, split: Split) {
        guard let creatorId = Auth.auth().currentUser?.uid,
              let creatorParticipant = split.participants.first(where: { $0.id == creatorId }) else {
            print("SplitViewModel: Could not find creator participant")
            return
        }
        
        let userRef = db.collection("users").document(creatorId)
        let transactionRef = userRef.collection("transactions").document()
        
        let transactionData: [String: Any] = [
            "name": "Split Created",
            "detail": "Split created - waiting for participants",
            "amount": creatorParticipant.share,
            "type": "expense",
            "category": "Split",
            "date": Timestamp(date: Date()),
            "status": "pending",
            "splitId": splitId
        ]
        
        transactionRef.setData(transactionData) { error in
            if let error = error {
                print("SplitViewModel: Error creating pending transaction for creator: \(error.localizedDescription)")
            } else {
                print("SplitViewModel: Created pending transaction for creator")
            }
        }
    }

func updateSplit(splitId: String, participants: [Participant]) {
        let splitRef = db.collection("splits").document(splitId)
        do {
            let participantsData = try participants.map { try Firestore.Encoder().encode($0) }
            splitRef.updateData([
                "participants": participantsData,
                "numberOfParticipants": participants.count
            ]) { [weak self] err in
                if let err = err {
                    self?.errorMessage = "Error updating split: \(err.localizedDescription)"
                }
            }
        } catch {
            self.errorMessage = "Error encoding participants: \(error.localizedDescription)"
        }
    }

    func joinSplit(splitId: String, amount: Double, completion: @escaping (Result<Split, Error>) -> Void) {
        guard let userId = Auth.auth().currentUser?.uid else {
            let error = NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "User not authenticated."])
            completion(.failure(error))
            return
        }

        print("SplitViewModel: Starting joinSplit for user \(userId), split \(splitId), amount \(amount)")
        
        let userRef = db.collection("users").document(userId)
        let splitRef = db.collection("splits").document(splitId)

        db.runTransaction({ (transaction, errorPointer) -> Any? in
            let userDocument: DocumentSnapshot
            let splitDocument: DocumentSnapshot
            do {
                userDocument = try transaction.getDocument(userRef)
                splitDocument = try transaction.getDocument(splitRef)
            } catch let fetchError as NSError {
                errorPointer?.pointee = fetchError
                return nil
            }

            guard let displayName = userDocument.data()?["displayName"] as? String, !displayName.isEmpty else {
                let error = NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "Display name not set."])
                errorPointer?.pointee = error
                return nil
            }

            guard var split = try? splitDocument.data(as: Split.self) else {
                let error = NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to decode split data."])
                errorPointer?.pointee = error
                return nil
            }

            // Check if split should be cancelled (created but not finalized, and someone other than creator is joining)
            if split.status == .pending && split.creatorId != userId && split.participants.count == 1 {
                // This is an unfinalised split that someone is trying to join - cancel it
                print("SplitViewModel: Cancelling unfinalised split \(splitId) as user \(userId) is trying to join")
                transaction.updateData(["status": "cancelled"], forDocument: splitRef)
                let error = NSError(domain: "AppErrorDomain", code: -2, userInfo: [NSLocalizedDescriptionKey: "This split was cancelled because it wasn't finalized before others tried to join."])
                errorPointer?.pointee = error
                return nil
            }

            print("SplitViewModel: Transaction - Current split has \(split.participants.count) participants")
            print("SplitViewModel: Transaction - Current participants: \(split.participants.map { $0.name }.joined(separator: ", "))")

            // Check if user is already a participant
            if let existingParticipantIndex = split.participants.firstIndex(where: { $0.id == userId }) {
                print("SplitViewModel: Transaction - User is existing participant, updating amount")
                // Update existing participant's amount
                split.participants[existingParticipantIndex].share = amount
            } else {
                print("SplitViewModel: Transaction - Adding new participant: \(displayName)")
                // Add new participant (not paid yet)
                let newParticipant = Participant(id: userId, name: displayName, share: amount, hasPaid: false)
                split.participants.append(newParticipant)
            }
            
            print("SplitViewModel: Transaction - After update, split has \(split.participants.count) participants")
            
            // Update the participants array in Firestore
            do {
                let participantsData = try split.participants.map { try Firestore.Encoder().encode($0) }
                transaction.updateData([
                    "participants": participantsData,
                    "numberOfParticipants": split.participants.count
                ], forDocument: splitRef)
                print("SplitViewModel: Transaction - Successfully updated Firestore with \(split.participants.count) participants")
            } catch {
                print("SplitViewModel: Transaction - Failed to encode participants: \(error)")
                errorPointer?.pointee = error as NSError
                return nil
            }

            return split // Return the locally updated split object
        }) { (result, error) in
            print("SplitViewModel: Transaction completed")
            if let error = error {
                print("SplitViewModel: Transaction failed with error: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            if let updatedSplit = result as? Split {
                print("SplitViewModel: Transaction succeeded, updated split has \(updatedSplit.participants.count) participants")
                DispatchQueue.main.async {
                    self.split = updatedSplit
                    self.hasJoinedSplit = true
                    
                    // Removed notification for split joined
                    
                    completion(.success(updatedSplit))
                }
            } else {
                print("SplitViewModel: Transaction returned nil, fetching latest split data")
                self.fetchSplit(splitId: splitId) { result in
                    switch result {
                    case .success(let fetchedSplit):
                        print("SplitViewModel: Fetched split has \(fetchedSplit.participants.count) participants")
                        self.split = fetchedSplit
                        self.hasJoinedSplit = true
                        completion(.success(fetchedSplit))
                    case .failure(let fetchError):
                        print("SplitViewModel: Failed to fetch split: \(fetchError.localizedDescription)")
                        completion(.failure(fetchError))
                    }
                }
            }
        }
    }

    func joinAndPayForSplit(splitId: String, amount: Double, completion: @escaping (Result<Split, Error>) -> Void) {
        guard let userId = Auth.auth().currentUser?.uid else {
            let error = NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "User not authenticated."])
            completion(.failure(error))
            return
        }

        print("SplitViewModel: Starting joinAndPayForSplit for user \(userId), split \(splitId), amount \(amount)")
        
        let userRef = db.collection("users").document(userId)
        let splitRef = db.collection("splits").document(splitId)
        let transactionRef = userRef.collection("transactions").document()

        db.runTransaction({ (transaction, errorPointer) -> Any? in
            let userDocument: DocumentSnapshot
            let splitDocument: DocumentSnapshot
            do {
                userDocument = try transaction.getDocument(userRef)
                splitDocument = try transaction.getDocument(splitRef)
            } catch let fetchError as NSError {
                errorPointer?.pointee = fetchError
                return nil
            }

            guard let displayName = userDocument.data()?["displayName"] as? String, !displayName.isEmpty else {
                let error = NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "Display name not set."])
                errorPointer?.pointee = error
                return nil
            }

            let currentBalance = userDocument.data()?["balance"] as? Double ?? 0.0
            if currentBalance < amount {
                let error = NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "Insufficient funds."])
                errorPointer?.pointee = error
                return nil
            }

            guard var split = try? splitDocument.data(as: Split.self) else {
                let error = NSError(domain: "AppErrorDomain", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to decode split data."])
                errorPointer?.pointee = error
                return nil
            }

            print("SplitViewModel: Transaction - Current split has \(split.participants.count) participants")
            print("SplitViewModel: Transaction - Current participants: \(split.participants.map { $0.name }.joined(separator: ", "))")

            // Check if user is already a participant (for creator payment)
            if let existingParticipantIndex = split.participants.firstIndex(where: { $0.id == userId }) {
                print("SplitViewModel: Transaction - User is existing participant, marking as paid")
                // Update existing participant to mark as paid
                split.participants[existingParticipantIndex].hasPaid = true
            } else {
                print("SplitViewModel: Transaction - Adding new participant: \(displayName)")
                // Add new participant
                let newParticipant = Participant(id: userId, name: displayName, share: amount, hasPaid: true)
                split.participants.append(newParticipant)
            }
            
            print("SplitViewModel: Transaction - After update, split has \(split.participants.count) participants")
            
            // Always update the entire participants array to ensure consistency
            do {
                let participantsData = try split.participants.map { try Firestore.Encoder().encode($0) }
                transaction.updateData([
                    "participants": participantsData
                ], forDocument: splitRef)
                print("SplitViewModel: Transaction - Successfully updated Firestore with \(split.participants.count) participants")
            } catch {
                print("SplitViewModel: Transaction - Failed to encode participants: \(error)")
                errorPointer?.pointee = error as NSError
                return nil
            }

            // Immediately charge the user and create a completed transaction
            let newBalance = currentBalance - amount
            transaction.updateData(["balance": newBalance], forDocument: userRef)
            
            let transactionData: [String: Any] = [
                "name": "Split Payment",
                "detail": "Split payment with \(split.participants.count) participants",
                "amount": amount,
                "type": "expense",
                "category": "Split",
                "date": Timestamp(date: Date()),
                "status": "completed",
                "splitId": splitId
            ]
            
            print("SplitViewModel: Transaction - Creating COMPLETED transaction with amount: \(amount) for user: \(userId)")
            print("SplitViewModel: Transaction - User balance updated from \(currentBalance) to \(newBalance)")
            
            transaction.setData(transactionData, forDocument: transactionRef)
            print("SplitViewModel: Transaction - Successfully created completed transaction document: \(transactionRef.documentID)")

            return split // Return the locally updated split object
        }) { (result, error) in
            print("SplitViewModel: Transaction completed")
            if let error = error {
                print("SplitViewModel: Transaction failed with error: \(error.localizedDescription)")
                completion(.failure(error))
                return
            }
            
            if let updatedSplit = result as? Split {
                print("SplitViewModel: Transaction succeeded, updated split has \(updatedSplit.participants.count) participants")
                DispatchQueue.main.async {
                    self.split = updatedSplit
                    self.hasJoinedSplit = true
                    
                    // Removed notification for split joined
                    
                    print("SplitViewModel: Setting up listener for split updates")
                    self.listenToSplit(splitId: splitId)
                    completion(.success(updatedSplit))
                }
            } else {
                print("SplitViewModel: Transaction returned nil, fetching latest split data")
                // This case handles when the transaction returns nil, e.g., user already a participant.
                // We fetch the latest split data to ensure consistency.
                self.fetchSplit(splitId: splitId) { result in
                    switch result {
                    case .success(let fetchedSplit):
                        print("SplitViewModel: Fetched split has \(fetchedSplit.participants.count) participants")
                        self.split = fetchedSplit
                        self.hasJoinedSplit = true
                        completion(.success(fetchedSplit))
                    case .failure(let fetchError):
                        print("SplitViewModel: Failed to fetch split: \(fetchError.localizedDescription)")
                        completion(.failure(fetchError))
                    }
                }
            }
        }
    }

    func finalizeSplit(splitId: String, participants: [Participant]) {
        let splitRef = db.collection("splits").document(splitId)

        splitRef.updateData([
            "status": "complete"
        ]) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "Failed to finalize split: \(error.localizedDescription)"
                } else {
                    self?.isSplitFinalized = true
                    print("Split finalized successfully")
                    // Now finalize all pending transactions for this split
                    self?.finalizeSplitTransactions(splitId: splitId)
                }
            }
        }
    }

    func cancelSplit(splitId: String, reason: String = "Split cancelled") {
        let splitRef = db.collection("splits").document(splitId)

        splitRef.updateData([
            "status": "cancelled"
        ]) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "Failed to cancel split: \(error.localizedDescription)"
                    print("SplitViewModel: Error cancelling split: \(error.localizedDescription)")
                } else {
                    print("SplitViewModel: Split \(splitId) cancelled successfully")
                    // Cancel all pending transactions for this split
                    self?.cancelSplitTransactions(splitId: splitId, reason: reason)

                    // Clear local split state if this is the current split
                    if self?.split?.id == splitId {
                        self?.split = nil
                    }

                    // Force refresh transactions to immediately reflect the cancellation
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        // Note: TransactionService will automatically refresh via its listener
                        print("SplitViewModel: Split cancellation completed, transactions should refresh automatically")
                    }
                }
            }
        }
    }
    
    private func finalizeSplitTransactions(splitId: String) {
        print("SplitViewModel: Finalizing transactions for split: \(splitId)")
        
        guard let split = self.split else {
            print("SplitViewModel: No split data available for finalization")
            return
        }
        
        print("SplitViewModel: Processing transactions for \(split.participants.count) participants")
        
        // Use a batch write to ensure all transactions are updated atomically
        let batch = db.batch()
        var transactionsToUpdate: [(userId: String, transactionId: String, amount: Double)] = []
        let dispatchGroup = DispatchGroup()
        
        for participant in split.participants {
            let participantId = participant.id
            
            if participantId.isEmpty {
                print("SplitViewModel: Participant has empty ID, skipping")
                continue
            }
            
            print("SplitViewModel: Checking transactions for participant: \(participant.name) (\(participantId))")
            
            dispatchGroup.enter()
            
            // Query each user's transactions directly
            db.collection("users").document(participantId).collection("transactions")
                .whereField("splitId", isEqualTo: splitId)
                .whereField("status", isEqualTo: "pending")
                .getDocuments { [weak self] (querySnapshot, error) in
                    defer { dispatchGroup.leave() }
                    
                    guard let self = self else { return }
                    
                    if let error = error {
                        print("SplitViewModel: Error fetching transactions for user \(participantId): \(error.localizedDescription)")
                        return
                    }
                    
                    guard let documents = querySnapshot?.documents else {
                        print("SplitViewModel: No pending transactions found for user \(participantId)")
                        return
                    }
                    
                    print("SplitViewModel: Found \(documents.count) pending transactions for user \(participantId)")
                    
                    // Collect transactions to update
                    for document in documents {
                        let transactionData = document.data()
                        let amount = transactionData["amount"] as? Double ?? 0.0
                        
                        print("SplitViewModel: Queuing transaction \(document.documentID) for user \(participantId), amount: \(amount)")
                        transactionsToUpdate.append((userId: participantId, transactionId: document.documentID, amount: amount))
                    }
                }
        }
        
        // Wait for all queries to complete, then process the transactions
        dispatchGroup.notify(queue: .main) {
            print("SplitViewModel: All queries completed. Processing \(transactionsToUpdate.count) transactions")
            
            if transactionsToUpdate.isEmpty {
                print("SplitViewModel: No pending transactions to finalize")
                return
            }
            
            // Process each transaction individually to ensure proper balance updates
            for transactionInfo in transactionsToUpdate {
                self.finalizeUserTransaction(
                    userId: transactionInfo.userId,
                    transactionId: transactionInfo.transactionId,
                    amount: transactionInfo.amount
                )
            }
        }
    }
    
    private func finalizeUserTransaction(userId: String, transactionId: String, amount: Double) {
        let userRef = db.collection("users").document(userId)
        let transactionRef = userRef.collection("transactions").document(transactionId)
        
        db.runTransaction({ (transaction, errorPointer) -> Any? in
            let userDocument: DocumentSnapshot
            do {
                userDocument = try transaction.getDocument(userRef)
            } catch let fetchError as NSError {
                errorPointer?.pointee = fetchError
                return nil
            }
            
            let currentBalance = userDocument.data()?["balance"] as? Double ?? 0.0
            let newBalance = currentBalance - amount
            
            // Update user balance
            transaction.updateData(["balance": newBalance], forDocument: userRef)
            
            // Update transaction status to completed
            transaction.updateData(["status": "completed"], forDocument: transactionRef)
            
            print("SplitViewModel: Finalized transaction for user \(userId), deducted \(amount), new balance: \(newBalance)")
            
            return nil
        }) { (result, error) in
            if let error = error {
                print("SplitViewModel: Error finalizing transaction for user \(userId): \(error.localizedDescription)")
            } else {
                print("SplitViewModel: Successfully finalized transaction for user \(userId)")
            }
        }
    }

    func fetchSplit(splitId: String, completion: @escaping (Result<Split, Error>) -> Void) {
        db.collection("splits").document(splitId).getDocument { (document, error) in
            if let error = error {
                completion(.failure(error))
                return
            }

            if let document = document, document.exists {
                do {
                    var decodedSplit = try document.data(as: Split.self)
                    decodedSplit.id = document.documentID // Ensure the ID is set
                    DispatchQueue.main.async {
                        self.split = decodedSplit
                    }
                    completion(.success(decodedSplit))
                } catch {
                    completion(.failure(error))
                }
            } else {
                completion(.failure(NSError(domain: "AppErrorDomain", code: -404, userInfo: [NSLocalizedDescriptionKey: "Split not found."])))
            }
        }
    }

    func loadSplitForReopening(splitId: String) {
        fetchSplit(splitId: splitId) { result in
            switch result {
            case .success(let split):
                print("SplitViewModel: Successfully loaded split for reopening: \(split.id ?? "unknown")")
            case .failure(let error):
                print("SplitViewModel: Failed to load split for reopening: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.errorMessage = "Failed to load split: \(error.localizedDescription)"
                }
            }
        }
    }

    func reset() {
        stopListening()
        split = nil
        errorMessage = nil
        isSplitFinalized = false
        hasJoinedSplit = false
    }

    func cancelCurrentSplitIfAbandoned() {
        guard let currentSplit = split,
              let splitId = currentSplit.id,
              currentSplit.status == .pending,
              !isSplitFinalized else {
            print("SplitViewModel: No split to cancel or split already finalized")
            return
        }

        // Cancel any pending split that hasn't been finalized when user abandons creation
        // This includes splits with only the creator or splits that were never properly set up
        print("SplitViewModel: Cancelling abandoned split: \(splitId)")
        cancelSplit(splitId: splitId, reason: "Split abandoned by creator")
    }

    deinit {
        listener?.remove()
    }

    private func cancelSplitTransactions(splitId: String, reason: String) {
        print("SplitViewModel: Cancelling transactions for split: \(splitId)")

        // Get the current user ID to cancel their transaction immediately
        guard let currentUserId = Auth.auth().currentUser?.uid else {
            print("SplitViewModel: No current user for transaction cancellation")
            return
        }

        // Cancel the current user's transaction immediately
        let userRef = db.collection("users").document(currentUserId)
        userRef.collection("transactions")
            .whereField("splitId", isEqualTo: splitId)
            .getDocuments { [weak self] (querySnapshot, error) in
                if let error = error {
                    print("SplitViewModel: Error fetching transactions for current user: \(error.localizedDescription)")
                    return
                }

                guard let documents = querySnapshot?.documents else {
                    print("SplitViewModel: No transactions found for current user with split \(splitId)")
                    return
                }

                // Cancel each transaction for this split
                for document in documents {
                    document.reference.updateData([
                        "status": "cancelled",
                        "detail": reason
                    ]) { error in
                        if let error = error {
                            print("SplitViewModel: Error cancelling transaction \(document.documentID): \(error.localizedDescription)")
                        } else {
                            print("SplitViewModel: Cancelled transaction \(document.documentID) for current user")
                        }
                    }
                }
            }

        // Also cancel transactions for other participants if we have split data
        if let split = self.split {
            let dispatchGroup = DispatchGroup()

            for participant in split.participants where participant.id != currentUserId {
                dispatchGroup.enter()

                let participantRef = db.collection("users").document(participant.id)
                participantRef.collection("transactions")
                    .whereField("splitId", isEqualTo: splitId)
                    .getDocuments { [weak self] (querySnapshot, error) in
                        defer { dispatchGroup.leave() }

                        if let error = error {
                            print("SplitViewModel: Error fetching transactions for user \(participant.id): \(error.localizedDescription)")
                            return
                        }

                        guard let documents = querySnapshot?.documents else {
                            print("SplitViewModel: No transactions found for user \(participant.id)")
                            return
                        }

                        // Cancel each transaction
                        for document in documents {
                            document.reference.updateData([
                                "status": "cancelled",
                                "detail": reason
                            ]) { error in
                                if let error = error {
                                    print("SplitViewModel: Error cancelling transaction \(document.documentID): \(error.localizedDescription)")
                                } else {
                                    print("SplitViewModel: Cancelled transaction \(document.documentID) for user \(participant.id)")
                                }
                            }
                        }
                    }
            }

            dispatchGroup.notify(queue: .main) {
                print("SplitViewModel: Finished cancelling all transactions for split \(splitId)")
            }
        }
    }
}
