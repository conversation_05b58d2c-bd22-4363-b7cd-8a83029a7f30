<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Learn about Dash - the team behind the modern expense splitting app" />
  <title>About Us - Dash</title>
  <link rel="stylesheet" href="CSS/style.css" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script defer src="JS/script.js"></script>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar" id="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <img src="images/appicon.png" alt="Dash" class="nav-logo">
        <span class="nav-title">Dash <span style="margin: 0 12px;">|</span> The New Way to Pay</span>
      </div>
      
      <div class="nav-menu" id="navMenu">
        <a href="home.html" class="nav-link">Home</a>
        <a href="aboutus.html" class="nav-link active">About</a>
        <button class="primary-button small" id="downloadBtn">
          <span>Download for iOS</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
        </button>
      </div>

      <div class="hamburger" id="hamburger">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="about-hero">
    <div class="container">
      <div class="about-hero-content">
        <h1>Building the future of group payments</h1>
        <p class="hero-subtitle">We're on a mission to make splitting expenses as simple as sending a message</p>
      </div>
    </div>
  </section>

  <!-- Story Section -->
  <section class="story-section">
    <div class="container">
      <div class="story-content">
        <div class="story-text">
          <h2>The Origin of Dash</h2>
          <p>Dash was born out of a real frustration: trying to split a dinner bill with friends, only to be told the restaurant doesn't allow it. That moment revealed a bigger issue—everyday group payments are harder than they should be.</p>
          <p>From delays to bank holds, the process is outdated and inconvenient. Dash exists to change that. We're building a better way for people to pay together—instantly, fairly, and without friction.</p>
          <div class="story-stats">
            <div class="stat-item">
              <span class="stat-number">2024</span>
              <span class="stat-label">Founded</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">10K+</span>
              <span class="stat-label">Users</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">$2M+</span>
              <span class="stat-label">Split</span>
            </div>
          </div>
        </div>
        <div class="story-visual">
          <div class="story-image">
            <img src="images/DashBanner.png" alt="Dash Story" />
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Team Section -->
  <section class="team-section">
    <div class="container">
      <div class="section-header">
        <h2>Meet the team</h2>
        <p>The passionate individuals building the future of payments</p>
      </div>
      
      <div class="team-grid">
        <div class="team-card">
          <div class="team-image">
            <img src="images/rickyabtus.png" alt="Ricky Schwartz" class="team-photo">
          </div>
          <div class="team-info">
            <h3>Ricky Schwartz</h3>
            <p class="team-role">Chief Executive Officer</p>
            <p class="team-bio">Young entrepreneur studying Business and IT, with a passion for technology and Apple products. His interest in finance made the transition into fintech natural. Brings energy, focus, and clear purpose to Dash's mission.</p>
            <div class="team-social">
              <a href="https://au.linkedin.com/in/ricky-schwartz" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
                  <rect x="2" y="9" width="4" height="12"/>
                  <circle cx="4" cy="4" r="2"/>
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div class="team-card">
          <div class="team-image">
            <img src="images/adamabtus.png" alt="Adam Kallenbach" class="team-photo">
          </div>
          <div class="team-info">
            <h3>Adam Kallenbach</h3>
            <p class="team-role">Chief Technology Officer</p>
            <p class="team-bio">Currently studying Cyber Security at Swinburne University. Dedicated to all things tech, from hardware to software development. His expertise enables smooth development and valuable insights for the Dash platform.</p>
            <div class="team-social">
              <a href="https://au.linkedin.com/in/adam-kallenbach-3578b6372" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"> 
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
                  <rect x="2" y="9" width="4" height="12"/>
                  <circle cx="4" cy="4" r="2"/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <img src="images/appicon.png" alt="Dash" class="footer-logo">
          <span class="footer-title">Dash</span>
          <p>The modern way to split expenses</p>
        </div>
        
        <div class="footer-links">
          <div class="footer-column">
            <h4>Product</h4>
            <a href="#features">Features</a>
            <a href="#pricing">Pricing</a>
            <a href="#security">Security</a>
          </div>
          
          <div class="footer-column">
            <h4>Company</h4>
            <a href="aboutus.html">About Us</a>
            <a href="#careers">Careers</a>
            <a href="#contact">Contact</a>
          </div>
          
          <div class="footer-column">
            <h4>Support</h4>
            <a href="#help">Help Center</a>
            <a href="#privacy">Privacy Policy</a>
            <a href="#terms">Terms of Service</a>
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2025 Dash. All rights reserved.</p>
        <div class="footer-social">
          <a href="#" aria-label="Twitter">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
            </svg>
          </a>
          <a href="#" aria-label="LinkedIn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
              <rect x="2" y="9" width="4" height="12"/>
              <circle cx="4" cy="4" r="2"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Download Modal -->
  <div id="downloadModal" class="modal-overlay">
    <div class="modal-content">
      <button class="modal-close" id="modalClose">&times;</button>
      <div class="modal-header">
        <h3>Coming Soon</h3>
        <p>Enter your email and we'll notify you when Dash launches.</p>
      </div>
      <form
        action="https://formspree.io/f/mgvzzwgz"
        method="POST"
        id="notifyForm"
        class="download-options"
      >
        <input
          type="email"
          name="email"
          id="emailInput"
          placeholder="Enter your email"
          required
          style="padding: 10px; width: 100%; max-width: 300px; border-radius: 8px; border: 1px solid #ccc;"
        />
        <button type="submit" class="primary-button small">Notify Me</button>
        <p id="notifyMessage" style="margin-top: 10px; color: green; display: none;">Thanks! We'll keep you posted.</p>
      </form>
    </div>
  </div>

</body>
</html>
