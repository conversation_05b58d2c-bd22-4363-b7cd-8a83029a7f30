<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Dash - Split payments instantly with friends. The modern way to share expenses." />
  <meta name="keywords" content="split payments, expense sharing, fintech, mobile payments" />
  <title>Dash - Split payments instantly</title>
  <link rel="stylesheet" href="CSS/style.css" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script defer src="JS/script.js"></script>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar" id="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <img src="images/appicon.png" alt="Dash" class="nav-logo">
        <span class="nav-title">Dash <span style="margin: 0 12px;">|</span> The New Way to Pay</span>
      </div>

      <div class="nav-menu" id="navMenu">
        <a href="#home" class="nav-link active">Home</a>
        <a href="aboutus.html" class="nav-link">About</a>
        <button class="cta-button" id="downloadBtn">
          <span>Download App</span>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="1 2" y1="15" x2="12" y2="3"/>
          </svg>
        </button>
      </div>

      <div class="hamburger" id="hamburger">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero" id="home">
    <div class="hero-container">
      <div class="hero-content">
        <div class="hero-badge">
          <span><strong>New</strong> split payments <u>instantly</u></span>
        </div>
        <h1 class="hero-title">
          The modern way to
          <span class="gradient-text">split expenses</span>
        </h1>
        <p class="hero-description">
          Stop the awkward money conversations. Dash makes splitting bills with friends as easy as sending a text.
        </p>
        <div class="hero-actions">
          <button class="primary-button" id="heroDownload">
            <span>Download for iOS</span>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
          </button>
          <button class="secondary-button">
            <span>Watch Demo</span>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="5,3 19,12 5,21"/>
            </svg>
          </button>
        </div>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number">10K+</span>
            <span class="stat-label">Active Users</span>
          </div>
          <div class="stat">
            <span class="stat-number">$2M+</span>
            <span class="stat-label">Split Successfully</span>
          </div>
          <div class="stat">
            <span class="stat-number">4.9★</span>
            <span class="stat-label">App Store Rating</span>
          </div>
        </div>
      </div>
      <div class="hero-visual">
        <div class="phone-mockup">
          <img src="images/DashBanner.png" alt="Dash App Interface" class="app-screenshot">
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features" id="features">
    <div class="container">
      <div class="section-header">
        <h2>Everything you need to split smarter</h2>
        <p>Powerful features designed to make group payments effortless</p>
      </div>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7l10 5 10-5-10-5z"/>
              <path d="M2 17l10 5 10-5"/>
              <path d="M2 12l10 5 10-5"/>
            </svg>
          </div>
          <h3>Smart Splitting</h3>
          <p>Automatically calculate who owes what with our intelligent splitting algorithms</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
          </div>
          <h3>Real-time Updates</h3>
          <p>See balance changes instantly as expenses are added and payments are made</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <path d="M9 9h6v6H9z"/>
            </svg>
          </div>
          <h3>QR Payments</h3>
          <p>Pay friends instantly by scanning QR codes - no bank details needed</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
          </div>
          <h3>Bank-level Security</h3>
          <p>Your financial data is protected with enterprise-grade encryption</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          </div>
          <h3>Instant Settlements</h3>
          <p>Settle debts immediately with integrated payment processing</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
          </div>
          <h3>Group Management</h3>
          <p>Create and manage multiple groups for different occasions and friend circles</p>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section class="how-it-works">
    <div class="container">
      <div class="section-header">
        <h2>How Dash works</h2>
        <p>Get started in three simple steps</p>
      </div>

      <div class="steps-container">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h3>Create a group</h3>
            <p>Add friends and start tracking shared expenses together</p>
          </div>
        </div>

        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h3>Add expenses</h3>
            <p>Snap photos of receipts or manually enter amounts</p>
          </div>
        </div>

        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h3>Settle instantly</h3>
            <p>Pay friends directly through the app with one tap</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="testimonials">
    <div class="container">
      <div class="section-header">
        <h2>Loved by thousands</h2>
        <p>See what our users are saying</p>
      </div>

      <div class="testimonials-grid">
        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"Finally, no more awkward IOUs. Dash makes everything so simple!"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">J</div>
            <div class="author-info">
              <span class="author-name">Jane D.</span>
              <span class="author-title">College Student</span>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"The app saved our trip. Super easy to use and everyone could track expenses."</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">A</div>
            <div class="author-info">
              <span class="author-name">Alex M.</span>
              <span class="author-title">Travel Enthusiast</span>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"Perfect for roommates. No more spreadsheets or forgotten payments!"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">S</div>
            <div class="author-info">
              <span class="author-name">Sarah K.</span>
              <span class="author-title">Working Professional</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="container">
      <div class="cta-content">
        <h2>Ready to split smarter?</h2>
        <p>Join thousands of users who've simplified their group payments</p>
        <button class="primary-button large" id="ctaDownload">
          <span>Download Dash Now</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
        </button>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <img src="images/appicon.png" alt="Dash" class="footer-logo">
          <span class="footer-title">Dash</span>
          <p>The modern way to split expenses</p>
        </div>

        <div class="footer-links">
          <div class="footer-column">
            <h4>Product</h4>
            <a href="#features">Features</a>
            <a href="#pricing">Pricing</a>
            <a href="#security">Security</a>
          </div>

          <div class="footer-column">
            <h4>Company</h4>
            <a href="aboutus.html">About Us</a>
            <a href="#careers">Careers</a>
            <a href="#contact">Contact</a>
          </div>

          <div class="footer-column">
            <h4>Support</h4>
            <a href="#help">Help Center</a>
            <a href="#privacy">Privacy Policy</a>
            <a href="#terms">Terms of Service</a>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; 2025 Dash. All rights reserved.</p>
        <div class="footer-social">
          <a href="#" aria-label="Twitter">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
            </svg>
          </a>
          <a href="#" aria-label="LinkedIn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
              <rect x="2" y="9" width="4" height="12"/>
              <circle cx="4" cy="4" r="2"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Download Modal -->
  <div id="downloadModal" class="modal-overlay">
    <div class="modal-content">
      <button class="modal-close" id="modalClose">&times;</button>
      <div class="modal-header">
        <h3>Download Dash</h3>
        <p>Get the app and start splitting smarter</p>
      </div>
      <div class="download-options">
        <a href="#" class="download-button ios">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
          </svg>
          <div>
            <span class="download-store">Download on the</span>
            <span class="download-name">App Store</span>
          </div>
        </a>
        <div class="qr-section">
          <p>Or scan QR code:</p>
          <div class="qr-placeholder">
            <svg width="120" height="120" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <rect x="7" y="7" width="3" height="3"/>
              <rect x="14" y="7" width="3" height="3"/>
              <rect x="7" y="14" width="3" height="3"/>
              <rect x="14" y="14" width="3" height="3"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

</body>
</html>