/* Modern CSS Reset and Variables */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Colors - Green theme inspired by your app */
  --primary: #00D26A;
  --primary-dark: #00B85C;
  --primary-light: #E8F8F0;
  --secondary: #1A1A1A;
  --text-primary: #1A1A1A;
  --text-secondary: #6B7280;
  --text-muted: #9CA3AF;
  --background: #FFFFFF;
  --surface: #F9FAFB;
  --border: #E5E7EB;
  --border-light: #F3F4F6;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border radius */
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  --radius-2xl: 2rem;

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition: 200ms ease;
  --transition-slow: 300ms ease;
}

/* Base styles */
html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  transition: all var(--transition);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
}

.nav-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.nav-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.nav-link {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
  text-decoration: none;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius);
  transition: all var(--transition);
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
  background-color: var(--surface);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background-color: var(--primary);
  border-radius: 50%;
}

.cta-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
}

.cta-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.cta-button svg {
  transition: transform var(--transition);
}

.cta-button:hover svg {
  transform: translateY(1px);
}

/* Hamburger menu */
.hamburger {
  display: none;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius);
  transition: all var(--transition);
}

.hamburger span {
  width: 20px;
  height: 2px;
  background-color: var(--text-primary);
  border-radius: 1px;
  transition: all var(--transition);
}

.hamburger:hover {
  background-color: var(--surface);
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Section header */
.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-header h2 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  letter-spacing: -0.025em;
}

.section-header p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Hero Section */
.hero {
  padding: calc(72px + var(--space-20)) var(--space-6) var(--space-24);
  background: linear-gradient(135deg, #FAFBFC 0%, #F8FAFC 100%);
  overflow: hidden;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 210, 106, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(0, 210, 106, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-content {
  max-width: 540px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--primary-light);
  color: var(--primary-dark);
  border-radius: var(--radius-2xl);
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--space-6);
  border: 1px solid rgba(0, 210, 106, 0.2);
}

.hero-badge span:first-child {
  font-weight: 600;
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: 700;
  line-height: 1.1;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  letter-spacing: -0.025em;
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-8);
  max-width: 480px;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
  justify-content: center;
}

.primary-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow-md);
  text-decoration: none;
}

.primary-button:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.primary-button.large {
  padding: var(--space-5) var(--space-8);
  font-size: var(--font-size-lg);
}

.primary-button.small {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-sm);
}

.secondary-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition);
  text-decoration: none;
}

.secondary-button:hover {
  background: var(--surface);
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-1px);
}

.hero-stats {
  display: flex;
  gap: var(--space-8);
}

.stat {
  text-align: left;
}

.stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.phone-mockup {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.app-screenshot {
  width: 100%;
  height: auto;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  transition: transform var(--transition-slow);
}

.app-screenshot:hover {
  transform: scale(1.02) rotate(1deg);
}

/* Features Section */
.features {
  padding: var(--space-24) 0;
  background: var(--background);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.feature-card {
  padding: var(--space-8);
  background: var(--background);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary), transparent);
  opacity: 0;
  transition: opacity var(--transition);
}

.feature-card:hover {
  border-color: var(--primary);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-light);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-5);
  transition: all var(--transition);
}

.feature-icon svg {
  color: var(--primary);
  transition: transform var(--transition);
}

.feature-card:hover .feature-icon {
  background: var(--primary);
  transform: scale(1.1);
}

.feature-card:hover .feature-icon svg {
  color: white;
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  letter-spacing: -0.025em;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
  padding: var(--space-24) 0;
  background: var(--surface);
}

.steps-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-12);
  margin-top: var(--space-16);
}

.step {
  display: flex;
  gap: var(--space-6);
  align-items: flex-start;
}

.step-number {
  width: 48px;
  height: 48px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  font-weight: 700;
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
}

.step-content h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  letter-spacing: -0.025em;
}

.step-content p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Testimonials Section */
.testimonials {
  padding: var(--space-24) 0;
  background: var(--background);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.testimonial-card {
  padding: var(--space-8);
  background: var(--background);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  transition: all var(--transition);
  position: relative;
}

.testimonial-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.testimonial-content {
  margin-bottom: var(--space-6);
}

.testimonial-content p {
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  line-height: 1.6;
  font-style: italic;
  position: relative;
}

.testimonial-content p::before {
  content: '"';
  font-size: var(--font-size-4xl);
  color: var(--primary);
  position: absolute;
  left: -16px;
  top: -8px;
  font-family: serif;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.author-avatar {
  width: 48px;
  height: 48px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.author-name {
  font-weight: 600;
  color: var(--text-primary);
  display: block;
}

.author-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: block;
}

/* CTA Section */
.cta-section {
  padding: var(--space-24) 0;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  letter-spacing: -0.025em;
}

.cta-content p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-8);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-content .primary-button {
  background: white;
  color: var(--primary);
  box-shadow: var(--shadow-lg);
}

.cta-content .primary-button:hover {
  background: var(--surface);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* Footer */
.footer {
  background: var(--secondary);
  color: white;
  padding: var(--space-16) 0 var(--space-8);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-16);
  margin-bottom: var(--space-12);
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.footer-brand > div:first-child {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.footer-logo {
  width: 32px;
  height: 32px;
  border-radius: var(--radius);
}

.footer-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.footer-brand p {
  color: rgba(255, 255, 255, 0.7);
  max-width: 300px;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-8);
}

.footer-column h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: white;
}

.footer-column a {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  margin-bottom: var(--space-2);
  transition: color var(--transition);
}

.footer-column a:hover {
  color: var(--primary);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--font-size-sm);
}

.footer-social {
  display: flex;
  gap: var(--space-4);
}

.footer-social a {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  transition: all var(--transition);
}

.footer-social a:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
}

/* About Page Styles */
.about-hero {
  padding: calc(72px + var(--space-20)) 0 var(--space-20);
  background: linear-gradient(135deg, #FAFBFC 0%, #F8FAFC 100%);
  text-align: center;
}

.about-hero-content h1 {
  font-size: var(--font-size-5xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
  letter-spacing: -0.025em;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.story-section {
  padding: var(--space-24) 0;
  background: var(--background);
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.story-text h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  letter-spacing: -0.025em;
}

.story-text p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.story-stats {
  display: flex;
  gap: var(--space-8);
  margin-top: var(--space-8);
}

.stat-item {
  text-align: left;
}

.stat-item .stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-item .stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.story-visual {
  display: flex;
  justify-content: center;
}

.story-image img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

/* Team Section */
.team-section {
  padding: var(--space-24) 0;
  background: var(--surface);
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-12);
  margin-top: var(--space-16);
}

.team-card {
  background: var(--background);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all var(--transition);
  text-align: center;
}

.team-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary);
}

.team-image {
  margin-bottom: var(--space-6);
}

.team-photo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary-light);
  transition: all var(--transition);
}

.team-card:hover .team-photo {
  border-color: var(--primary);
  transform: scale(1.05);
}

.team-info h3 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  letter-spacing: -0.025em;
}

.team-role {
  font-size: var(--font-size-base);
  color: var(--primary);
  font-weight: 600;
  margin-bottom: var(--space-4);
}

.team-bio {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
  text-align: left;
}

.team-social {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
}

.team-social a {
  width: 40px;
  height: 40px;
  background: var(--surface);
  border: 1px solid var(--border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: all var(--transition);
}

.team-social a:hover {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
  transform: translateY(-2px);
}

/* Download Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--background);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  max-width: 500px;
  width: 90%;
  position: relative;
  transform: scale(0.9);
  transition: transform var(--transition);
}

.modal-overlay.show .modal-content {
  transform: scale(1);
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition);
}

.modal-close:hover {
  background: var(--surface);
  color: var(--text-primary);
}

.modal-header {
  text-align: center;
  margin-bottom: var(--space-8);
}

.modal-header h3 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.modal-header p {
  color: var(--text-secondary);
}

.download-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  align-items: center;
}

.download-button {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4) var(--space-6);
  background: var(--secondary);
  color: white;
  border-radius: var(--radius-lg);
  text-decoration: none;
  transition: all var(--transition);
  min-width: 200px;
}

.download-button:hover {
  background: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.download-button div {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.download-store {
  font-size: var(--font-size-xs);
  opacity: 0.8;
}

.download-name {
  font-size: var(--font-size-base);
  font-weight: 600;
}

.qr-section {
  text-align: center;
}

.qr-section p {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
}

.qr-placeholder {
  width: 120px;
  height: 120px;
  background: var(--surface);
  border: 2px dashed var(--border);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-12);
    text-align: center;
  }

  .hero-visual {
    order: -1;
  }

  .story-content {
    grid-template-columns: 1fr;
    gap: var(--space-12);
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 72px;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    flex-direction: column;
    padding: var(--space-6);
    gap: var(--space-4);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition);
  }

  .nav-menu.show {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .hamburger {
    display: flex;
  }

  .hero {
    padding: calc(72px + var(--space-12)) var(--space-4) var(--space-16);
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .steps-container {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .team-grid {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .footer-bottom {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .container {
    padding: 0 var(--space-4);
  }

  .section-header h2 {
    font-size: var(--font-size-3xl);
  }

  .about-hero-content h1 {
    font-size: var(--font-size-4xl);
  }

  .story-text h2 {
    font-size: var(--font-size-2xl);
  }

  .cta-content h2 {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    justify-content: center;
  }

  .story-stats {
    flex-direction: column;
    gap: var(--space-4);
  }

  .modal-content {
    margin: var(--space-4);
    width: calc(100% - var(--space-8));
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-content > * {
  animation: fadeInUp 0.6s ease-out forwards;
}

.hero-badge {
  animation-delay: 0.1s;
}

.hero-title {
  animation-delay: 0.2s;
}

.hero-description {
  animation-delay: 0.3s;
}

.hero-actions {
  animation-delay: 0.4s;
}

.hero-stats {
  animation-delay: 0.5s;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.site-header {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 20px;     
    transition: padding var(--transition);
  }

  .logo {
    height: 50px;
    transition: height var(--transition);
    margin-left: -100px;
  }

.header1.shrink .logo {
  height: 40px;
}

.site-title {
  flex: 1;
  text-align: center;
  font-size: 28px;
  font-weight: 700;
  color: var(--white);
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  margin: 0;
  margin-left: 20px;
}

/* Hamburger */
.hamburger {
  display: none;
  font-size: 28px;
  cursor: pointer;
  color: #000; 
  z-index: 1100;
  transition: transform var(--transition);
}

.hamburger.open {
  transform: rotate(90deg);
}

.desktop-nav {
    position: relative;
    width: auto;
    margin: 0 auto;
    margin-bottom: 0;
    padding: 0 20px;
    z-index: 999;
    animation: fadeIn 0.9s ease-out;
  }

  .desktop-nav ul {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 80px;
    list-style: none;
    padding: 0;
    padding-right: 45px;
    margin: 0 auto;
    max-width: 800px;
  }

.desktop-nav li {
  opacity: 0;
  animation: fadeInUp 0.6s var(--delay) forwards;
}

.desktop-nav li:nth-child(1) { --delay: 0.2s; }
.desktop-nav li:nth-child(2) { --delay: 0.4s; }
.desktop-nav li:nth-child(3) { --delay: 0.6s; }

.desktop-nav a {
  text-decoration: none;
  font-size: 18px;
  font-weight: 700;
  color: var(--text);
  position: relative;
  padding: 10px 15px;
  transition: color var(--transition);
  border-radius: 6px;
}

.desktop-nav a::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 0%;
  height: 2px;
  background: var(--primary);
  transition: width var(--transition);
}

.desktop-nav a:hover {
  color: var(--primary-dark);
  background-color: rgba(76, 175, 80, 0.1);
}

.desktop-nav a:hover::after {
  width: 100%;
}

/* fadeInUp keyframes */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(10px); }
  to   { opacity: 1; transform: translateY(0); }
}

/* Mobile slide-out menu */

body.nav-open {
  overflow: hidden;
}

.nav-menu.show {
  right: 0;
}

.nav-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu li {
  transform: translateX(50px);
  opacity: 0;
  animation: slideIn 0.4s var(--delay) forwards;
}

.nav-menu li:nth-child(1) { --delay: 0.3s; }
.nav-menu li:nth-child(2) { --delay: 0.5s; }
.nav-menu li:nth-child(3) { --delay: 0.7s; }

@keyframes slideIn {
  from { opacity: 0; transform: translateX(50px); }
  to   { opacity: 1; transform: translateX(0); }
}

.nav-menu a {
  display: block;
  padding: 15px 30px;
  color: var(--primary);
  font-size: 18px;
  font-weight: 1000;
  text-decoration: none;
  transition: background var(--transition);
}

.nav-menu a:hover {
  background: rgba(255,255,255,0.1);
}

/* Hero banner */
.banner-image {
  position: relative;
  margin-top: 0;
  overflow: hidden;
  height: 100vh;
}

.banner-image::after {
  content: '';
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: rgba(0,0,0,0.2);
  pointer-events: none;
}

.Dash-Banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center -450px;
  display: block;
  transition: transform 0.3s ease;
  transform: none;
}

/* FadeIn keyframes for nav wrapper */
@keyframes fadeIn {
  from { opacity: 0; }
  to   { opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background-color: var(--text);
    transition: right var(--transition);
    z-index: 1050;
    padding-top: 80px;
  }
  .hamburger {
    display: block;
  }
  .desktop-nav {
    display: none;
  }
  .banner-image {
    margin-top: 80px;
    height: 100vh;
  }
  .site-header {
    justify-content: space-between;
  }

  .download-link {
    position: static !important;
    transform: none !important;
    margin: 10px auto;
    display: block;
    text-align: center;
  }

  .hamburger {
    order: 1;
  }

  .logo {
    margin-left: 0;
  }

  .Dash-Banner {
    width: 100%;
    height: 100vh;
    object-fit: cover;
    object-position: center;
    display: block;
  }
}

/* About Us Page Styling */
.about-us-section {
  height: 100vh;
  padding: 110px 20px 40px;
  box-sizing: border-box;
  background-color: var(--white);
  color: var(--text);
  text-align: center;
  overflow: hidden;
}

.about-us-section h2 {
  font-size: 30px;
  margin-bottom: 20px;
  color: var(--primary-dark);
}

.team-section h2 {
  margin-top: -15px; 
}

.about-us-section h2 + p {
  max-width: 800px;
  margin: 0 auto 40px;
  font-size: 17px;
  color: #555;
  line-height: 1.6;
}

.company-info p {
  max-width: 800px;
  margin: 0 auto 20px;
  font-size: 18px;
  line-height: 1.6;
}


.team-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: -5px 20px 30px;
}

.team-members {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 40px;
  justify-items: center;
  align-items: stretch;
}

.team-member {
  background-color: var(--bg);
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
  width: 100%;
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
}

.profile-photo {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 50%;
  margin-bottom: 15px;
}

.team-member h3 {
  margin: 10px 0 5px;
  font-size: 22px;
  color: var(--primary);
}

.team-member h4 {
  margin: 0 0 10px;
  font-size: 16px;
  color: #666;
}

.team-member p {
  font-size: 15px;
  line-height: 1.5;
}

#downloadNav,
#downloadNav:visited,
#downloadNav:active,
#downloadNav:focus {
  position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  text-decoration: none;
  font-size: 18px;
  font-weight: 700;
  color: var(--text);
  padding: 10px 15px;
  border-radius: 6px;
  background: none;
  border: none;
  cursor: pointer;
  transition: color var(--transition), background-color var(--transition);
  /* If needed, adjust z-index to appear above nav */
}

#downloadNav:hover {
  color: var(--primary-dark);
  background-color: rgba(76, 175, 80, 0.1);
}

#downloadNav::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 0%;
  height: 2px;
  background: var(--primary);
  transition: width var(--transition);
}

#downloadNav:hover::after {
  width: 100%;
}
/*General Section Styling*/
section {
  padding: 80px 20px;
  background-color: var(--white);
  color: var(--text);
  text-align: center;
  animation: fadeIn 1s ease-out;
}

section h2 {
  font-size: 32px;
  margin-bottom: 20px;
  color: var(--primary-dark);
  animation: fadeInUp 0.6s ease-out;
}

section p,
section ol,
section ul,
section blockquote {
  font-size: 18px;
  max-width: 800px;
  margin: 0 auto 30px;
  line-height: 1.6;
  color: #555;
  animation: fadeInUp 0.6s ease-out;
}

section ol,
section ul {
  text-align: left;
  list-style-position: inside;
}

section li {
  margin-bottom: 10px;
  animation: fadeInUp 0.6s ease-out;
}

section blockquote {
  font-style: italic;
  color: #444;
  border-left: 5px solid var(--primary);
  padding-left: 15px;
  margin-top: 20px;
}

.site-footer {
  padding: 40px 20px;
  background-color: var(--primary-dark);
  color: var(--white);
  text-align: center;
  animation: fadeIn 1s ease-out;
}

.site-footer p {
  margin: 0;
  font-size: 16px;
}

.container {
  max-width: 1100px;
  margin: 0 auto;
}