import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import multer from 'multer';
import PassGeneratorService from './services/PassGeneratorService.js';

// Initialize environment variables
dotenv.config();

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create directories if they don't exist
const CERTS_DIR = path.join(__dirname, '.certificates');
fs.ensureDirSync(CERTS_DIR);

// Initialize pass generator service
const passGenerator = new PassGeneratorService();

// Set up Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Set up multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, CERTS_DIR);
  },
  filename: function (req, file, cb) {
    cb(null, file.originalname);
  }
});
const upload = multer({ storage: storage });

// Routes
app.get('/', (req, res) => {
  res.send('Dash Wallet Pass Server is running!');
});

// Check server status
app.get('/status', (req, res) => {
  try {
    const certStatus = passGenerator.checkCertificates();
    const passes = passGenerator.listPasses();

    res.status(200).json({
      status: 'ok',
      certificates: {
        valid: certStatus.valid,
        missing: certStatus.missing
      },
      passes: {
        count: passes.length,
        latest: passes.length > 0 ? passes[0] : null
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message
    });
  }
});

// Upload certificates
app.post('/upload-certificates', upload.fields([
  { name: 'wwdr', maxCount: 1 },
  { name: 'signerCert', maxCount: 1 },
  { name: 'signerKey', maxCount: 1 }
]), (req, res) => {
  try {
    const files = req.files;
    if (!files.wwdr || !files.signerCert || !files.signerKey) {
      return res.status(400).json({ error: 'Missing required certificate files' });
    }

    res.status(200).json({ message: 'Certificates uploaded successfully' });
  } catch (error) {
    console.error('Error uploading certificates:', error);
    res.status(500).json({ error: 'Failed to upload certificates' });
  }
});

// Generate pass
app.post('/generate-pass', async (req, res) => {
  try {
    const { userId, userName, userEmail, balance } = req.body;

    if (!userId || !userName) {
      return res.status(400).json({ error: 'Missing required user information' });
    }

    // Check certificates
    const certStatus = passGenerator.checkCertificates();
    if (!certStatus.valid) {
      return res.status(400).json({
        error: 'Missing certificates',
        missing: certStatus.missing
      });
    }

    // Generate pass
    const passData = await passGenerator.generatePass({
      userId,
      userName,
      userEmail,
      balance
    });

    // Send pass file
    res.set({
      'Content-Type': 'application/vnd.apple.pkpass',
      'Content-Disposition': `attachment; filename="${passData.fileName}"`
    });

    res.send(passData.buffer);
  } catch (error) {
    console.error('Error generating pass:', error);
    res.status(500).json({ error: 'Failed to generate pass', details: error.message });
  }
});

// List all passes
app.get('/passes', (req, res) => {
  try {
    const passes = passGenerator.listPasses();
    res.status(200).json(passes);
  } catch (error) {
    console.error('Error listing passes:', error);
    res.status(500).json({ error: 'Failed to list passes' });
  }
});

// Get pass info
app.get('/passes/:serialNumber', (req, res) => {
  try {
    const { serialNumber } = req.params;
    const passInfo = passGenerator.getPassInfo(serialNumber);
    res.status(200).json(passInfo);
  } catch (error) {
    console.error('Error getting pass info:', error);
    res.status(404).json({ error: 'Pass not found' });
  }
});

// Download pass
app.get('/passes/:serialNumber/download', (req, res) => {
  try {
    const { serialNumber } = req.params;
    const passInfo = passGenerator.getPassInfo(serialNumber);

    const passBuffer = fs.readFileSync(passInfo.filePath);

    res.set({
      'Content-Type': 'application/vnd.apple.pkpass',
      'Content-Disposition': `attachment; filename="${serialNumber}.pkpass"`
    });

    res.send(passBuffer);
  } catch (error) {
    console.error('Error downloading pass:', error);
    res.status(404).json({ error: 'Pass not found' });
  }
});

// Update pass
app.post('/passes/:serialNumber/update', async (req, res) => {
  try {
    const { serialNumber } = req.params;
    const { balance, userName, userEmail } = req.body;

    // Extract user ID from serial number
    const userIdMatch = serialNumber.match(/dash-(.+?)-\d+/);
    if (!userIdMatch) {
      return res.status(400).json({ error: 'Invalid serial number format' });
    }

    const userId = userIdMatch[1];

    // Generate updated pass
    const passData = await passGenerator.generatePass({
      userId,
      userName: userName || 'Dash User',
      userEmail: userEmail || '',
      balance: balance || 0
    });

    // Send updated pass file
    res.set({
      'Content-Type': 'application/vnd.apple.pkpass',
      'Content-Disposition': `attachment; filename="${passData.fileName}"`
    });

    res.send(passData.buffer);
  } catch (error) {
    console.error('Error updating pass:', error);
    res.status(500).json({ error: 'Failed to update pass', details: error.message });
  }
});

// Apple Wallet Web Service endpoints for pass updates
// These endpoints are called by iOS devices when passes are added/removed

// Device registration
app.post('/v1/devices/:deviceLibraryIdentifier/registrations/:passTypeIdentifier/:serialNumber', (req, res) => {
  const { deviceLibraryIdentifier, passTypeIdentifier, serialNumber } = req.params;
  const { pushToken } = req.body;
  const authHeader = req.headers.authorization;

  // Validate authentication token
  const expectedAuth = `ApplePass ${process.env.AUTHENTICATION_TOKEN}`;
  if (authHeader !== expectedAuth) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  console.log('Device registration:', {
    deviceLibraryIdentifier,
    passTypeIdentifier,
    serialNumber,
    pushToken
  });

  // In a real implementation, you would store this in a database
  // For now, just return success
  res.status(201).json({ message: 'Registration successful' });
});

// Get updatable passes
app.get('/v1/devices/:deviceLibraryIdentifier/registrations/:passTypeIdentifier', (req, res) => {
  const { deviceLibraryIdentifier, passTypeIdentifier } = req.params;
  const passesUpdatedSince = req.query.passesUpdatedSince;
  const authHeader = req.headers.authorization;

  // Validate authentication token
  const expectedAuth = `ApplePass ${process.env.AUTHENTICATION_TOKEN}`;
  if (authHeader !== expectedAuth) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  console.log('Get updatable passes:', {
    deviceLibraryIdentifier,
    passTypeIdentifier,
    passesUpdatedSince
  });

  // In a real implementation, you would check your database for passes that need updates
  // For now, return empty list
  res.status(204).send();
});

// Get latest version of pass
app.get('/v1/passes/:passTypeIdentifier/:serialNumber', (req, res) => {
  const { passTypeIdentifier, serialNumber } = req.params;
  const authHeader = req.headers.authorization;

  // Validate authentication token
  const expectedAuth = `ApplePass ${process.env.AUTHENTICATION_TOKEN}`;
  if (authHeader !== expectedAuth) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const passInfo = passGenerator.getPassInfo(serialNumber);
    const passBuffer = fs.readFileSync(passInfo.filePath);

    res.set({
      'Content-Type': 'application/vnd.apple.pkpass',
      'Last-Modified': passInfo.modified.toUTCString()
    });

    res.send(passBuffer);
  } catch (error) {
    console.error('Error getting pass:', error);
    res.status(404).json({ error: 'Pass not found' });
  }
});

// Device unregistration
app.delete('/v1/devices/:deviceLibraryIdentifier/registrations/:passTypeIdentifier/:serialNumber', (req, res) => {
  const { deviceLibraryIdentifier, passTypeIdentifier, serialNumber } = req.params;
  const authHeader = req.headers.authorization;

  // Validate authentication token
  const expectedAuth = `ApplePass ${process.env.AUTHENTICATION_TOKEN}`;
  if (authHeader !== expectedAuth) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  console.log('Device unregistration:', {
    deviceLibraryIdentifier,
    passTypeIdentifier,
    serialNumber
  });

  // In a real implementation, you would remove this from your database
  res.status(200).json({ message: 'Unregistration successful' });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Dash Wallet Pass Server running on port ${PORT}`);
  console.log(`Local URL: http://localhost:${PORT}`);
  console.log(`Network URL: http://*************:${PORT}`);
  console.log(`Web Service URL: http://*************:${PORT}/v1/`);
});
