{"name": "dash-wallet-pass-server", "version": "1.0.0", "description": "Local server for generating and serving Apple Wallet passes for Dash app", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js", "test": "node test.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "passkit-generator": "^3.4.0", "axios": "^1.7.9", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "fs-extra": "^11.2.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["apple-wallet", "passkit", "wallet-pass", "dash"], "author": "Dash Team", "license": "MIT"}