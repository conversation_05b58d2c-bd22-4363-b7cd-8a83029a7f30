#!/bin/bash
# Resize images for Apple Wallet pass
# Requires ImageMagick: brew install imagemagick

cd "/Users/<USER>/Documents/GitHub/DashBranch/wallet-pass-server/models/DashCard.pass"

echo "Resizing images for Apple Wallet pass..."

# Resize icon
if [ -f "icon.png" ]; then
  magick convert icon.png -resize 29x29 icon_29x29.png
  magick convert icon.png -resize 58x58 <EMAIL>
  mv icon_29x29.png icon.png
  echo "✅ Resized icon.png"
fi

# Resize logo (keep aspect ratio, fit within 160x50)
if [ -f "logo.png" ]; then
  magick convert logo.png -resize 160x50 logo_160x50.png
  magick convert logo.png -resize 320x100 <EMAIL>
  mv logo_160x50.png logo.png
  echo "✅ Resized logo.png"
fi

# Resize thumbnail
if [ -f "thumbnail.png" ]; then
  magick convert thumbnail.png -resize 90x90 thumbnail_90x90.png
  magick convert thumbnail.png -resize 180x180 <EMAIL>
  mv thumbnail_90x90.png thumbnail.png
  echo "✅ Resized thumbnail.png"
fi

echo "🎉 All images resized for Apple Wallet pass!"
