<?xml version="1.0" encoding="UTF-8"?>
<svg width="375" height="123" viewBox="0 0 375 123" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- White background like the virtual card -->
  <rect width="375" height="123" fill="white"/>
  
  <!-- Subtle gradient or pattern could go here -->
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with subtle gradient -->
  <rect width="375" height="123" fill="url(#cardGradient)"/>
  
  <!-- Contactless payment symbol in corner -->
  <g transform="translate(320, 20)">
    <circle cx="0" cy="0" r="8" fill="none" stroke="#666666" stroke-width="1"/>
    <circle cx="0" cy="0" r="12" fill="none" stroke="#666666" stroke-width="1"/>
    <circle cx="0" cy="0" r="16" fill="none" stroke="#666666" stroke-width="1"/>
  </g>
  
  <!-- Mastercard logo in bottom right -->
  <g transform="translate(340, 90)">
    <!-- Red circle -->
    <circle cx="0" cy="0" r="9" fill="#EB001B"/>
    <!-- Orange circle overlapping -->
    <circle cx="12" cy="0" r="9" fill="#FF5F00"/>
  </g>
</svg>