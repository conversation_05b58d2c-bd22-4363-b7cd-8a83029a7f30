#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const appAssetsPath = path.join(__dirname, '..', 'Dash', 'Assets.xcassets');
const passModelPath = path.join(__dirname, 'models', 'DashCard.pass');

async function copyAppAssets() {
  console.log('📱 Copying Dash app assets to wallet pass...');
  
  // Ensure pass model directory exists
  fs.ensureDirSync(passModelPath);
  
  try {
    // Copy DashLogo as the main logo
    const dashLogoPath = path.join(appAssetsPath, 'DashLogo.imageset', 'dashlogo.png');
    if (fs.existsSync(dashLogoPath)) {
      // Copy as logo.png (resize to appropriate dimensions)
      fs.copyFileSync(dashLogoPath, path.join(passModelPath, 'logo.png'));
      console.log('✅ Copied <PERSON><PERSON><PERSON> as logo.png');
    } else {
      console.log('❌ DashLogo not found at:', dashLogoPath);
    }
    
    // Copy App Icon as the pass icon
    const appIconPath = path.join(appAssetsPath, 'AppIcon.appiconset', 'dashlogo.png');
    if (fs.existsSync(appIconPath)) {
      // Copy as icon.png (will need to resize to 29x29)
      fs.copyFileSync(appIconPath, path.join(passModelPath, 'icon.png'));
      console.log('✅ Copied AppIcon as icon.png');
    } else {
      console.log('❌ AppIcon not found at:', appIconPath);
    }
    
    // Copy DashCardIcon if it exists
    const cardIconPath = path.join(appAssetsPath, 'DashCardIcon.imageset', 'Artboard 1.png');
    if (fs.existsSync(cardIconPath)) {
      // This could be used as thumbnail
      fs.copyFileSync(cardIconPath, path.join(passModelPath, 'thumbnail.png'));
      console.log('✅ Copied DashCardIcon as thumbnail.png');
    } else {
      console.log('❌ DashCardIcon not found at:', cardIconPath);
    }
    
    console.log('\n📋 Next steps:');
    console.log('1. The images have been copied but may need resizing');
    console.log('2. Use an image editor or ImageMagick to resize:');
    console.log('   - icon.png to 29x29 pixels');
    console.log('   - logo.png to 160x50 pixels (or keep original size)');
    console.log('   - thumbnail.png to 90x90 pixels');
    console.log('3. Create @2x versions for retina displays');
    
    // Create a simple script to resize with ImageMagick if available
    const resizeScript = `#!/bin/bash
# Resize images for Apple Wallet pass
# Requires ImageMagick: brew install imagemagick

cd "${passModelPath}"

echo "Resizing images for Apple Wallet pass..."

# Resize icon
if [ -f "icon.png" ]; then
  magick convert icon.png -resize 29x29 icon_29x29.png
  magick convert icon.png -resize 58x58 <EMAIL>
  mv icon_29x29.png icon.png
  echo "✅ Resized icon.png"
fi

# Resize logo (keep aspect ratio, fit within 160x50)
if [ -f "logo.png" ]; then
  magick convert logo.png -resize 160x50 logo_160x50.png
  magick convert logo.png -resize 320x100 <EMAIL>
  mv logo_160x50.png logo.png
  echo "✅ Resized logo.png"
fi

# Resize thumbnail
if [ -f "thumbnail.png" ]; then
  magick convert thumbnail.png -resize 90x90 thumbnail_90x90.png
  magick convert thumbnail.png -resize 180x180 <EMAIL>
  mv thumbnail_90x90.png thumbnail.png
  echo "✅ Resized thumbnail.png"
fi

echo "🎉 All images resized for Apple Wallet pass!"
`;
    
    fs.writeFileSync(path.join(passModelPath, 'resize-images.sh'), resizeScript);
    fs.chmodSync(path.join(passModelPath, 'resize-images.sh'), '755');
    console.log('4. Run ./resize-images.sh to automatically resize (requires ImageMagick)');
    
  } catch (error) {
    console.error('❌ Error copying assets:', error.message);
  }
}

// Create a strip image that matches the virtual card design
function createStripImage() {
  console.log('\n🎨 Creating strip image to match virtual card...');
  
  // Create an SVG that matches the virtual card background
  const stripSvg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="375" height="123" viewBox="0 0 375 123" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- White background like the virtual card -->
  <rect width="375" height="123" fill="white"/>
  
  <!-- Subtle gradient or pattern could go here -->
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background with subtle gradient -->
  <rect width="375" height="123" fill="url(#cardGradient)"/>
  
  <!-- Contactless payment symbol in corner -->
  <g transform="translate(320, 20)">
    <circle cx="0" cy="0" r="8" fill="none" stroke="#666666" stroke-width="1"/>
    <circle cx="0" cy="0" r="12" fill="none" stroke="#666666" stroke-width="1"/>
    <circle cx="0" cy="0" r="16" fill="none" stroke="#666666" stroke-width="1"/>
  </g>
  
  <!-- Mastercard logo in bottom right -->
  <g transform="translate(340, 90)">
    <!-- Red circle -->
    <circle cx="0" cy="0" r="9" fill="#EB001B"/>
    <!-- Orange circle overlapping -->
    <circle cx="12" cy="0" r="9" fill="#FF5F00"/>
  </g>
</svg>`;

  // Save the strip SVG
  fs.writeFileSync(path.join(passModelPath, 'strip.svg'), stripSvg);
  console.log('✅ Created strip.svg (convert to PNG for use)');
  
  // Create a simple background image
  const backgroundSvg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="375" height="220" viewBox="0 0 375 220" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- White background matching virtual card -->
  <rect width="375" height="220" fill="white"/>
  
  <!-- Subtle shadow/border -->
  <rect x="1" y="1" width="373" height="218" fill="none" stroke="#e0e0e0" stroke-width="1" rx="12"/>
</svg>`;

  fs.writeFileSync(path.join(passModelPath, 'background.svg'), backgroundSvg);
  console.log('✅ Created background.svg');
}

// Run the functions
copyAppAssets();
createStripImage();

console.log('\n🎯 Summary:');
console.log('- Copied existing Dash app logos to wallet pass');
console.log('- Created strip and background images matching virtual card design');
console.log('- Generated resize script for proper dimensions');
console.log('\nThe wallet pass will now use the same branding as your app!');
