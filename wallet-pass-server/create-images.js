#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create SVG images for the pass
function createImages() {
  const modelDir = path.join(__dirname, 'models', 'DashCard.pass');
  fs.ensureDirSync(modelDir);

  // Create a simple Dash logo (icon.png)
  const iconSvg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="29" height="29" rx="6" fill="#000000"/>
  <text x="14.5" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">D</text>
</svg>`;

  // Create a logo (logo.png) - wider version
  const logoSvg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="29" viewBox="0 0 120 29" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="29" height="29" rx="6" fill="#000000"/>
  <text x="14.5" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">D</text>
  <text x="45" y="20" fill="black" font-family="Arial, sans-serif" font-size="18" font-weight="bold">DASH</text>
</svg>`;

  // Create a strip image (strip.png) - this appears at the bottom of the pass
  const stripSvg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="375" height="40" viewBox="0 0 375 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="375" height="40" fill="#f8f9fa"/>
  <!-- Mastercard logo -->
  <circle cx="320" cy="20" r="8" fill="#EB001B"/>
  <circle cx="332" cy="20" r="8" fill="#FF5F00"/>
  <text x="20" y="25" fill="#666666" font-family="Arial, sans-serif" font-size="12">Contactless</text>
</svg>`;

  // Create a thumbnail (thumbnail.png) - smaller version of the card
  const thumbnailSvg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="90" height="55" viewBox="0 0 90 55" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="90" height="55" rx="4" fill="white" stroke="#e0e0e0"/>
  <rect x="5" y="5" width="15" height="15" rx="3" fill="#000000"/>
  <text x="12.5" y="15" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">D</text>
  <text x="25" y="15" fill="black" font-family="Arial, sans-serif" font-size="10" font-weight="bold">DASH</text>
  <text x="5" y="30" fill="#666666" font-family="Arial, sans-serif" font-size="6">AVAILABLE BALANCE</text>
  <text x="5" y="40" fill="black" font-family="Arial, sans-serif" font-size="8" font-weight="bold">$0.00</text>
  <text x="5" y="50" fill="#666666" font-family="Arial, sans-serif" font-size="6">**** **** **** 1234</text>
</svg>`;

  // Save SVG files (these would need to be converted to PNG for actual use)
  fs.writeFileSync(path.join(modelDir, 'icon.svg'), iconSvg);
  fs.writeFileSync(path.join(modelDir, 'logo.svg'), logoSvg);
  fs.writeFileSync(path.join(modelDir, 'strip.svg'), stripSvg);
  fs.writeFileSync(path.join(modelDir, 'thumbnail.svg'), thumbnailSvg);

  console.log('✅ SVG images created in models/DashCard.pass/');
  console.log('Note: For production use, convert these SVG files to PNG format:');
  console.log('- icon.png (29x29)');
  console.log('- <EMAIL> (58x58)');
  console.log('- logo.png (120x29)');
  console.log('- <EMAIL> (240x58)');
  console.log('- strip.png (375x40)');
  console.log('- <EMAIL> (750x80)');
  console.log('- thumbnail.png (90x55)');
  console.log('- <EMAIL> (180x110)');
}

// Create a README for image requirements
function createImageReadme() {
  const modelDir = path.join(__dirname, 'models', 'DashCard.pass');
  const readmeContent = `# Pass Images

This directory should contain the following image files for the wallet pass:

## Required Images

### Icon (Required)
- **icon.png** (29x29) - Pass icon for notifications and pass library
- **<EMAIL>** (58x58) - Retina version

### Logo (Optional but recommended)
- **logo.png** (120x29) - Logo displayed on the pass
- **<EMAIL>** (240x58) - Retina version

### Strip (Optional)
- **strip.png** (375x40) - Image displayed behind the primary fields
- **<EMAIL>** (750x80) - Retina version

### Thumbnail (Optional)
- **thumbnail.png** (90x55) - Thumbnail for pass library
- **<EMAIL>** (180x110) - Retina version

## Design Guidelines

- Use PNG format with transparency support
- Follow Apple's design guidelines for wallet passes
- Keep images simple and readable at small sizes
- Use consistent branding with your app

## Current Status

SVG templates have been created. Convert these to PNG format using your preferred image editor or conversion tool.

For a quick conversion using ImageMagick:
\`\`\`bash
convert icon.svg -resize 29x29 icon.png
convert icon.svg -resize 58x58 <EMAIL>
convert logo.svg -resize 120x29 logo.png
convert logo.svg -resize 240x58 <EMAIL>
convert strip.svg -resize 375x40 strip.png
convert strip.svg -resize 750x80 <EMAIL>
convert thumbnail.svg -resize 90x55 thumbnail.png
convert thumbnail.svg -resize 180x110 <EMAIL>
\`\`\`
`;

  fs.writeFileSync(path.join(modelDir, 'IMAGES_README.md'), readmeContent);
}

// Run the script
createImages();
createImageReadme();

console.log('\n🎨 Pass images setup complete!');
console.log('Check models/DashCard.pass/ for SVG templates and instructions.');
