# Dash Wallet Pass Server

A local Node.js server for generating and serving Apple Wallet passes for the Dash Finance app.

## Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

Copy `.env.example` to `.env` and fill in your configuration:

```bash
cp .env.example .env
```

### 3. Apple Developer Certificates

You need to obtain the following certificates from Apple Developer:

1. **Pass Type ID**: Create a new Pass Type ID in Apple Developer Console
2. **Pass Certificate**: Generate a certificate for your Pass Type ID
3. **WWDR Certificate**: Download the Apple Worldwide Developer Relations certificate

#### Certificate Setup Steps:

1. Go to [Apple Developer Console](https://developer.apple.com/account/)
2. Navigate to Certificates, Identifiers & Profiles
3. Create a new Pass Type ID under Identifiers
4. Create a new certificate for your Pass Type ID
5. Download the WWDR certificate from Apple
6. Convert certificates to PEM format:

```bash
# Convert pass certificate to PEM
openssl pkcs12 -in pass.p12 -out signerCert.pem -clcerts -nokeys
openssl pkcs12 -in pass.p12 -out signerKey.pem -nocerts -nodes

# Convert WWDR certificate to PEM
openssl x509 -inform DER -in AppleWWDRCA.cer -out wwdr.pem
```

### 4. Upload Certificates

Start the server and upload your certificates:

```bash
npm start
```

Use the `/upload-certificates` endpoint to upload:
- `wwdr.pem`
- `signerCert.pem` 
- `signerKey.pem`

## API Endpoints

### POST /upload-certificates

Upload the required certificates for pass signing.

**Form Data:**
- `wwdr`: WWDR certificate file
- `signerCert`: Pass signing certificate file
- `signerKey`: Pass signing key file

### POST /generate-pass

Generate a wallet pass for a user.

**Body:**
```json
{
  "userId": "user123",
  "userName": "John Doe",
  "userEmail": "<EMAIL>",
  "balance": 150.50
}
```

**Response:** Returns a `.pkpass` file

## Usage

1. Start the server: `npm start`
2. Upload certificates via the upload endpoint
3. Generate passes by calling the generate-pass endpoint
4. The generated pass can be opened on iOS devices to add to Apple Wallet

## Development

For development with auto-reload:

```bash
npm run dev
```

## File Structure

```
wallet-pass-server/
├── .certificates/          # Certificate files (created automatically)
├── models/                 # Pass model templates
│   └── DashCard.pass/     # Default pass model
├── passes/                # Generated pass files
├── server.js              # Main server file
├── package.json           # Dependencies
└── .env                   # Environment configuration
```

## Security Notes

- Keep your certificates secure and never commit them to version control
- Use strong passphrases for your signing keys
- The server is intended for local development and testing
- For production, implement proper authentication and HTTPS
