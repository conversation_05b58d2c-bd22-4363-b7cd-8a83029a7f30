import fs from 'fs-extra';
import path from 'path';
import { PKPass } from 'passkit-generator';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class PassGeneratorService {
  constructor() {
    this.certsDir = path.join(__dirname, '..', '.certificates');
    this.modelsDir = path.join(__dirname, '..', 'models');
    this.passesDir = path.join(__dirname, '..', 'passes');
    
    // Ensure directories exist
    fs.ensureDirSync(this.certsDir);
    fs.ensureDirSync(this.modelsDir);
    fs.ensureDirSync(this.passesDir);
  }

  /**
   * Check if all required certificates are available
   */
  checkCertificates() {
    const requiredCerts = ['wwdr.pem', 'signerCert.pem', 'signerKey.pem'];
    const missingCerts = [];

    for (const cert of requiredCerts) {
      const certPath = path.join(this.certsDir, cert);
      if (!fs.existsSync(certPath)) {
        missingCerts.push(cert);
      }
    }

    return {
      valid: missingCerts.length === 0,
      missing: missingCerts
    };
  }

  /**
   * Load certificates for pass signing
   */
  loadCertificates() {
    const certCheck = this.checkCertificates();
    if (!certCheck.valid) {
      throw new Error(`Missing certificates: ${certCheck.missing.join(', ')}`);
    }

    return {
      wwdr: fs.readFileSync(path.join(this.certsDir, 'wwdr.pem')),
      signerCert: fs.readFileSync(path.join(this.certsDir, 'signerCert.pem')),
      signerKey: fs.readFileSync(path.join(this.certsDir, 'signerKey.pem')),
      signerKeyPassphrase: process.env.SIGNER_KEY_PASSPHRASE || ''
    };
  }

  /**
   * Create or update pass model
   */
  createPassModel(modelName = 'DashCard.pass') {
    const modelPath = path.join(this.modelsDir, modelName);
    const passJsonPath = path.join(modelPath, 'pass.json');

    fs.ensureDirSync(modelPath);

    if (!fs.existsSync(passJsonPath)) {
      const passJson = {
        formatVersion: 1,
        passTypeIdentifier: process.env.PASS_TYPE_IDENTIFIER || 'pass.com.dashfinanceapp.card',
        teamIdentifier: process.env.TEAM_IDENTIFIER || 'YOUR_TEAM_ID',
        organizationName: 'Dash Finance',
        description: 'Dash Premium Card',
        backgroundColor: 'rgb(255, 255, 255)',
        foregroundColor: 'rgb(0, 0, 0)',
        labelColor: 'rgb(102, 102, 102)',
        logoText: 'DASH',
        webServiceURL: process.env.WEB_SERVICE_URL,
        authenticationToken: process.env.AUTHENTICATION_TOKEN,
        generic: {
          headerFields: [
            {
              key: 'cardType',
              label: '',
              value: 'Premium',
              textAlignment: 'PKTextAlignmentRight'
            }
          ],
          primaryFields: [
            {
              key: 'balance',
              label: 'AVAILABLE BALANCE',
              value: 0,
              currencyCode: 'AUD'
            }
          ],
          secondaryFields: [
            {
              key: 'cardNumber',
              label: 'CARD NUMBER',
              value: '**** **** **** 1234'
            },
            {
              key: 'expiry',
              label: 'VALID THRU',
              value: '06/26',
              textAlignment: 'PKTextAlignmentRight'
            }
          ],
          auxiliaryFields: [
            {
              key: 'name',
              label: 'CARDHOLDER NAME',
              value: 'User Name'
            }
          ],
          backFields: [
            {
              key: 'email',
              label: 'EMAIL',
              value: '<EMAIL>'
            },
            {
              key: 'userId',
              label: 'USER ID',
              value: 'user-id'
            },
            {
              key: 'info',
              label: 'ABOUT',
              value: 'This is your Dash Premium card. Use it to access your account and track your balance. The card number shown is for display purposes only.'
            },
            {
              key: 'features',
              label: 'FEATURES',
              value: '• Contactless payments\n• Real-time balance updates\n• Secure transactions\n• Premium benefits'
            }
          ]
        }
      };

      fs.writeJsonSync(passJsonPath, passJson, { spaces: 2 });
    }

    return modelPath;
  }

  /**
   * Generate a virtual card number based on user ID
   */
  generateCardNumber(userId) {
    // Create a consistent card number based on user ID
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);

    const lastFour = Math.abs(hash % 10000).toString().padStart(4, '0');
    return `**** **** **** ${lastFour}`;
  }

  /**
   * Generate a wallet pass for a user
   */
  async generatePass(userData) {
    const { userId, userName, userEmail, balance = 0 } = userData;

    if (!userId || !userName) {
      throw new Error('Missing required user data: userId and userName are required');
    }

    try {
      // Load certificates
      const certificates = this.loadCertificates();

      // Create/get model path
      const modelPath = this.createPassModel();

      // Generate unique serial number
      const serialNumber = `dash-${userId}-${Date.now()}`;

      // Generate card number for this user
      const cardNumber = this.generateCardNumber(userId);

      // Create pass
      const pass = await PKPass.from({
        model: modelPath,
        certificates
      }, {
        serialNumber,
        description: `Dash Premium Card for ${userName}`
      });

      // Explicitly disable barcode/QR code for contactless card design
      try {
        pass.setBarcodes(null);
      } catch (e) {
        // If setBarcodes doesn't work, try removing barcode property
        delete pass.barcode;
        delete pass.barcodes;
      }

      // Update pass data using the PKPass API methods
      try {
        // Update balance using the correct API
        if (pass.primaryFields && pass.primaryFields.length > 0) {
          const balanceField = pass.primaryFields.find(f => f.key === 'balance');
          if (balanceField) {
            balanceField.value = balance;
          }
        }

        // Update card number
        if (pass.secondaryFields && pass.secondaryFields.length > 0) {
          const cardNumberField = pass.secondaryFields.find(f => f.key === 'cardNumber');
          if (cardNumberField) {
            cardNumberField.value = cardNumber;
          }
        }

        // Update cardholder name
        if (pass.auxiliaryFields && pass.auxiliaryFields.length > 0) {
          const nameField = pass.auxiliaryFields.find(f => f.key === 'name');
          if (nameField) {
            nameField.value = userName.toUpperCase();
          }
        }

        // Update back fields
        if (pass.backFields && pass.backFields.length > 0) {
          const emailField = pass.backFields.find(f => f.key === 'email');
          if (emailField && userEmail) {
            emailField.value = userEmail;
          }

          const userIdField = pass.backFields.find(f => f.key === 'userId');
          if (userIdField) {
            userIdField.value = userId;
          }
        }
      } catch (fieldError) {
        console.warn('Warning: Could not update some fields:', fieldError.message);
        // Continue with pass generation using template values
      }

      // Generate pass buffer
      const passBuffer = pass.getAsBuffer();

      // Save pass file for reference
      const passFileName = `${serialNumber}.pkpass`;
      const passFilePath = path.join(this.passesDir, passFileName);
      fs.writeFileSync(passFilePath, passBuffer);

      return {
        buffer: passBuffer,
        fileName: passFileName,
        serialNumber,
        filePath: passFilePath
      };

    } catch (error) {
      console.error('Error generating pass:', error);
      throw new Error(`Failed to generate pass: ${error.message}`);
    }
  }

  /**
   * Update an existing pass
   */
  async updatePass(serialNumber, updateData) {
    // This would be used for pass updates
    // Implementation depends on your update requirements
    throw new Error('Pass updates not yet implemented');
  }

  /**
   * Get pass information
   */
  getPassInfo(serialNumber) {
    const passFilePath = path.join(this.passesDir, `${serialNumber}.pkpass`);
    
    if (!fs.existsSync(passFilePath)) {
      throw new Error('Pass not found');
    }

    const stats = fs.statSync(passFilePath);
    
    return {
      serialNumber,
      filePath: passFilePath,
      created: stats.birthtime,
      modified: stats.mtime,
      size: stats.size
    };
  }

  /**
   * List all generated passes
   */
  listPasses() {
    const passFiles = fs.readdirSync(this.passesDir)
      .filter(file => file.endsWith('.pkpass'))
      .map(file => {
        const filePath = path.join(this.passesDir, file);
        const stats = fs.statSync(filePath);
        const serialNumber = file.replace('.pkpass', '');
        
        return {
          serialNumber,
          fileName: file,
          created: stats.birthtime,
          modified: stats.mtime,
          size: stats.size
        };
      });

    return passFiles;
  }
}

export default PassGeneratorService;
