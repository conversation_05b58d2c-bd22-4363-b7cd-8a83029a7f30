#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import axios from 'axios';
import dotenv from 'dotenv';

// Initialize environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BASE_URL = `http://localhost:${process.env.PORT || 3001}`;

async function runTests() {
  console.log('🧪 Running Wallet Pass Server Tests');
  console.log('==================================\n');

  try {
    // Test 1: Check server status
    console.log('Test 1: Checking server status...');
    const statusResponse = await axios.get(`${BASE_URL}/status`);
    console.log('✅ Server status:', statusResponse.data.status);
    console.log('Certificate status:', statusResponse.data.certificates);
    console.log('Pass count:', statusResponse.data.passes?.count || 0);
    console.log();

    // Test 2: Check certificate availability
    console.log('Test 2: Checking certificates...');
    const certsDir = path.join(__dirname, '.certificates');
    const requiredCerts = ['wwdr.pem', 'signerCert.pem', 'signerKey.pem'];
    const missingCerts = requiredCerts.filter(cert => !fs.existsSync(path.join(certsDir, cert)));

    if (missingCerts.length > 0) {
      console.log('❌ Missing certificates:', missingCerts);
      console.log('Please add these certificate files to the .certificates/ directory');
      console.log('See README.md for certificate setup instructions');
    } else {
      console.log('✅ All certificates found');
    }
    console.log();

    // Test 3: Check pass model
    console.log('Test 3: Checking pass model...');
    const modelPath = path.join(__dirname, 'models', 'DashCard.pass', 'pass.json');
    if (fs.existsSync(modelPath)) {
      const passModel = fs.readJsonSync(modelPath);
      console.log('✅ Pass model found');
      console.log('Pass Type Identifier:', passModel.passTypeIdentifier);
      console.log('Team Identifier:', passModel.teamIdentifier);
      
      // Check if identifiers match .env
      if (passModel.passTypeIdentifier !== process.env.PASS_TYPE_IDENTIFIER) {
        console.log('⚠️  Warning: Pass Type Identifier in model does not match .env');
      }
      if (passModel.teamIdentifier !== process.env.TEAM_IDENTIFIER) {
        console.log('⚠️  Warning: Team Identifier in model does not match .env');
      }
    } else {
      console.log('❌ Pass model not found');
    }
    console.log();

    // Test 4: Generate test pass
    console.log('Test 4: Generating test pass...');
    try {
      const testUser = {
        userId: 'test-user-' + Date.now(),
        userName: 'Test User',
        userEmail: '<EMAIL>',
        balance: 123.45
      };
      
      const generateResponse = await axios.post(
        `${BASE_URL}/generate-pass`,
        testUser,
        { responseType: 'arraybuffer' }
      );
      
      const passBuffer = Buffer.from(generateResponse.data);
      const testPassPath = path.join(__dirname, 'test-pass.pkpass');
      fs.writeFileSync(testPassPath, passBuffer);
      
      console.log('✅ Test pass generated successfully');
      console.log('Pass saved to:', testPassPath);
      console.log('Pass size:', passBuffer.length, 'bytes');
    } catch (error) {
      console.log('❌ Failed to generate test pass');
      if (error.response) {
        console.log('Status:', error.response.status);
        console.log('Error:', error.response.data.toString());
      } else {
        console.log('Error:', error.message);
      }
    }
    console.log();

    // Test 5: Check web service endpoints
    console.log('Test 5: Checking web service endpoints...');
    const webServiceUrl = process.env.WEB_SERVICE_URL;
    if (!webServiceUrl) {
      console.log('❌ Web Service URL not configured in .env');
    } else {
      console.log('✅ Web Service URL configured:', webServiceUrl);
      
      // Check if authentication token is set
      if (!process.env.AUTHENTICATION_TOKEN) {
        console.log('❌ Authentication token not configured in .env');
      } else {
        console.log('✅ Authentication token configured');
      }
    }
    console.log();

    // Summary
    console.log('Test Summary');
    console.log('===========');
    console.log('Server is running at:', BASE_URL);
    console.log('Web Service URL:', process.env.WEB_SERVICE_URL || 'Not configured');
    console.log('Pass Type Identifier:', process.env.PASS_TYPE_IDENTIFIER || 'Not configured');
    console.log('Team Identifier:', process.env.TEAM_IDENTIFIER || 'Not configured');
    
    console.log('\nNext Steps:');
    console.log('1. If all tests passed, your server is ready for use');
    console.log('2. Open the iOS app and navigate to Settings');
    console.log('3. Use the "Add to Wallet" button to add a pass');
    console.log('4. Check that the pass appears in Apple Wallet');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\nThe server appears to be offline. Start it with:');
      console.log('npm start');
    }
  }
}

// Run tests
runTests();
