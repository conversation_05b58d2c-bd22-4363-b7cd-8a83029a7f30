#!/usr/bin/env node

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import readline from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setup() {
  console.log('🍎 Dash Wallet Pass Server Setup');
  console.log('================================\n');

  // Check if .env exists
  const envPath = path.join(__dirname, '.env');
  const envExamplePath = path.join(__dirname, '.env.example');

  if (!fs.existsSync(envPath)) {
    console.log('Creating .env file from template...');
    fs.copyFileSync(envExamplePath, envPath);
  }

  // Read current .env
  let envContent = fs.readFileSync(envPath, 'utf8');

  console.log('Please provide the following configuration:\n');

  // Get Pass Type Identifier
  const passTypeId = await question('Pass Type Identifier (e.g., pass.com.dashfinanceapp.card): ');
  if (passTypeId.trim()) {
    envContent = envContent.replace(/PASS_TYPE_IDENTIFIER=.*/, `PASS_TYPE_IDENTIFIER=${passTypeId.trim()}`);
  }

  // Get Team Identifier
  const teamId = await question('Apple Developer Team Identifier: ');
  if (teamId.trim()) {
    envContent = envContent.replace(/TEAM_IDENTIFIER=.*/, `TEAM_IDENTIFIER=${teamId.trim()}`);
  }

  // Get Signer Key Passphrase
  const passphrase = await question('Certificate Passphrase (leave empty if none): ');
  if (passphrase.trim()) {
    envContent = envContent.replace(/SIGNER_KEY_PASSPHRASE=.*/, `SIGNER_KEY_PASSPHRASE=${passphrase.trim()}`);
  }

  // Get Web Service URL
  const webServiceUrl = await question('Web Service URL (http://localhost:3001/v1/): ') || 'http://localhost:3001/v1/';
  envContent = envContent.replace(/WEB_SERVICE_URL=.*/, `WEB_SERVICE_URL=${webServiceUrl}`);

  // Generate authentication token
  const authToken = generateAuthToken();
  envContent = envContent.replace(/AUTHENTICATION_TOKEN=.*/, `AUTHENTICATION_TOKEN=${authToken}`);

  // Write updated .env
  fs.writeFileSync(envPath, envContent);

  console.log('\n✅ Configuration saved to .env file');
  console.log(`🔑 Generated authentication token: ${authToken}`);

  // Check for certificates
  const certsDir = path.join(__dirname, '.certificates');
  fs.ensureDirSync(certsDir);

  const requiredCerts = ['wwdr.pem', 'signerCert.pem', 'signerKey.pem'];
  const missingCerts = requiredCerts.filter(cert => !fs.existsSync(path.join(certsDir, cert)));

  if (missingCerts.length > 0) {
    console.log('\n⚠️  Missing certificates:');
    missingCerts.forEach(cert => console.log(`   - ${cert}`));
    console.log('\nPlease add these certificate files to the .certificates/ directory');
    console.log('See README.md for certificate setup instructions');
  } else {
    console.log('\n✅ All certificates found');
  }

  // Create basic pass model
  const modelDir = path.join(__dirname, 'models', 'DashCard.pass');
  fs.ensureDirSync(modelDir);

  const passJsonPath = path.join(modelDir, 'pass.json');
  if (!fs.existsSync(passJsonPath)) {
    const passJson = {
      formatVersion: 1,
      passTypeIdentifier: passTypeId.trim() || 'pass.com.dashfinanceapp.card',
      teamIdentifier: teamId.trim() || 'YOUR_TEAM_ID',
      organizationName: 'Dash Finance',
      description: 'Dash Card',
      backgroundColor: 'rgb(255, 255, 255)',
      foregroundColor: 'rgb(0, 0, 0)',
      labelColor: 'rgb(0, 122, 255)',
      logoText: 'Dash',
      webServiceURL: webServiceUrl,
      authenticationToken: authToken,
      generic: {
        primaryFields: [
          {
            key: 'balance',
            label: 'BALANCE',
            value: 0,
            currencyCode: 'AUD'
          }
        ],
        secondaryFields: [
          {
            key: 'name',
            label: 'NAME',
            value: 'User Name'
          }
        ],
        auxiliaryFields: [
          {
            key: 'userId',
            label: 'USER ID',
            value: 'user-id'
          }
        ],
        backFields: [
          {
            key: 'email',
            label: 'EMAIL',
            value: '<EMAIL>'
          },
          {
            key: 'info',
            label: 'ABOUT',
            value: 'This is your Dash Finance card. Use it to access your account and track your balance.'
          }
        ]
      }
    };

    fs.writeJsonSync(passJsonPath, passJson, { spaces: 2 });
    console.log('✅ Created pass model template');
  }

  console.log('\n🚀 Setup complete!');
  console.log('\nNext steps:');
  console.log('1. Add your certificates to .certificates/ directory');
  console.log('2. Run: npm start');
  console.log('3. Test pass generation at http://localhost:3001');

  rl.close();
}

function generateAuthToken() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Run setup
setup().catch(console.error);
