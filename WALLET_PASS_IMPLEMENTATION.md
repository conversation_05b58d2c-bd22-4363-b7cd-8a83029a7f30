# Apple Wallet Pass Implementation for Dash App

This document outlines the complete implementation of Apple Wallet pass functionality for the Dash Finance app.

## Overview

The implementation consists of:
1. **Local Pass Server** - Node.js server for generating and serving wallet passes
2. **iOS PassKit Integration** - Swift code for wallet pass management
3. **Settings UI** - Updated settings view with wallet functionality
4. **Pass Update Service** - Web service for automatic pass updates

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   iOS App       │    │  Local Server    │    │  Apple Wallet   │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ Settings    │ │───▶│ │ Pass         │ │───▶│ │ User's      │ │
│ │ View        │ │    │ │ Generator    │ │    │ │ Wallet      │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │                 │
│ │ Wallet      │ │◀───│ │ Update       │ │    │                 │
│ │ Service     │ │    │ │ Service      │ │    │                 │
│ └─────────────┘ │    │ └──────────────┘ │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Setup Instructions

### 1. Apple Developer Setup

1. **Create Pass Type ID**:
   - Go to Apple Developer Console
   - Navigate to Certificates, Identifiers & Profiles
   - Create new Pass Type ID (e.g., `pass.com.dashfinanceapp.card`)

2. **Generate Certificates**:
   - Create Pass Certificate for your Pass Type ID
   - Download Apple WWDR Certificate
   - Convert certificates to PEM format

3. **Certificate Conversion**:
   ```bash
   # Convert pass certificate
   openssl pkcs12 -in pass.p12 -out signerCert.pem -clcerts -nokeys
   openssl pkcs12 -in pass.p12 -out signerKey.pem -nocerts -nodes
   
   # Convert WWDR certificate
   openssl x509 -inform DER -in AppleWWDRCA.cer -out wwdr.pem
   ```

### 2. Server Setup

1. **Install Dependencies**:
   ```bash
   cd wallet-pass-server
   npm install
   ```

2. **Run Setup Script**:
   ```bash
   npm run setup
   ```

3. **Add Certificates**:
   - Place certificate files in `.certificates/` directory:
     - `wwdr.pem`
     - `signerCert.pem`
     - `signerKey.pem`

4. **Start Server**:
   ```bash
   npm start
   ```

### 3. iOS App Integration

The iOS integration is already implemented with:
- `WalletPassService.swift` - Main service for wallet operations
- `AddToWalletButton.swift` - UI component for adding passes
- Updated `SettingsView.swift` - Settings integration

## Features

### Current Implementation

✅ **Pass Generation**
- Generate wallet passes with user data
- Custom pass design with balance and user info
- QR code integration

✅ **iOS Integration**
- Add to Wallet button in settings
- Pass library monitoring
- Existing pass detection

✅ **Local Server**
- RESTful API for pass generation
- Certificate management
- Pass storage and retrieval

✅ **Web Service Endpoints**
- Device registration
- Pass updates
- Device unregistration

### Planned Features

🔄 **Automatic Updates**
- APNs integration for push notifications
- Real-time balance updates
- Pass synchronization

🔄 **Enhanced Security**
- Token-based authentication
- Certificate validation
- Secure pass distribution

## API Endpoints

### Pass Generation
- `POST /generate-pass` - Generate new wallet pass
- `GET /passes` - List all generated passes
- `GET /passes/:serialNumber` - Get pass information
- `GET /passes/:serialNumber/download` - Download pass file

### Apple Wallet Web Service
- `POST /v1/devices/:deviceId/registrations/:passType/:serialNumber` - Device registration
- `GET /v1/devices/:deviceId/registrations/:passType` - Get updatable passes
- `GET /v1/passes/:passType/:serialNumber` - Get latest pass version
- `DELETE /v1/devices/:deviceId/registrations/:passType/:serialNumber` - Device unregistration

### Management
- `POST /upload-certificates` - Upload signing certificates
- `GET /status` - Server status and certificate validation

## File Structure

```
wallet-pass-server/
├── .certificates/          # Certificate files (not in git)
│   ├── wwdr.pem
│   ├── signerCert.pem
│   └── signerKey.pem
├── models/                 # Pass templates
│   └── DashCard.pass/
│       └── pass.json
├── passes/                 # Generated passes
├── services/
│   └── PassGeneratorService.js
├── server.js              # Main server
├── setup.js               # Setup script
├── package.json
├── .env                   # Configuration
└── README.md

Dash/
├── Services/
│   └── WalletPassService.swift
├── Views/
│   ├── Components/
│   │   └── AddToWalletButton.swift
│   └── SettingsView.swift (updated)
```

## Configuration

### Environment Variables (.env)
```
PORT=3001
PASS_TYPE_IDENTIFIER=pass.com.dashfinanceapp.card
TEAM_IDENTIFIER=YOUR_TEAM_ID
SIGNER_KEY_PASSPHRASE=your_passphrase
WEB_SERVICE_URL=http://localhost:3001/v1/
AUTHENTICATION_TOKEN=generated_token
```

## Testing

### 1. Server Testing
```bash
# Start server
npm start

# Test pass generation
curl -X POST http://localhost:3001/generate-pass \
  -H "Content-Type: application/json" \
  -d '{"userId":"test123","userName":"Test User","balance":100.50}'
```

### 2. iOS Testing
1. Run the iOS app
2. Navigate to Settings
3. Look for "Apple Wallet" section
4. Tap "Add to Wallet" button
5. Verify pass appears in Apple Wallet

## Troubleshooting

### Common Issues

1. **Certificate Errors**
   - Verify certificates are in PEM format
   - Check certificate expiration
   - Ensure Pass Type ID matches

2. **Pass Generation Fails**
   - Check server logs
   - Verify all required fields are provided
   - Test certificate validity

3. **iOS Integration Issues**
   - Ensure PassKit framework is imported
   - Check device wallet availability
   - Verify server connectivity

### Debug Commands

```bash
# Check certificate validity
openssl x509 -in signerCert.pem -text -noout

# Test server status
curl http://localhost:3001/status

# View server logs
npm start
```

## Security Considerations

1. **Certificate Security**
   - Store certificates securely
   - Use strong passphrases
   - Rotate certificates regularly

2. **Authentication**
   - Use secure authentication tokens
   - Implement proper request validation
   - Monitor for suspicious activity

3. **Network Security**
   - Use HTTPS in production
   - Implement rate limiting
   - Validate all inputs

## Production Deployment

For production deployment:

1. **Server Hosting**
   - Deploy to secure cloud environment
   - Use HTTPS with valid SSL certificate
   - Implement proper logging and monitoring

2. **Database Integration**
   - Store device registrations
   - Track pass updates
   - Implement proper data backup

3. **APNs Integration**
   - Set up Apple Push Notification Service
   - Implement push notification logic
   - Handle notification responses

## Support

For issues or questions:
1. Check server logs
2. Review Apple's PassKit documentation
3. Test with Apple's pass validator
4. Contact development team

---

**Note**: This implementation is designed for local development and testing. Additional security and scalability measures are required for production use.
