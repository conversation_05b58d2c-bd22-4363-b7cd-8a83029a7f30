# TOTP Debug Guide

## What I've Added

I've added comprehensive debugging to help identify the "dash.totperror 1" issue:

### 1. Enhanced Error Messages
- Added `LocalizedError` extension to `TOTPError` enum
- Now shows user-friendly error messages instead of cryptic codes

### 2. Debug Logging
- Added detailed logging throughout the TOTP verification process
- Logs secret loading, base32 decoding, time calculations, and code generation

### 3. Fixed Secret Generation
- Changed from 32-byte to standard 20-byte secrets for better compatibility
- This matches what most TOTP apps expect

### 4. Test Functions
- Added `generateCurrentCode()` to see what code should be expected
- Added `testTOTPImplementation()` to self-verify the algorithm

## How to Debug

1. **Run the app** and try to log in with 2FA
2. **Check the Xcode console** for debug messages starting with "DEBUG:"
3. **Look for these key indicators:**

### If you see "Failed to load TOTP secret":
- The secret isn't stored properly in keychain
- You may need to set up 2FA again

### If you see "Failed to decode base32 secret":
- There's an issue with the secret format
- The secret may be corrupted

### If you see "No matching code found":
- Time synchronization issue
- Your authenticator app might be using a different algorithm

### If you see "Rate limit exceeded":
- Too many failed attempts
- Wait a few minutes before trying again

## Expected Debug Output

When working correctly, you should see:
```
DEBUG: TOTP verification started for user: [userId], code: [code]
DEBUG: Successfully loaded TOTP secret, length: [length]
DEBUG: verifyTOTP called with code: [code], secret length: [length]
DEBUG: Secret decoded successfully, data length: [bytes]
DEBUG: Current time counter: [timestamp]
DEBUG: Time offset 0, generated code: [expected_code]
DEBUG: Code match found at time offset 0
DEBUG: TOTP verification result: true
```

## Next Steps

1. Try logging in and share the debug output
2. If the secret is missing, you'll need to disable and re-enable 2FA
3. If codes don't match, we may need to check time synchronization

## Quick Fix Commands

If you need to reset 2FA for testing:
```swift
// In your app, you can call this to remove TOTP data
TOTPService.shared.deleteTOTPData(for: userId)
```

Then set up 2FA again with the new 20-byte secret generation.
