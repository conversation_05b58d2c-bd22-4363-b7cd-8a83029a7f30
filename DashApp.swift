import SwiftUI
import Firebase
import Stripe

@main
struct DashApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate
    @StateObject private var authViewModel = AuthViewModel.shared
    @StateObject private var securityViewModel = SecurityViewModel()
    @AppStorage("isDarkMode") private var isDarkMode = false
    @Environment(\.scenePhase) private var scenePhase
    
    // This state controls the presentation of the lock screen.
    @State private var isLocked = false

    init() {
        FirebaseApp.configure()
        // It's recommended to fetch this key from a secure location rather than hardcoding.
        StripeAPI.defaultPublishableKey = "pk_test_51PGBVDRv9Vn8sHh8g9g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8h8g8"
    }

    var body: some Scene {
        WindowGroup {
            ZStack {
                // The main view hierarchy is determined here.
                if authViewModel.userSession == nil {
                    WelcomeView()
                        .environmentObject(authViewModel)
                } else {
                    // User is logged in, decide between main dashboard and PIN setup.
                    if securityViewModel.isPinSet {
                        MainTabView()
                            .environmentObject(authViewModel)
                            .environmentObject(securityViewModel)
                    } else {
                        PINSetupView()
                            .environmentObject(securityViewModel)
                    }
                }
            }
            .environment(\.colorScheme, isDarkMode ? .dark : .light)
            .onAppear {
                // Initial data fetch when the app starts.
                authViewModel.fetchUser()
                securityViewModel.checkPinExists()
                
                // If a PIN is set, lock the app on first launch.
                if securityViewModel.isPinSet {
                    isLocked = true
                }
            }
            .onChange(of: scenePhase) { oldPhase, newPhase in
                if newPhase == .active {
                    // When the app becomes active, lock it if a PIN is set and the user is logged in.
                    if securityViewModel.isPinSet && authViewModel.userSession != nil {
                        // Only lock if not already successfully authenticated in this session
                        if !securityViewModel.isUnlocked {
                            isLocked = true
                        }
                    }
                } else if newPhase == .background || newPhase == .inactive {
                    // When the app goes to the background, reset the unlocked state.
                    if authViewModel.userSession != nil {
                        securityViewModel.isUnlocked = false
                    }
                }
            }
            .onChange(of: securityViewModel.isUnlocked) { isUnlocked in
                // When authentication is successful, dismiss the lock screen.
                if isUnlocked {
                    isLocked = false
                }
            }
            .fullScreenCover(isPresented: $isLocked) {
                // The LockScreenView is presented as a modal sheet.
                LockScreenView()
                    .environmentObject(securityViewModel)
            }
        }
    }
} 
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        FirebaseApp.configure()
        return true
    }
}