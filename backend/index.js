require('dotenv').config();
const express = require('express');
const cors = require('cors');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { Airwallex } = require('@airwallex/node-sdk');

const airwallex = new Airwallex({
    apiKey: process.env.AIRWALLEX_API_KEY,
    clientId: process.env.AIRWALLEX_CLIENT_ID,
    env: 'demo' // Use 'prod' for production
});

const app = express();
app.use(cors());
app.use(express.json());

const PORT = process.env.PORT || 3000;

app.get('/', (req, res) => {
    res.send('Dash backend is running!');
});

// Stripe Payment Intent Endpoint
app.post('/create-payment-intent', async (req, res) => {
    const { amount, currency } = req.body;

    try {
        const paymentIntent = await stripe.paymentIntents.create({
            amount,
            currency,
            payment_method_types: ['card'],
        });

        res.status(200).send({
            clientSecret: paymentIntent.client_secret,
        });
    } catch (error) {
        res.status(500).send({ error: error.message });
    }
});

// Airwallex Create Cardholder Endpoint
app.post('/create-cardholder', async (req, res) => {
    try {
        const cardholder = await airwallex.issuing.createCardholder({
            // In a real app, you'd get this from the user's profile
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            phone_number: '13000000000',
            address: {
                street: '180 Lonsdale Street',
                city: 'Melbourne',
                state: 'VIC',
                country_code: 'AU',
                postcode: '3000',
            },
        });
        res.status(200).send(cardholder);
    } catch (error) {
        console.error('Airwallex Error:', error);
        res.status(500).send({ error: error.message });
    }
});

// Airwallex Issue Virtual Card Endpoint
app.post('/issue-virtual-card', async (req, res) => {
    const { cardholder_id } = req.body;

    if (!cardholder_id) {
        return res.status(400).send({ error: 'Cardholder ID is required.' });
    }

    try {
        const card = await airwallex.issuing.issueCard({
            cardholder_id: cardholder_id,
            card_type: 'virtual',
            currency: 'AUD',
            spending_limits: [
                {
                    amount: 1000,
                    interval: 'per_transaction',
                },
            ],
        });
        res.status(200).send(card);
    } catch (error) {
        console.error('Airwallex Error:', error);
        res.status(500).send({ error: error.message });
    }
});

// TODO: Add endpoint to issue virtual card to a cardholder

app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
}); 