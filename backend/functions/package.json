{"name": "dash-functions", "description": "Cloud Functions for Dash app", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}