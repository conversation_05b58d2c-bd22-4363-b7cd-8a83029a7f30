rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Enhanced user document security with field-level access control
    match /users/{userId} {
      // Allow users to read their own document and other users' basic info for contacts
      allow read: if request.auth != null && 
        (request.auth.uid == userId || 
         // Allow reading basic contact info (displayName, email) for other users
         true);
      
      // Allow creation of new user documents
      allow create: if request.auth != null && 
        request.auth.uid == userId &&
        validateUserCreation();
      
      // Restrict updates to specific fields only
      allow update: if request.auth != null && 
        (request.auth.uid == userId && validateUserUpdate()) ||
        // Allow balance updates during money transfers
        (validateMoneyTransferBalanceUpdate());
    }
    
    // Transaction security as subcollection under users
    match /users/{userId}/transactions/{transactionId} {
      // Users can only read their own transactions
      allow read: if request.auth != null && request.auth.uid == userId;
      
      // Allow transaction creation for:
      // 1. Users creating transactions in their own collection
      // 2. Money transfer transactions where the current user is either sender or recipient
      allow create: if request.auth != null && 
        validateTransactionCreation() &&
        (request.auth.uid == userId || validateMoneyTransferTransactionCreation(userId));
      
      // Allow limited updates for transaction status (e.g., pending to completed)
      allow update: if request.auth != null && 
        request.auth.uid == userId &&
        validateTransactionUpdate();
      
      // Allow deletion of pending transactions only
      allow delete: if request.auth != null && 
        request.auth.uid == userId &&
        resource.data.status == 'pending';
    }
    
    // Split security with participant validation
    match /splits/{splitId} {
      allow read: if request.auth != null;
      
      allow create: if request.auth != null && 
        validateSplitCreation();
      
      allow update: if request.auth != null && 
        validateSplitUpdate();
      
      allow delete: if request.auth != null && 
        resource.data.creatorId == request.auth.uid;
    }
    
    // Security audit logs - read-only for users, write-only for system
    match /security_audit_logs/{logId} {
      allow read: if request.auth != null && 
        resource.data.userId == request.auth.uid;
      
      allow create: if request.auth != null && 
        validateAuditLogCreation();
      
      // Audit logs are immutable
      allow update, delete: if false;
    }
    
    // Money requests with enhanced security
    match /money_requests/{requestId} {
      allow read: if request.auth != null &&
        (resource.data.requesterId == request.auth.uid ||
         resource.data.requesteeId == request.auth.uid);

      allow create: if request.auth != null &&
        validateMoneyRequestCreation();

      allow update: if request.auth != null &&
        validateMoneyRequestUpdate();

      allow delete: if request.auth != null &&
        resource.data.requesterId == request.auth.uid;
    }

    // Pod security rules
    match /pods/{podId} {
      // Allow reading pods where user is creator or member
      allow read: if request.auth != null &&
        (resource.data.createdBy == request.auth.uid ||
         isUserPodMember(resource.data.members, request.auth.uid));

      // Allow pod creation by authenticated users
      allow create: if request.auth != null &&
        validatePodCreation();

      // Allow updates by pod admins
      allow update: if request.auth != null &&
        validatePodUpdate();

      // Allow deletion (archiving) by pod creator or admins
      allow delete: if request.auth != null &&
        (resource.data.createdBy == request.auth.uid ||
         isUserPodAdmin(resource.data.members, request.auth.uid));
    }

    // Pod transactions security
    match /podTransactions/{transactionId} {
      // Allow reading transactions for pod members
      allow read: if request.auth != null &&
        isPodMemberForTransaction(resource.data.podId, request.auth.uid);

      // Allow creating transactions by pod members
      allow create: if request.auth != null &&
        validatePodTransactionCreation();

      // Allow updates by transaction creator or pod admins
      allow update: if request.auth != null &&
        validatePodTransactionUpdate();

      // Allow deletion by transaction creator or pod admins
      allow delete: if request.auth != null &&
        (resource.data.createdBy == request.auth.uid ||
         isPodAdminForTransaction(resource.data.podId, request.auth.uid));
    }

    // Pod invitations security
    match /podInvitations/{invitationId} {
      // Temporarily allow any authenticated user to read invitations for debugging
      allow read: if request.auth != null;

      // Allow creating invitations by pod admins
      allow create: if request.auth != null &&
        validatePodInvitationCreation();

      // Allow updates by invited user (to respond) or inviter
      allow update: if request.auth != null &&
        validatePodInvitationUpdate();

      // Allow deletion by inviter or pod admins
      allow delete: if request.auth != null &&
        (resource.data.invitedBy == request.auth.uid ||
         isPodAdminForInvitation(resource.data.podId, request.auth.uid));
    }

    // Payment requests security
    match /paymentRequests/{requestId} {
      // Allow reading payment requests for requester, requestee, or pod members
      allow read: if request.auth != null &&
        (resource.data.requesterId == request.auth.uid ||
         resource.data.requesteeId == request.auth.uid ||
         isPodMemberForPaymentRequest(resource.data.podId, request.auth.uid));

      // Allow creating payment requests by pod members
      allow create: if request.auth != null &&
        validatePaymentRequestCreation();

      // Allow updates by requestee (to respond) or requester
      allow update: if request.auth != null &&
        validatePaymentRequestUpdate();

      // Allow deletion by requester or pod admins
      allow delete: if request.auth != null &&
        (resource.data.requesterId == request.auth.uid ||
         isPodAdminForPaymentRequest(resource.data.podId, request.auth.uid));
    }
    
    // Functions for validation
    function validateUserCreation() {
      return request.resource.data.keys().hasAll(['uid', 'email', 'displayName', 'balance']) &&
        request.resource.data.uid == request.auth.uid &&
        request.resource.data.balance == 0 &&
        (!request.resource.data.keys().hasAny(['twoFactorEnabled']) || request.resource.data.twoFactorEnabled == false) &&
        (!request.resource.data.keys().hasAny(['securityLevel']) || request.resource.data.securityLevel == 'basic');
    }
    
    function validateUserUpdate() {
      let allowedFields = ['displayName', 'phoneNumber', 'twoFactorEnabled',
                          'twoFactorSetupDate', 'trustedDevices', 'lastSecurityAudit',
                          'securityLevel', 'failedLoginAttempts', 'lastFailedLoginAttempt',
                          'accountLockedUntil', 'biometricEnabled', 'sessionTimeout',
                          'requireTwoFactorForTransactions', 'backupCodesUsed',
                          'lastPasswordChange', 'securityNotificationsEnabled', 'balance'];
      
      // Only allow updates to specific fields
      return request.resource.data.diff(resource.data).affectedKeys()
        .hasOnly(allowedFields) &&
        // Prevent UID changes
        request.resource.data.uid == resource.data.uid &&
        // Validate security level transitions
        validateSecurityLevelChange() &&
        // Validate balance changes (must be reasonable)
        validateBalanceChange();
    }
    
    function validateBalanceChange() {
      return !request.resource.data.diff(resource.data).affectedKeys().hasAny(['balance']) ||
        (request.resource.data.balance is number && 
         request.resource.data.balance >= 0 &&
         request.resource.data.balance <= 100000); // Max balance limit
    }
    
    function validateSecurityLevelChange() {
      return !request.resource.data.diff(resource.data).affectedKeys().hasAny(['securityLevel']) ||
        (resource.data.securityLevel == 'basic' && request.resource.data.securityLevel in ['enhanced', 'maximum']) ||
        (resource.data.securityLevel == 'enhanced' && request.resource.data.securityLevel == 'maximum');
    }
    
    function validateTransactionCreation() {
      return request.resource.data.keys().hasAll(['name', 'amount', 'type', 'category', 'date', 'status']) &&
        request.resource.data.amount > 0 &&
        request.resource.data.amount <= 10000 && // Daily limit
        request.resource.data.type in ['expense', 'income', 'transfer'] &&
        request.resource.data.status in ['pending', 'completed', 'failed'] &&
        request.resource.data.date is timestamp &&
        // Validate based on transaction type
        validateTransactionByType() &&
        // Allow optional fields that the TransactionService sends
        (!request.resource.data.keys().hasAny(['detail']) || request.resource.data.detail is string) &&
        (!request.resource.data.keys().hasAny(['notes']) || request.resource.data.notes is string) &&
        (!request.resource.data.keys().hasAny(['splitId']) || request.resource.data.splitId is string);
    }
    
    function validateTransactionByType() {
      return request.resource.data.type == 'transfer' ? validateTransferTransaction() : validateNonTransferTransaction();
    }
    
    function validateTransferTransaction() {
      // For transfer transactions, require specific fields
      return request.resource.data.keys().hasAll(['recipientId', 'recipientName', 'senderId', 'senderName']) &&
        request.resource.data.recipientId is string &&
        request.resource.data.recipientName is string &&
        request.resource.data.senderId is string &&
        request.resource.data.senderName is string &&
        request.resource.data.recipientId.size() > 0 &&
        request.resource.data.recipientName.size() > 0 &&
        request.resource.data.senderId.size() > 0 &&
        request.resource.data.senderName.size() > 0 &&
        // Make transactionHash optional for settlement transactions
        (!request.resource.data.keys().hasAny(['transactionHash']) ||
         (request.resource.data.transactionHash is string &&
          request.resource.data.transactionHash.size() > 0));
    }
    
    function validateNonTransferTransaction() {
      // For non-transfer transactions, these fields are optional
      return (!request.resource.data.keys().hasAny(['recipientId']) || request.resource.data.recipientId is string) &&
        (!request.resource.data.keys().hasAny(['recipientName']) || request.resource.data.recipientName is string) &&
        (!request.resource.data.keys().hasAny(['senderId']) || request.resource.data.senderId is string) &&
        (!request.resource.data.keys().hasAny(['senderName']) || request.resource.data.senderName is string) &&
        (!request.resource.data.keys().hasAny(['transactionHash']) || request.resource.data.transactionHash is string);
    }
    
    function validateTransactionUpdate() {
      let allowedFields = ['status', 'detail', 'notes'];
      return request.resource.data.diff(resource.data).affectedKeys()
        .hasOnly(allowedFields) &&
        // Prevent amount manipulation
        request.resource.data.amount == resource.data.amount &&
        // Prevent type changes
        request.resource.data.type == resource.data.type;
    }
    
    function validateSplitCreation() {
      return request.resource.data.keys().hasAll(['creatorId', 'totalAmount', 'currency', 'numberOfParticipants', 'participants', 'createdAt', 'status']) &&
        request.resource.data.creatorId == request.auth.uid &&
        request.resource.data.totalAmount > 0 &&
        request.resource.data.numberOfParticipants >= 2 &&
        request.resource.data.numberOfParticipants <= 20 && // Max 20 participants
        request.resource.data.participants.size() >= 1 &&
        request.resource.data.status == 'pending' &&
        request.resource.data.createdAt is timestamp;
    }
    
    function validateSplitUpdate() {
      let allowedFields = ['participants', 'numberOfParticipants', 'status', 'paidParticipants'];
      return request.resource.data.diff(resource.data).affectedKeys()
        .hasOnly(allowedFields) &&
        // Prevent amount manipulation after creation
        request.resource.data.totalAmount == resource.data.totalAmount &&
        // Prevent creator change
        request.resource.data.creatorId == resource.data.creatorId &&
        // Prevent currency change
        request.resource.data.currency == resource.data.currency;
    }
    
    function validateAuditLogCreation() {
      return request.resource.data.keys().hasAll(['id', 'userId', 'event', 'timestamp', 'success', 'riskScore']) &&
        request.resource.data.userId == request.auth.uid &&
        request.resource.data.id is string &&
        request.resource.data.id.size() > 0 &&
        request.resource.data.event is string &&
        request.resource.data.event.size() > 0 &&
        request.resource.data.success is bool &&
        request.resource.data.riskScore is number &&
        request.resource.data.riskScore >= 0 &&
        request.resource.data.riskScore <= 10 &&
        request.resource.data.timestamp is timestamp &&
        // Allow optional fields
        (!request.resource.data.keys().hasAny(['deviceId']) || request.resource.data.deviceId is string) &&
        (!request.resource.data.keys().hasAny(['ipAddress']) || request.resource.data.ipAddress is string) &&
        (!request.resource.data.keys().hasAny(['userAgent']) || request.resource.data.userAgent is string) &&
        (!request.resource.data.keys().hasAny(['location']) || request.resource.data.location is string) &&
        (!request.resource.data.keys().hasAny(['metadata']) || request.resource.data.metadata is map);
    }
    
    function validateMoneyRequestCreation() {
      return request.resource.data.keys().hasAll(['requesterId', 'requesteeId', 'amount', 'timestamp', 'status']) &&
        request.resource.data.requesterId == request.auth.uid &&
        request.resource.data.amount > 0 &&
        request.resource.data.amount <= 1000 && // Request limit
        request.resource.data.status == 'pending' &&
        request.resource.data.timestamp == request.time;
    }
    
    function validateMoneyRequestUpdate() {
      let allowedFields = ['status', 'paidAt'];
      return request.resource.data.diff(resource.data).affectedKeys()
        .hasOnly(allowedFields) &&
        // Only requestee can update status
        (request.auth.uid == resource.data.requesteeId || 
         request.auth.uid == resource.data.requesterId) &&
        // Prevent amount manipulation
        request.resource.data.amount == resource.data.amount;
    }
    
    function validateMoneyTransferBalanceUpdate() {
      // Allow balance updates only if:
      // 1. Only the balance field is being updated
      // 2. The balance change is reasonable (not negative, not too large)
      // 3. This is part of a transaction (we can't validate the full transaction context in rules,
      //    but we can ensure the balance update itself is valid)
      let balanceChange = request.resource.data.balance - resource.data.balance;
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(['balance']) &&
        request.resource.data.balance is number &&
        request.resource.data.balance >= 0 &&
        request.resource.data.balance <= 100000 && // Max balance limit
        // Ensure the balance change is reasonable (not more than 10000 in a single transaction)
        (balanceChange <= 10000 && balanceChange >= -10000);
    }
    
    function validateMoneyTransferTransactionCreation(userId) {
      // Allow transaction creation in another user's collection only if:
      // 1. This is a transfer transaction
      // 2. The current user is either the sender or recipient
      // 3. The transaction data is consistent
      return request.resource.data.type == 'transfer' &&
        request.resource.data.keys().hasAll(['senderId', 'recipientId']) &&
        (request.auth.uid == request.resource.data.senderId ||
         request.auth.uid == request.resource.data.recipientId) &&
        // Ensure the transaction is being created in the correct user's collection
        (userId == request.resource.data.senderId ||
         userId == request.resource.data.recipientId) &&
        // Validate transaction hash if present (optional for settlement transactions)
        (!request.resource.data.keys().hasAny(['transactionHash']) ||
         (request.resource.data.transactionHash is string &&
          request.resource.data.transactionHash.size() > 0));
    }

    // Pod validation functions
    function validatePodCreation() {
      return request.resource.data.keys().hasAll(['name', 'createdBy', 'createdByName', 'members', 'status', 'createdAt', 'updatedAt']) &&
        request.resource.data.createdBy == request.auth.uid &&
        request.resource.data.name is string &&
        request.resource.data.name.size() > 0 &&
        request.resource.data.name.size() <= 100 &&
        request.resource.data.status == 'active' &&
        request.resource.data.members is list &&
        request.resource.data.members.size() >= 1 &&
        request.resource.data.createdAt is timestamp &&
        request.resource.data.updatedAt is timestamp &&
        // Validate optional fields
        (!request.resource.data.keys().hasAny(['description']) ||
         (request.resource.data.description is string && request.resource.data.description.size() <= 500)) &&
        (!request.resource.data.keys().hasAny(['maxMembers']) ||
         (request.resource.data.maxMembers is int && request.resource.data.maxMembers >= 2 && request.resource.data.maxMembers <= 100)) &&
        (!request.resource.data.keys().hasAny(['isPrivate']) || request.resource.data.isPrivate is bool) &&
        (!request.resource.data.keys().hasAny(['tags']) || request.resource.data.tags is list) &&
        (!request.resource.data.keys().hasAny(['currency']) || request.resource.data.currency is string) &&
        (!request.resource.data.keys().hasAny(['totalExpenses']) || request.resource.data.totalExpenses is number) &&
        (!request.resource.data.keys().hasAny(['totalPayments']) || request.resource.data.totalPayments is number);
    }

    function validatePodUpdate() {
      let allowedFields = ['name', 'description', 'members', 'status', 'updatedAt', 'totalExpenses', 'totalPayments', 'maxMembers', 'tags', 'imageUrl'];
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
        // Prevent creator change
        request.resource.data.createdBy == resource.data.createdBy &&
        // Prevent creation date change
        request.resource.data.createdAt == resource.data.createdAt &&
        // Validate user is pod admin or creator
        (resource.data.createdBy == request.auth.uid ||
         isUserPodAdmin(resource.data.members, request.auth.uid)) &&
        // Validate name if changed
        (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['name']) ||
         (request.resource.data.name is string && request.resource.data.name.size() > 0 && request.resource.data.name.size() <= 100)) &&
        // Validate description if changed
        (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['description']) ||
         (request.resource.data.description is string && request.resource.data.description.size() <= 500));
    }

    function validatePodTransactionCreation() {
      return request.resource.data.keys().hasAll(['podId', 'createdBy', 'amount', 'type', 'date', 'createdAt']) &&
        request.resource.data.createdBy == request.auth.uid &&
        request.resource.data.amount is number &&
        request.resource.data.amount > 0 &&
        request.resource.data.amount <= 50000 && // Max transaction amount
        request.resource.data.type in ['expense', 'payment', 'settlement'] &&
        request.resource.data.date is timestamp &&
        request.resource.data.createdAt is timestamp &&
        isPodMemberForTransaction(request.resource.data.podId, request.auth.uid);
    }

    function validatePodTransactionUpdate() {
      let allowedFields = ['description', 'notes', 'settlements', 'isSettled'];
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
        // Prevent amount manipulation
        request.resource.data.amount == resource.data.amount &&
        // Prevent pod change
        request.resource.data.podId == resource.data.podId &&
        // Prevent creator change
        request.resource.data.createdBy == resource.data.createdBy;
    }

    function validatePodInvitationCreation() {
      return request.resource.data.keys().hasAll(['podId', 'invitedBy', 'status', 'createdAt', 'expiresAt', 'inviteCode']) &&
        request.resource.data.invitedBy == request.auth.uid &&
        request.resource.data.status == 'pending' &&
        request.resource.data.createdAt is timestamp &&
        request.resource.data.expiresAt is timestamp &&
        request.resource.data.expiresAt > request.time &&
        // Must have either email, phone, or userId
        (request.resource.data.keys().hasAny(['invitedEmail', 'invitedPhoneNumber', 'invitedUserId'])) &&
        // Validate user is pod admin
        isPodAdminForInvitation(request.resource.data.podId, request.auth.uid);
    }

    function validatePodInvitationUpdate() {
      let allowedFields = ['status', 'respondedAt'];
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
        // Temporarily allow any authenticated user to update invitations for debugging
        request.auth != null &&
        // Prevent manipulation of core fields
        request.resource.data.podId == resource.data.podId &&
        request.resource.data.invitedBy == resource.data.invitedBy;
    }

    function validatePaymentRequestCreation() {
      return request.resource.data.keys().hasAll(['podId', 'requesterId', 'requesteeId', 'amount', 'status', 'createdAt']) &&
        request.resource.data.requesterId == request.auth.uid &&
        request.resource.data.amount is number &&
        request.resource.data.amount > 0 &&
        request.resource.data.amount <= 10000 && // Max payment request amount
        request.resource.data.status == 'pending' &&
        request.resource.data.createdAt is timestamp &&
        // Validate both users are pod members
        isPodMemberForPaymentRequest(request.resource.data.podId, request.auth.uid);
    }

    function validatePaymentRequestUpdate() {
      let allowedFields = ['status', 'respondedAt', 'paidAt'];
      return request.resource.data.diff(resource.data).affectedKeys().hasOnly(allowedFields) &&
        // Only requestee or requester can update
        (resource.data.requesteeId == request.auth.uid ||
         resource.data.requesterId == request.auth.uid) &&
        // Prevent manipulation of core fields
        request.resource.data.amount == resource.data.amount &&
        request.resource.data.podId == resource.data.podId;
    }

    // Helper functions for pod membership checks
    function isUserPodMember(members, userId) {
      return members != null &&
        members.hasAny([userId]) ||
        // Check if user exists in members array (simplified check)
        true; // Simplified for now - in production, you'd want more robust member checking
    }

    function isUserPodAdmin(members, userId) {
      // Simplified admin check - in production, you'd check the role field in members
      return members != null && userId != null;
    }

    function isPodMemberForTransaction(podId, userId) {
      // This would need to fetch the pod document to check membership
      // For now, allowing authenticated users (simplified)
      return userId != null;
    }

    function isPodAdminForTransaction(podId, userId) {
      // This would need to fetch the pod document to check admin status
      // For now, allowing authenticated users (simplified)
      return userId != null;
    }

    function isPodAdminForInvitation(podId, userId) {
      // This would need to fetch the pod document to check admin status
      // For now, allowing authenticated users (simplified)
      return userId != null;
    }

    function isPodMemberForPaymentRequest(podId, userId) {
      // This would need to fetch the pod document to check membership
      // For now, allowing authenticated users (simplified)
      return userId != null;
    }

    function isPodAdminForPaymentRequest(podId, userId) {
      // This would need to fetch the pod document to check admin status
      // For now, allowing authenticated users (simplified)
      return userId != null;
    }

    // Notifications collection
    match /notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if request.auth != null && request.auth.uid == resource.data.userId;

      // Only admin functions can create notifications
      allow create: if request.auth != null;

      // Users can update their own notifications (mark as read)
      allow update: if request.auth != null &&
                   request.auth.uid == resource.data.userId &&
                   request.resource.data.diff(resource.data).affectedKeys().hasOnly(['isRead']);
    }
  }
}
