# Dash Apple Wallet Pass Setup Guide

This guide will walk you through setting up the complete Apple Wallet pass functionality for the Dash app.

## Overview

The implementation creates wallet passes that look exactly like the virtual card in your app:
- **White background** with black text
- **Dash logo** and "Premium" branding
- **Available Balance** prominently displayed
- **Card number** (unique per user)
- **Expiry date** and card details
- **Contactless payment** styling

## Quick Start

### 1. Set up the Wallet Pass Server

```bash
# Navigate to the wallet pass server directory
cd wallet-pass-server

# Install dependencies
npm install

# Run the setup wizard
npm run setup

# Create image templates
node create-images.js
```

### 2. Get Apple Developer Certificates

You need these certificates from Apple Developer Console:

1. **Pass Type ID**: Create at developer.apple.com
   - Example: `pass.com.dashfinanceapp.card`

2. **Pass Certificate**: Generate for your Pass Type ID
3. **WWDR Certificate**: Download from Apple

### 3. Convert Certificates to PEM Format

```bash
# Convert your pass certificate (.p12 to .pem)
openssl pkcs12 -in YourPassCert.p12 -out signerCert.pem -clcerts -nokeys
openssl pkcs12 -in YourPassCert.p12 -out signerKey.pem -nocerts -nodes

# Convert WWDR certificate (.cer to .pem)
openssl x509 -inform DER -in AppleWWDRCA.cer -out wwdr.pem
```

### 4. Add Certificates to Server

Place these files in `wallet-pass-server/.certificates/`:
- `wwdr.pem`
- `signerCert.pem`
- `signerKey.pem`

### 5. Start the Server

```bash
npm start
```

The server will run on `http://localhost:3001`

### 6. Test the Implementation

```bash
# Run the test suite
npm test
```

## iOS App Integration

The iOS integration is already complete and includes:

### WalletPassService.swift
- Handles pass generation requests
- Manages wallet pass library
- Monitors pass updates
- Provides pass details

### AddToWalletButton.swift
- Custom SwiftUI button for adding passes
- Loading states and error handling
- Success/failure feedback

### Updated SettingsView.swift
- Apple Wallet section (only shows if wallet is available)
- Displays existing passes with balance and card number
- Add new pass functionality
- Pass management

## Pass Design

The wallet pass is designed to match your VirtualCardView exactly:

### Front of Pass
```
┌─────────────────────────────────┐
│ [D] DASH              Premium   │
│                                 │
│ AVAILABLE BALANCE               │
│ $123.45                         │
│                                 │
│ CARD NUMBER    VALID THRU       │
│ **** **** **** 1234    06/26    │
│                                 │
│ CARDHOLDER NAME                 │
│ JOHN DOE                        │
└─────────────────────────────────┘
```

### Back of Pass
- User email
- User ID
- About information
- Features list

## API Endpoints

### Pass Generation
- `POST /generate-pass` - Create new pass
- `GET /passes` - List all passes
- `GET /status` - Server status

### Apple Wallet Web Service
- `POST /v1/devices/.../registrations/...` - Device registration
- `GET /v1/devices/.../registrations/...` - Get updatable passes
- `GET /v1/passes/.../...` - Get latest pass version
- `DELETE /v1/devices/.../registrations/...` - Device unregistration

## Configuration

### Environment Variables (.env)
```
PORT=3001
PASS_TYPE_IDENTIFIER=pass.com.dashfinanceapp.card
TEAM_IDENTIFIER=YOUR_TEAM_ID
SIGNER_KEY_PASSPHRASE=your_passphrase
WEB_SERVICE_URL=http://localhost:3001/v1/
AUTHENTICATION_TOKEN=generated_token
```

## Testing Steps

### 1. Server Testing
```bash
# Check server status
curl http://localhost:3001/status

# Generate a test pass
curl -X POST http://localhost:3001/generate-pass \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test123",
    "userName": "Test User",
    "userEmail": "<EMAIL>",
    "balance": 150.50
  }' \
  --output test-pass.pkpass
```

### 2. iOS App Testing
1. Build and run the iOS app
2. Sign in with a user account
3. Navigate to Settings
4. Look for "Apple Wallet" section
5. Tap "Add to Wallet"
6. Verify pass appears in Apple Wallet

### 3. Pass Validation
- Open the generated `.pkpass` file on iOS device
- Verify all fields display correctly
- Check that balance matches user's actual balance
- Confirm card number is unique per user

## Troubleshooting

### Common Issues

1. **"Missing certificates" error**
   - Verify all 3 certificate files are in `.certificates/` directory
   - Check file permissions
   - Ensure certificates are in PEM format

2. **"Invalid pass data" error**
   - Check Pass Type Identifier matches Apple Developer Console
   - Verify Team Identifier is correct
   - Ensure certificates haven't expired

3. **Pass doesn't appear in wallet**
   - Check device has Apple Wallet enabled
   - Verify pass file is valid (test with Apple's pass validator)
   - Check server logs for errors

4. **iOS app can't connect to server**
   - Ensure server is running on port 3001
   - Check firewall settings
   - Verify iOS simulator/device can reach localhost

### Debug Commands

```bash
# Check certificate validity
openssl x509 -in .certificates/signerCert.pem -text -noout

# Validate pass file
# (Use Apple's pass validator tool)

# Check server logs
npm start # Watch console output
```

## Production Considerations

For production deployment:

1. **Secure Hosting**
   - Deploy server with HTTPS
   - Use environment variables for secrets
   - Implement proper logging

2. **Database Integration**
   - Store device registrations
   - Track pass updates
   - Implement user pass management

3. **Push Notifications**
   - Set up APNs for automatic updates
   - Handle balance change notifications
   - Implement update queuing

4. **Security**
   - Rotate certificates regularly
   - Implement rate limiting
   - Validate all inputs

## File Structure

```
wallet-pass-server/
├── .certificates/          # Certificate files
├── models/DashCard.pass/   # Pass template
├── passes/                 # Generated passes
├── services/               # Business logic
├── server.js              # Main server
├── setup.js               # Setup wizard
└── test.js                # Test suite

Dash/
├── Services/WalletPassService.swift
├── Views/Components/AddToWalletButton.swift
└── Views/SettingsView.swift (updated)
```

## Next Steps

1. **Complete Certificate Setup**
   - Get your Apple Developer certificates
   - Convert to PEM format
   - Add to server

2. **Test End-to-End**
   - Generate passes from iOS app
   - Verify wallet integration
   - Test on physical device

3. **Customize Design**
   - Add your app's branding
   - Create proper logo images
   - Adjust colors and styling

4. **Deploy to Production**
   - Set up secure hosting
   - Configure production certificates
   - Implement monitoring

## Support

If you encounter issues:
1. Check the server logs
2. Run the test suite
3. Verify certificate setup
4. Test with Apple's pass validator

The implementation is designed to work locally for development and testing. Additional security and scalability measures are needed for production use.
