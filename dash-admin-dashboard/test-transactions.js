const admin = require('firebase-admin');

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
    projectId: 'dashfinanceapp-51a69'
  });
}

async function testTransactions() {
  try {
    console.log('Testing Firestore transactions queries...');
    
    const db = admin.firestore();
    
    // Test 1: Check if transactions collection exists and has any documents
    console.log('\n1. Checking transactions collection...');
    const transactionsSnapshot = await db.collection('transactions').limit(5).get();
    console.log(`Found ${transactionsSnapshot.size} documents in transactions collection`);
    
    if (transactionsSnapshot.size > 0) {
      transactionsSnapshot.forEach(doc => {
        const data = doc.data();
        console.log(`Transaction ${doc.id}:`, {
          senderId: data.senderId,
          recipientId: data.recipientId,
          amount: data.amount,
          timestamp: data.timestamp?.toDate?.() || data.timestamp,
          date: data.date?.toDate?.() || data.date
        });
      });
    }
    
    // Test 2: Check users collection for transaction subcollections
    console.log('\n2. Checking users collection...');
    const usersSnapshot = await db.collection('users').limit(3).get();
    console.log(`Found ${usersSnapshot.size} users`);
    
    for (const userDoc of usersSnapshot.docs) {
      console.log(`\nChecking transactions for user ${userDoc.id}...`);
      const userTransactions = await db.collection('users').doc(userDoc.id).collection('transactions').limit(3).get();
      console.log(`User ${userDoc.id} has ${userTransactions.size} transactions`);
      
      if (userTransactions.size > 0) {
        userTransactions.forEach(txDoc => {
          const txData = txDoc.data();
          console.log(`  Transaction ${txDoc.id}:`, {
            amount: txData.amount,
            type: txData.type,
            date: txData.date?.toDate?.() || txData.date,
            timestamp: txData.timestamp?.toDate?.() || txData.timestamp
          });
        });
      }
    }
    
    // Test 3: Try the actual query that the API uses
    console.log('\n3. Testing API query patterns...');
    
    if (usersSnapshot.size > 0) {
      const testUserId = usersSnapshot.docs[0].id;
      console.log(`Testing queries for user: ${testUserId}`);
      
      try {
        // Test user subcollection query with orderBy
        console.log('Testing user subcollection with orderBy...');
        const userTxQuery = await db.collection('users').doc(testUserId).collection('transactions')
          .orderBy('date', 'desc')
          .limit(10)
          .get();
        console.log(`User subcollection orderBy query returned ${userTxQuery.size} results`);
      } catch (error) {
        console.log('User subcollection orderBy failed:', error.message);
        
        // Fallback without orderBy
        console.log('Trying user subcollection without orderBy...');
        const userTxQueryNoOrder = await db.collection('users').doc(testUserId).collection('transactions')
          .limit(10)
          .get();
        console.log(`User subcollection no-order query returned ${userTxQueryNoOrder.size} results`);
      }
      
      try {
        // Test top-level transactions query
        console.log('Testing top-level transactions where senderId...');
        const senderQuery = await db.collection('transactions')
          .where('senderId', '==', testUserId)
          .orderBy('timestamp', 'desc')
          .limit(10)
          .get();
        console.log(`Sender query returned ${senderQuery.size} results`);
      } catch (error) {
        console.log('Sender query failed:', error.message);
      }
      
      try {
        console.log('Testing top-level transactions where recipientId...');
        const recipientQuery = await db.collection('transactions')
          .where('recipientId', '==', testUserId)
          .orderBy('timestamp', 'desc')
          .limit(10)
          .get();
        console.log(`Recipient query returned ${recipientQuery.size} results`);
      } catch (error) {
        console.log('Recipient query failed:', error.message);
      }
    }
    
    console.log('\nTest completed successfully!');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
  
  process.exit(0);
}

testTransactions();
