# CSS Loading Issue - TrueNAS Deployment Fix

## Quick Diagnosis

Your Firebase authentication is working, but CSS isn't loading. Here's how to fix it:

### Step 1: Check if static files exist in the container

```bash
# Check the debug endpoint to see if files exist
curl http://your-truenas-ip:3000/debug/static
```

This should return JSON showing if `styles.css`, `app.js`, and `index.html` exist in the container.

### Step 2: Rebuild and redeploy the Docker image

The issue is likely that the static files weren't properly copied into the Docker image. You need to:

1. **Rebuild the Docker image** with the updated server.js:

   ```bash
   # On your development machine
   cd dash-admin-dashboard
   docker build -t dash-admin-dashboard:latest .
   ```

2. **Save and transfer the image to TrueNAS**:

   ```bash
   # Save the image
   docker save dash-admin-dashboard:latest > dash-admin-dashboard.tar

   # Transfer to TrueNAS (replace with your TrueNAS IP)
   scp dash-admin-dashboard.tar root@your-truenas-ip:/tmp/
   ```

3. **Load the image on TrueNAS**:

   ```bash
   # On TrueNAS
   docker load < /tmp/dash-admin-dashboard.tar
   ```

4. **Restart the container**:

   ```bash
   # Stop and remove current container
   docker stop dash-admin-dashboard
   docker rm dash-admin-dashboard

   # Start with new image
   cd /mnt/tank/appdata/dash-admin
   docker-compose -f truenas-docker-compose.yml up -d
   ```

### Step 3: Alternative - Quick Fix with Volume Mount

If rebuilding doesn't work, you can mount the static files directly:

1. **Copy static files to TrueNAS**:

   ```bash
   # Create directory on TrueNAS
   mkdir -p /mnt/tank/appdata/dash-admin/public

   # Copy files from your development machine
   scp -r dash-admin-dashboard/src/public/* root@your-truenas-ip:/mnt/tank/appdata/dash-admin/public/
   ```

2. **Update docker-compose.yml** to mount the static files:

   ```yaml
   version: "3.8"

   services:
     dash-admin:
       image: dash-admin-dashboard:latest
       container_name: dash-admin-dashboard
       restart: unless-stopped
       ports:
         - "3000:3000"
       environment:
         - NODE_ENV=production
         - PORT=3000
       env_file:
         - .env
       volumes:
         - /mnt/tank/appdata/dash-admin/logs:/app/logs
         - /mnt/tank/appdata/dash-admin/public:/app/src/public # Mount static files
       networks:
         - dash-network

   networks:
     dash-network:
       driver: bridge
   ```

### Step 4: Verify the fix

1. **Check the debug endpoint**:

   ```bash
   curl http://your-truenas-ip:3000/debug/static
   ```

2. **Test CSS loading directly**:

   ```bash
   curl -I http://your-truenas-ip:3000/styles.css
   ```

   Should return `200 OK` with `Content-Type: text/css`

3. **Check browser network tab** - CSS should now load without 404 errors

### Step 5: Clear browser cache

After fixing, make sure to:

- Hard refresh the page (Ctrl+F5 or Cmd+Shift+R)
- Clear browser cache
- Try in incognito/private mode

## What was changed

I've updated the server.js file to:

1. Allow all origins in production (fixes CORS issues)
2. Added explicit CSS and JS routes with proper MIME types
3. Added a debug endpoint to check static file availability
4. Improved static file serving configuration

The main issue was likely that the static files weren't properly included in the Docker image when you built it, or the CORS policy was blocking them.
