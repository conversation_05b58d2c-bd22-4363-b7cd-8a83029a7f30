# Use Node.js 18 LTS as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Create non-root user for security (TrueNAS Scale compatible)
RUN addgroup -g 1001 -S nodejs
RUN adduser -S dashapp -u 1001 -G nodejs

# Copy package files
COPY package*.json ./

# Install system dependencies for SSL
RUN apk add --no-cache openssl

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy application code
COPY . .

# Create necessary directories and set permissions
RUN mkdir -p /app/logs && \
    chown -R dashapp:nodejs /app

# Switch to non-root user
USER dashapp

# Set environment variable for Firebase config location
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/config/firebase-service-account.json

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["node", "server.js"]
