# TrueNAS Scale Deployment Guide for Dash Admin Dashboard

This guide will help you deploy your Dash Admin Dashboard to TrueNAS Scale using multiple deployment methods. Choose the method that best fits your needs.

## Prerequisites

- TrueNAS Scale server (version 22.12 or later recommended)
- SSH access to TrueNAS Scale (for automated deployment)
- Docker installed on your local machine (for building images)
- Basic understanding of Docker and networking concepts

## Deployment Methods Overview

1. **Custom App via TrueNAS Scale UI** (Recommended for beginners)
2. **Automated Script Deployment** (Recommended for experienced users)
3. **Manual Docker Deployment** (For advanced users or troubleshooting)

## Method 1: Custom App via TrueNAS Scale UI (Recommended)

This is the easiest method for most users and doesn't require SSH access.

### Step 1: Prepare Your Docker Image

1. **Build the Docker image locally:**
   ```bash
   cd dash-admin-dashboard
   docker build -t dash-admin-dashboard:latest .
   ```

2. **Push to a container registry** (Docker Hub, GitHub Container Registry, or your private registry):
   ```bash
   # Example for Docker Hub (replace 'yourusername' with your Docker Hub username)
   docker tag dash-admin-dashboard:latest yourusername/dash-admin-dashboard:latest
   docker push yourusername/dash-admin-dashboard:latest
   ```

### Step 2: Deploy via TrueNAS Scale UI

1. **Access TrueNAS Scale Web Interface**
   - Navigate to `http://your-truenas-ip`
   - Log in with your admin credentials

2. **Navigate to Apps**
   - Click on "Apps" in the left sidebar
   - Click "Discover Apps" if this is your first time

3. **Install Custom App**
   - Click "Custom App" button
   - Fill in the following details:

   **Application Name:** `dash-admin-dashboard`

   **Container Images:**
   - **Image repository:** `yourusername/dash-admin-dashboard` (or your registry URL)
   - **Image tag:** `latest`
   - **Image Pull Policy:** `Always`

   **Container Configuration:**
   - **Container Port:** `3000`
   - **Node Port:** `30000` (or any available port 30000-32767)

   **Environment Variables:**
   Add all required environment variables from your `.env` file:
   - `NODE_ENV=production`
   - `PORT=3000`
   - `JWT_SECRET=your-secure-jwt-secret`
   - `FIREBASE_PROJECT_ID=your-project-id`
   - (Add all other required variables)

   **Storage:**
   - **Host Path:** `/mnt/tank/appdata/dash-admin/logs`
   - **Mount Path:** `/app/logs`

4. **Deploy the Application**
   - Click "Install" to deploy
   - Wait for the deployment to complete

### Step 3: Access Your Application

- **Direct Access:** `http://your-truenas-ip:30000`
- **Internal Network:** The app will be accessible on the assigned node port

## Method 2: Automated Deployment Script

This method uses the included automated script for users comfortable with SSH.

### Prerequisites for Script Deployment
- SSH access enabled on TrueNAS Scale
- SSH key-based authentication or password access
- Docker installed locally

### Steps

1. **Configure environment variables:**
   ```bash
   cd dash-admin-dashboard
   cp .env.example .env
   # Edit .env with your production values
   nano .env
   ```

2. **Run the deployment script:**
   ```bash
   ./deploy-to-truenas.sh
   ```

3. **Follow the prompts:**
   - Enter your TrueNAS Scale IP address
   - Enter your TrueNAS Scale username (usually 'root')
   - Enter desired subdomain (e.g., 'management')
   - Enter your domain (e.g., 'dashfinanceapp.com')

The script will automatically:
- Build the Docker image
- Transfer files to TrueNAS Scale
- Set up the application directory
- Start the container
- Configure networking

## Method 3: Manual Docker Deployment

#### Step 1: Prepare Your Environment

1. **Configure your environment variables:**

   ```bash
   cp .env.example .env
   # Edit .env with your production values
   ```

2. **Build the Docker image:**

   ```bash
   docker build -t dash-admin-dashboard:latest .
   ```

3. **Save the image to a tar file:**
   ```bash
   docker save dash-admin-dashboard:latest -o dash-admin-dashboard.tar
   ```

#### Step 2: Transfer Files to TrueNAS Scale

```bash
# Replace with your TrueNAS Scale details
TRUENAS_IP="*************"
TRUENAS_USER="admin"

# Copy files to TrueNAS Scale
scp dash-admin-dashboard.tar ${TRUENAS_USER}@${TRUENAS_IP}:/tmp/
scp truenas-docker-compose.yml ${TRUENAS_USER}@${TRUENAS_IP}:/tmp/
scp .env ${TRUENAS_USER}@${TRUENAS_IP}:/tmp/
```

#### Step 3: Deploy on TrueNAS Scale

SSH into your TrueNAS Scale server and run:

```bash
# Load the Docker image
docker load -i /tmp/dash-admin-dashboard.tar

# Create application directory
sudo mkdir -p /mnt/tank/appdata/dash-admin/logs
sudo chown -R $USER:$USER /mnt/tank/appdata/dash-admin

# Move configuration files
mv /tmp/truenas-docker-compose.yml /mnt/tank/appdata/dash-admin/docker-compose.yml
mv /tmp/.env /mnt/tank/appdata/dash-admin/.env

# Start the container
cd /mnt/tank/appdata/dash-admin
docker-compose up -d

# Clean up
rm /tmp/dash-admin-dashboard.tar
```

## Setting Up Subdomain Access

### Option 1: Router DNS Configuration (Recommended)

1. **Access your router's admin panel**
2. **Navigate to DNS settings**
3. **Add a DNS entry:**
   - Hostname: `dash-admin.yourdomain.local`
   - IP Address: `[TrueNAS Scale IP]`

### Option 2: Local Hosts File

#### On Windows:

1. Open `C:\Windows\System32\drivers\etc\hosts` as Administrator
2. Add: `************* dash-admin.yourdomain.local`

#### On macOS/Linux:

1. Edit `/etc/hosts` with sudo
2. Add: `************* dash-admin.yourdomain.local`

### Option 3: Pi-hole or Local DNS Server

If you're running Pi-hole or another local DNS server:

1. **Add a local DNS record:**
   - Domain: `dash-admin.yourdomain.local`
   - IP: `[TrueNAS Scale IP]`

## Setting Up Reverse Proxy (Optional but Recommended)

For SSL and port 80/443 access, consider setting up a reverse proxy:

### Using Nginx Proxy Manager

1. **Install Nginx Proxy Manager on TrueNAS Scale**
2. **Create a new proxy host:**
   - Domain Names: `dash-admin.yourdomain.local`
   - Forward Hostname/IP: `localhost` (or TrueNAS Scale IP)
   - Forward Port: `3000`
   - Enable SSL if desired

### Using Traefik

1. **Set up Traefik on TrueNAS Scale**
2. **Add labels to your docker-compose.yml:**
   ```yaml
   services:
     dash-admin:
       # ... existing configuration
       labels:
         - "traefik.enable=true"
         - "traefik.http.routers.dash-admin.rule=Host(`dash-admin.yourdomain.local`)"
         - "traefik.http.services.dash-admin.loadbalancer.server.port=3000"
   ```

## Verification

1. **Check container status:**

   ```bash
   docker ps | grep dash-admin-dashboard
   ```

2. **View logs:**

   ```bash
   cd /mnt/tank/appdata/dash-admin
   docker-compose logs -f
   ```

3. **Test access:**
   - Direct IP: `http://[TrueNAS-IP]:3000`
   - Subdomain: `http://dash-admin.yourdomain.local:3000`

## Maintenance

### Updating the Application

1. **Build new image locally:**

   ```bash
   docker build -t dash-admin-dashboard:latest .
   docker save dash-admin-dashboard:latest -o dash-admin-dashboard.tar
   ```

2. **Transfer and update on TrueNAS Scale:**

   ```bash
   scp dash-admin-dashboard.tar ${TRUENAS_USER}@${TRUENAS_IP}:/tmp/
   ssh ${TRUENAS_USER}@${TRUENAS_IP}

   # On TrueNAS Scale:
   cd /mnt/tank/appdata/dash-admin
   docker-compose down
   docker load -i /tmp/dash-admin-dashboard.tar
   docker-compose up -d
   rm /tmp/dash-admin-dashboard.tar
   ```

### Backup

1. **Backup configuration:**

   ```bash
   tar -czf dash-admin-backup.tar.gz /mnt/tank/appdata/dash-admin
   ```

2. **Backup logs:**
   ```bash
   cp -r /mnt/tank/appdata/dash-admin/logs /path/to/backup/
   ```

## Troubleshooting

### Container Won't Start

1. **Check logs:**

   ```bash
   docker-compose logs dash-admin
   ```

2. **Verify environment variables:**

   ```bash
   cat .env
   ```

3. **Check port conflicts:**
   ```bash
   netstat -tulpn | grep :3000
   ```

### Can't Access via Subdomain

1. **Test DNS resolution:**

   ```bash
   nslookup dash-admin.yourdomain.local
   ```

2. **Check firewall rules on TrueNAS Scale**

3. **Verify container is listening on all interfaces:**
   ```bash
   docker exec dash-admin-dashboard netstat -tulpn
   ```

### Performance Issues

1. **Monitor resource usage:**

   ```bash
   docker stats dash-admin-dashboard
   ```

2. **Check TrueNAS Scale system resources**

3. **Review application logs for errors**

## Security Considerations

1. **Use strong JWT secrets in production**
2. **Configure proper firewall rules**
3. **Consider using SSL/TLS certificates**
4. **Regularly update the application**
5. **Monitor access logs**
6. **Use strong admin passwords**

## Support

For issues specific to this deployment:

1. Check the application logs
2. Verify TrueNAS Scale system health
3. Test network connectivity
4. Review Docker container status

For application-specific issues, refer to the main README.md and CONFIGURATION.md files.
