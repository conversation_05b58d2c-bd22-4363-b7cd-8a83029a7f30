#!/usr/bin/env node

/**
 * Firebase Admin Setup Script
 * Sets custom claims for admin users with domain validation
 */

const admin = require('firebase-admin');
require('dotenv').config();

// Initialize Firebase Admin SDK
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID,
  private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
  client_id: process.env.FIREBASE_CLIENT_ID,
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`,
  universe_domain: "googleapis.com"
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_PROJECT_ID
});

const ALLOWED_DOMAIN = process.env.ALLOWED_EMAIL_DOMAIN || 'dashfinanceapp.com';
const TARGET_EMAIL = '<EMAIL>';

async function setAdminClaims() {
  try {
    console.log('🔧 Setting up Firebase Admin Claims...');
    console.log(`📧 Target Email: ${TARGET_EMAIL}`);
    console.log(`🏢 Allowed Domain: ${ALLOWED_DOMAIN}`);
    
    // Validate domain
    if (!TARGET_EMAIL.endsWith(`@${ALLOWED_DOMAIN}`)) {
      throw new Error(`❌ Email ${TARGET_EMAIL} is not from allowed domain @${ALLOWED_DOMAIN}`);
    }
    
    // Get user by email
    console.log('🔍 Looking up user...');
    let userRecord;
    try {
      userRecord = await admin.auth().getUserByEmail(TARGET_EMAIL);
      console.log(`✅ Found user: ${userRecord.uid}`);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        console.log('❌ User not found. Creating new user...');
        
        // Create user if doesn't exist
        userRecord = await admin.auth().createUser({
          email: TARGET_EMAIL,
          emailVerified: true,
          displayName: 'Ricky Schwartz (Admin)'
        });
        console.log(`✅ Created user: ${userRecord.uid}`);
      } else {
        throw error;
      }
    }
    
    // Set custom claims
    console.log('🔐 Setting admin custom claims...');
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin',
      domain: ALLOWED_DOMAIN,
      createdAt: new Date().toISOString()
    });
    
    // Verify claims were set
    const updatedUser = await admin.auth().getUser(userRecord.uid);
    console.log('✅ Custom claims set successfully:');
    console.log(JSON.stringify(updatedUser.customClaims, null, 2));
    
    console.log('\n🎉 Admin setup complete!');
    console.log(`📧 Admin user: ${TARGET_EMAIL}`);
    console.log(`🆔 User ID: ${userRecord.uid}`);
    console.log(`🔐 Admin claims: ${JSON.stringify(updatedUser.customClaims)}`);
    
    if (!userRecord.emailVerified) {
      console.log('\n⚠️  Note: User email is not verified. They may need to verify their email before logging in.');
    }
    
  } catch (error) {
    console.error('❌ Error setting up admin:', error.message);
    process.exit(1);
  }
}

async function listAdminUsers() {
  try {
    console.log('\n👥 Current Admin Users:');
    console.log('========================');
    
    const listUsersResult = await admin.auth().listUsers();
    const adminUsers = listUsersResult.users.filter(user => 
      user.customClaims && user.customClaims.admin === true
    );
    
    if (adminUsers.length === 0) {
      console.log('No admin users found.');
    } else {
      adminUsers.forEach(user => {
        console.log(`📧 ${user.email}`);
        console.log(`🆔 ${user.uid}`);
        console.log(`🔐 Claims: ${JSON.stringify(user.customClaims)}`);
        console.log(`✅ Verified: ${user.emailVerified}`);
        console.log('---');
      });
    }
  } catch (error) {
    console.error('❌ Error listing admin users:', error.message);
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'setup':
      await setAdminClaims();
      break;
    case 'list':
      await listAdminUsers();
      break;
    default:
      console.log('Usage:');
      console.log('  node setup-admin.js setup  - Set admin claims for target user');
      console.log('  node setup-admin.js list   - List all admin users');
      break;
  }
  
  process.exit(0);
}

main().catch(console.error);
