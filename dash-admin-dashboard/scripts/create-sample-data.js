const admin = require('firebase-admin');
require('dotenv').config();

// Initialize Firebase Admin using environment variables (same as server)
if (!admin.apps.length) {
  try {
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: process.env.FIREBASE_AUTH_URI,
      token_uri: process.env.FIREBASE_TOKEN_URI,
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });
    
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.error('Error initializing Firebase Admin:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

async function createSampleSecurityLogs() {
  console.log('Creating sample security audit logs...');
  
  const sampleLogs = [
    {
      id: 'sample-1',
      userId: 'JJS5fdd9IwPCYjKNZXYT6gYFYVC3', // Your user ID
      event: 'login',
      timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 3600000)), // 1 hour ago
      deviceId: 'device-123',
      ipAddress: '*************',
      userAgent: 'Dash/1.0 (iPhone; iOS 17.0)',
      riskScore: 2,
      success: true,
      metadata: {
        loginMethod: 'email'
      }
    },
    {
      id: 'sample-2',
      userId: 'JJS5fdd9IwPCYjKNZXYT6gYFYVC3',
      event: 'login_failed',
      timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 7200000)), // 2 hours ago
      deviceId: 'device-456',
      ipAddress: '*************',
      userAgent: 'Dash/1.0 (iPhone; iOS 17.0)',
      riskScore: 6,
      success: false,
      metadata: {
        reason: 'invalid_password'
      }
    },
    {
      id: 'sample-3',
      userId: 'JJS5fdd9IwPCYjKNZXYT6gYFYVC3',
      event: '2fa_verified',
      timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 1800000)), // 30 minutes ago
      deviceId: 'device-123',
      ipAddress: '*************',
      userAgent: 'Dash/1.0 (iPhone; iOS 17.0)',
      riskScore: 1,
      success: true,
      metadata: {
        method: 'totp'
      }
    },
    {
      id: 'sample-4',
      userId: 'user456',
      event: 'suspicious_activity',
      timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 10800000)), // 3 hours ago
      deviceId: 'device-789',
      ipAddress: '*********',
      userAgent: 'Dash/1.0 (Android; Android 13)',
      riskScore: 8,
      success: false,
      metadata: {
        reason: 'multiple_failed_attempts'
      }
    },
    {
      id: 'sample-5',
      userId: 'user789',
      event: 'password_changed',
      timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 86400000)), // 1 day ago
      deviceId: 'device-abc',
      ipAddress: '*************',
      userAgent: 'Dash/1.0 (iPhone; iOS 17.0)',
      riskScore: 1,
      success: true,
      metadata: {
        reason: 'user_initiated'
      }
    },
    {
      id: 'sample-6',
      userId: 'JJS5fdd9IwPCYjKNZXYT6gYFYVC3',
      event: 'transaction_attempt',
      timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 900000)), // 15 minutes ago
      deviceId: 'device-123',
      ipAddress: '*************',
      userAgent: 'Dash/1.0 (iPhone; iOS 17.0)',
      riskScore: 3,
      success: true,
      metadata: {
        amount: '50.00',
        recipient: 'user456'
      }
    }
  ];

  try {
    const batch = db.batch();
    
    for (const log of sampleLogs) {
      const docRef = db.collection('security_audit_logs').doc(log.id);
      batch.set(docRef, log);
    }
    
    await batch.commit();
    console.log(`✅ Successfully created ${sampleLogs.length} sample security audit logs`);
    
    // Also create some logs with severity field for the dashboard queries
    const severityLogs = [
      {
        id: 'severity-1',
        userId: 'JJS5fdd9IwPCYjKNZXYT6gYFYVC3',
        event: 'login',
        severity: 'low',
        timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 1200000)), // 20 minutes ago
        riskScore: 2,
        success: true
      },
      {
        id: 'severity-2',
        userId: 'user456',
        event: 'login_failed',
        severity: 'medium',
        timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 3600000)), // 1 hour ago
        riskScore: 6,
        success: false
      },
      {
        id: 'severity-3',
        userId: 'user789',
        event: 'suspicious_activity',
        severity: 'high',
        timestamp: admin.firestore.Timestamp.fromDate(new Date(Date.now() - 7200000)), // 2 hours ago
        riskScore: 9,
        success: false
      }
    ];
    
    const severityBatch = db.batch();
    for (const log of severityLogs) {
      const docRef = db.collection('security_audit_logs').doc(log.id);
      severityBatch.set(docRef, log);
    }
    
    await severityBatch.commit();
    console.log(`✅ Successfully created ${severityLogs.length} additional logs with severity field`);
    
  } catch (error) {
    console.error('Error creating sample data:', error);
  }
}

async function main() {
  try {
    await createSampleSecurityLogs();
    console.log('✅ Sample data creation completed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

main();
