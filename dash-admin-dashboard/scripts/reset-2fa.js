#!/usr/bin/env node

const admin = require('firebase-admin');
require('dotenv').config();

// Initialize Firebase Admin
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: process.env.FIREBASE_PROJECT_ID
});

const db = admin.firestore();
const SECRETS_COLLECTION = 'admin_2fa_secrets';

async function reset2FA() {
  try {
    const email = process.argv[2];
    
    if (!email) {
      console.log('Usage: node reset-2fa.js <email>');
      console.log('Example: node reset-2fa.js <EMAIL>');
      process.exit(1);
    }

    console.log(`Resetting 2FA for: ${email}`);
    
    // Delete the 2FA secret document
    await db.collection(SECRETS_COLLECTION).doc(email).delete();
    
    console.log('✅ 2FA reset successfully!');
    console.log('You can now login again and will be prompted to set up 2FA.');
    
  } catch (error) {
    if (error.code === 5) { // Document not found
      console.log('ℹ️  No 2FA setup found for this email.');
    } else {
      console.error('❌ Error resetting 2FA:', error);
    }
  } finally {
    process.exit(0);
  }
}

reset2FA();
