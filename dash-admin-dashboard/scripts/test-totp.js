#!/usr/bin/env node

const speakeasy = require('speakeasy');
const QRCode = require('qrcode');

async function testTOTP() {
  console.log('🔧 TOTP Debug Test\n');
  
  // Generate a secret
  const secret = speakeasy.generateSecret({
    name: 'Dash Admin Test',
    issuer: 'Dash Finance App'
  });
  
  console.log('📋 Secret Details:');
  console.log('Base32:', secret.base32);
  console.log('OTP Auth URL:', secret.otpauth_url);
  console.log('');
  
  // Generate QR code
  try {
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);
    console.log('✅ QR Code generated successfully');
    console.log('QR Code length:', qrCodeUrl.length);
    console.log('');
  } catch (error) {
    console.error('❌ QR Code generation failed:', error);
  }
  
  // Generate current TOTP token
  const token = speakeasy.totp({
    secret: secret.base32,
    encoding: 'base32'
  });
  
  console.log('🔑 Current TOTP Token:', token);
  console.log('');
  
  // Test verification
  const verified = speakeasy.totp.verify({
    secret: secret.base32,
    encoding: 'base32',
    token: token,
    window: 1
  });
  
  console.log('✅ Self-verification result:', verified);
  console.log('');
  
  // Test with different time windows
  console.log('🕐 Testing time windows:');
  for (let i = -2; i <= 2; i++) {
    const testToken = speakeasy.totp({
      secret: secret.base32,
      encoding: 'base32',
      time: Math.floor(Date.now() / 1000) + (i * 30)
    });
    
    const testVerified = speakeasy.totp.verify({
      secret: secret.base32,
      encoding: 'base32',
      token: testToken,
      window: 1
    });
    
    console.log(`  Window ${i}: Token ${testToken} - Verified: ${testVerified}`);
  }
  
  console.log('\n📱 Manual Test Instructions:');
  console.log('1. Add this secret to your authenticator app manually:');
  console.log(`   Secret: ${secret.base32}`);
  console.log('2. Or scan the QR code from the admin dashboard');
  console.log('3. Compare the token from your app with the current token above');
  console.log('4. They should match (within 30 seconds)');
}

testTOTP().catch(console.error);
