const admin = require('firebase-admin');
require('dotenv').config();

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  const serviceAccount = {
    type: "service_account",
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: "https://accounts.google.com/o/oauth2/auth",
    token_uri: "https://oauth2.googleapis.com/token",
    auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
  };

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: process.env.FIREBASE_PROJECT_ID
  });
}

async function cleanAdminLogins() {
  try {
    const db = admin.firestore();
    const collection = db.collection('admin_dashboard_logins');
    
    console.log('Fetching all documents in admin_dashboard_logins collection...');
    const snapshot = await collection.get();
    
    console.log(`Found ${snapshot.size} documents to delete`);
    
    if (snapshot.size === 0) {
      console.log('No documents to delete');
      return;
    }
    
    // Delete all documents in batches
    const batch = db.batch();
    let count = 0;
    
    snapshot.docs.forEach((doc) => {
      console.log(`Marking document ${doc.id} for deletion`);
      batch.delete(doc.ref);
      count++;
    });
    
    console.log(`Deleting ${count} documents...`);
    await batch.commit();
    
    console.log('✅ Successfully cleaned admin_dashboard_logins collection');
    console.log('From now on, only legitimate admin dashboard logins will be recorded');
    
  } catch (error) {
    console.error('❌ Error cleaning admin logins:', error);
    process.exit(1);
  }
}

// Run the cleanup
cleanAdminLogins().then(() => {
  console.log('Cleanup completed');
  process.exit(0);
});
