# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Email Configuration (Resend)
RESEND_API_KEY=your-resend-api-key

# Firebase Configuration
FIREBASE_PROJECT_ID=dashfinanceapp-51a69
FIREBASE_PRIVATE_KEY_ID=your-firebase-private-key-id
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FIREBASE_CLIENT_EMAIL=your-firebase-client-email
FIREBASE_CLIENT_ID=your-firebase-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# FreshDesk Configuration
FRESHDESK_DOMAIN=dashfinanceapp.freshdesk.com
FRESHDESK_API_KEY=your-freshdesk-api-key

# Security Configuration
ALLOWED_EMAIL_DOMAIN=dashfinanceapp.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>
