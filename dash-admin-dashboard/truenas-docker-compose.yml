version: '3.8'

services:
  dash-admin:
    image: dash-admin-dashboard:latest
    container_name: dash-admin-dashboard
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    env_file:
      - .env
    volumes:
      - /mnt/tank/appdata/dash-admin/logs:/app/logs
    networks:
      - dash-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  dash-network:
    driver: bridge

volumes:
  logs:
    driver: local
