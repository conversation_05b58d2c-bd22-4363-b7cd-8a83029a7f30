#!/bin/bash

# Dash Admin Dashboard - TrueNAS Scale Deployment Script
# This script helps deploy the Dash Admin Dashboard to TrueNAS Scale

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="dash-admin-dashboard"
IMAGE_TAG="latest"
CONTAINER_NAME="dash-admin-dashboard"
TRUENAS_IP=""
TRUENAS_USER=""
SUBDOMAIN=""
LOCAL_DOMAIN=""

echo -e "${BLUE}=== Dash Admin Dashboard - TrueNAS Scale Deployment ===${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker daemon is running
print_status "Checking Docker daemon..."
if ! docker info &> /dev/null; then
    print_warning "Docker daemon is not running. Starting Docker Desktop..."
    open -a Docker
    print_status "Waiting for Docker to start (this may take a moment)..."
    
    # Wait for Docker to be ready
    for i in {1..30}; do
        if docker info &> /dev/null; then
            print_status "Docker is now running!"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Docker failed to start. Please start Docker Desktop manually and try again."
            exit 1
        fi
        sleep 2
    done
fi

# Set default values for dashfinanceapp.com setup
DEFAULT_TRUENAS_IP="*************"
DEFAULT_USER="root"
DEFAULT_SUBDOMAIN="management"
DEFAULT_DOMAIN="dashfinanceapp.com"

# Get user input with defaults
read -p "Enter your TrueNAS Scale IP address [$DEFAULT_TRUENAS_IP]: " TRUENAS_IP
TRUENAS_IP=${TRUENAS_IP:-$DEFAULT_TRUENAS_IP}

read -p "Enter your TrueNAS Scale username [$DEFAULT_USER]: " TRUENAS_USER
TRUENAS_USER=${TRUENAS_USER:-$DEFAULT_USER}

read -p "Enter desired subdomain [$DEFAULT_SUBDOMAIN]: " SUBDOMAIN
SUBDOMAIN=${SUBDOMAIN:-$DEFAULT_SUBDOMAIN}

read -p "Enter your domain [$DEFAULT_DOMAIN]: " LOCAL_DOMAIN
LOCAL_DOMAIN=${LOCAL_DOMAIN:-$DEFAULT_DOMAIN}

print_status "Building Docker image..."
docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .

print_status "Saving Docker image to tar file..."
docker save ${IMAGE_NAME}:${IMAGE_TAG} -o ${IMAGE_NAME}.tar

print_status "Testing SSH connection to TrueNAS Scale server..."
print_warning "You may be prompted for the root password for ${TRUENAS_IP}"

# Test SSH connection with a simple command
if ! ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${TRUENAS_USER}@${TRUENAS_IP} "echo 'SSH connection successful'" 2>/dev/null; then
    print_error "SSH connection failed to ${TRUENAS_IP}"
    print_warning "This could be due to:"
    echo "  1. SSH service not enabled on TrueNAS Scale"
    echo "  2. Firewall blocking SSH (port 22)"
    echo "  3. Incorrect password"
    echo "  4. Wrong IP address or username"
    echo ""
    print_status "To enable SSH on TrueNAS Scale:"
    echo "1. Access TrueNAS Scale web interface: http://${TRUENAS_IP}"
    echo "2. Go to System Settings → Services"
    echo "3. Find SSH service and enable it"
    echo "4. Configure SSH to allow password authentication"
    echo ""
    print_status "Manual deployment required. Files have been prepared:"
    echo "  - Docker image: ${IMAGE_NAME}.tar"
    echo "  - Docker Compose: truenas-docker-compose.yml"
    echo "  - Environment: .env"
    echo ""
    print_status "See MANUAL-DEPLOYMENT.md for detailed manual deployment steps"
    exit 1
fi

print_status "SSH connection successful! Proceeding with deployment..."

print_status "Copying files to TrueNAS Scale server..."
print_warning "You may be prompted for password multiple times during file transfer"

if ! scp -o StrictHostKeyChecking=no ${IMAGE_NAME}.tar ${TRUENAS_USER}@${TRUENAS_IP}:/tmp/; then
    print_error "Failed to copy Docker image"
    print_status "Please check your password and network connection"
    exit 1
fi

if ! scp -o StrictHostKeyChecking=no truenas-docker-compose.yml ${TRUENAS_USER}@${TRUENAS_IP}:/tmp/; then
    print_error "Failed to copy Docker Compose file"
    exit 1
fi

if ! scp -o StrictHostKeyChecking=no .env ${TRUENAS_USER}@${TRUENAS_IP}:/tmp/; then
    print_error "Failed to copy environment file"
    exit 1
fi

print_status "Connecting to TrueNAS Scale server to complete deployment..."
print_warning "You may be prompted for password one more time"

if ssh -o StrictHostKeyChecking=no ${TRUENAS_USER}@${TRUENAS_IP} << 'EOF'
    echo "Loading Docker image..."
    docker load -i /tmp/dash-admin-dashboard.tar
    
    echo "Creating application directory..."
    mkdir -p /mnt/tank/appdata/dash-admin/logs
    chown -R root:root /mnt/tank/appdata/dash-admin
    
    echo "Moving configuration files..."
    mv /tmp/truenas-docker-compose.yml /mnt/tank/appdata/dash-admin/docker-compose.yml
    mv /tmp/.env /mnt/tank/appdata/dash-admin/.env
    
    echo "Starting container..."
    cd /mnt/tank/appdata/dash-admin
    docker-compose up -d
    
    echo "Checking container status..."
    docker ps | grep dash-admin-dashboard
    
    echo "Cleaning up..."
    rm /tmp/dash-admin-dashboard.tar
    
    echo "Deployment completed successfully!"
EOF
then
    print_status "Cleaning up local files..."
    rm ${IMAGE_NAME}.tar
    print_status "Deployment completed successfully!"
else
    print_error "Deployment failed on TrueNAS Scale server"
    print_status "Docker image saved locally as: ${IMAGE_NAME}.tar"
    print_status "Please check TrueNAS Scale server logs and try manual deployment"
    print_status "See MANUAL-DEPLOYMENT.md for alternative deployment methods"
    exit 1
fi

print_status "Deployment completed!"
print_status "Your Dash Admin Dashboard should now be running on http://${TRUENAS_IP}:3000"
print_status "To set up the subdomain, add this to your local DNS or hosts file:"
print_status "${TRUENAS_IP} ${SUBDOMAIN}.${LOCAL_DOMAIN}"

echo -e "\n${BLUE}=== Next Steps ===${NC}"
echo "1. Configure your router's DNS or local DNS server to point ${SUBDOMAIN}.${LOCAL_DOMAIN} to ${TRUENAS_IP}"
echo "2. Or add '${TRUENAS_IP} ${SUBDOMAIN}.${LOCAL_DOMAIN}' to your local hosts file"
echo "3. Access your dashboard at: http://${SUBDOMAIN}.${LOCAL_DOMAIN}:3000"
echo "4. Consider setting up a reverse proxy (like Traefik or Nginx Proxy Manager) for SSL and port 80/443 access"

print_status "Deployment script completed successfully!"
