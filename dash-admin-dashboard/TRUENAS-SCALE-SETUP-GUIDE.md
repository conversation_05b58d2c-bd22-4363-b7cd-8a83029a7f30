# Complete TrueNAS Scale Setup Guide for Dash Admin Dashboard

## Overview

This guide provides step-by-step instructions for deploying your Dash Admin Dashboard as a TrueNAS Scale app. The dashboard is a Node.js application that provides administrative functionality for the Dash Finance App.

## What You're Deploying

**Technology Stack:**
- Node.js 18 with Express.js framework
- Firebase Admin SDK for authentication
- JWT-based session management
- Rate limiting and security middleware
- Health check endpoints
- Logging capabilities

**Container Specifications:**
- Base Image: Node.js 18 Alpine Linux
- Port: 3000 (configurable)
- Non-root user execution for security
- Health checks included
- Volume mount for logs

## Prerequisites

### TrueNAS Scale Requirements
- TrueNAS Scale 22.12 or later
- At least 2GB RAM available for apps
- Storage pool configured (recommended: `/mnt/tank`)
- Network access to container registries

### Local Development Machine
- Docker installed and running
- Git access to your repository
- Text editor for configuration files

## Deployment Options

### Option 1: TrueNAS Scale Custom App (Recommended)

This is the easiest method using the TrueNAS Scale web interface.

#### Step 1: Prepare Container Image

1. **Build locally and push to registry:**
   ```bash
   cd dash-admin-dashboard
   
   # Build the image
   docker build -t dash-admin-dashboard:latest .
   
   # Tag for your registry (replace with your details)
   docker tag dash-admin-dashboard:latest yourusername/dash-admin:latest
   
   # Push to Docker Hub (or your preferred registry)
   docker push yourusername/dash-admin:latest
   ```

#### Step 2: Configure Environment

1. **Create your production environment file:**
   ```bash
   cp .env.example .env.production
   ```

2. **Extract Firebase credentials from your service account file:**

   Since you have the Firebase service account file at `/Volumes/vault2/AppData/dash-admin/dashfinanceapp-51a69-firebase-adminsdk-fbsvc-971c2a5132.json`, you need to extract the values:

   ```bash
   # View your Firebase service account file to get the values
   cat /Volumes/vault2/AppData/dash-admin/dashfinanceapp-51a69-firebase-adminsdk-fbsvc-971c2a5132.json
   ```

3. **Edit the environment variables:**
   ```env
   NODE_ENV=production
   PORT=3000
   JWT_SECRET=your-super-secure-jwt-secret-change-this

   # Firebase Configuration (extract these from your service account JSON file)
   FIREBASE_PROJECT_ID=dashfinanceapp-51a69
   FIREBASE_PRIVATE_KEY_ID=your-private-key-id-from-json
   FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour-Private-Key-From-JSON\n-----END PRIVATE KEY-----\n"
   FIREBASE_CLIENT_EMAIL=<EMAIL>
   FIREBASE_CLIENT_ID=your-client-id-from-json
   FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
   FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

   # Email Configuration
   RESEND_API_KEY=your-resend-api-key

   # FreshDesk Configuration
   FRESHDESK_DOMAIN=dashfinanceapp.freshdesk.com
   FRESHDESK_API_KEY=your-freshdesk-api-key

   # Security
   ALLOWED_EMAIL_DOMAIN=dashfinanceapp.com
   ADMIN_EMAILS=<EMAIL>,<EMAIL>
   ```

#### Step 3: Deploy via TrueNAS Scale UI

1. **Access TrueNAS Scale:**
   - Open web browser to `http://your-truenas-ip`
   - Login with admin credentials

2. **Navigate to Apps:**
   - Click "Apps" in the left sidebar
   - Click "Discover Apps" if first time

3. **Create Custom App:**
   - Click "Custom App"
   - Fill in the configuration:

   **Basic Configuration:**
   - Application Name: `dash-admin-dashboard`
   - Version: `1.0.0`

   **Container Images:**
   - Image repository: `yourusername/dash-admin`
   - Image tag: `latest`
   - Image Pull Policy: `Always`

   **Container Configuration:**
   - Container Port: `3000`
   - Protocol: `TCP`

   **Networking:**
   - Node Port: `30300` (choose any available port 30000-32767)

   **Environment Variables:**
   Add each variable from your `.env.production` file:
   - Name: `NODE_ENV`, Value: `production`
   - Name: `PORT`, Value: `3000`
   - Name: `JWT_SECRET`, Value: `your-jwt-secret`
   - (Continue for all variables)

   **Storage:**
   - Host Path: `/mnt/tank/appdata/dash-admin/logs`
   - Mount Path: `/app/logs`
   - Type: `Host Path`

4. **Deploy:**
   - Click "Install"
   - Wait for deployment to complete
   - Check status in Apps dashboard

#### Step 4: Verify Deployment

1. **Check app status:**
   - In TrueNAS Scale Apps, verify status is "Running"
   - Check logs for any errors

2. **Test access:**
   - Direct access: `http://your-truenas-ip:30300`
   - Check health endpoint: `http://your-truenas-ip:30300/health`

### Option 2: Automated Script Deployment

Use the included deployment script for automated setup.

#### Prerequisites
- SSH access to TrueNAS Scale
- Root or sudo access on TrueNAS Scale

#### Steps

1. **Prepare environment:**
   ```bash
   cd dash-admin-dashboard
   cp .env.example .env
   # Edit .env with your values
   ```

2. **Run deployment script:**
   ```bash
   chmod +x deploy-to-truenas.sh
   ./deploy-to-truenas.sh
   ```

3. **Follow prompts:**
   - TrueNAS Scale IP address
   - Username (usually 'root')
   - Subdomain preference
   - Domain name

The script handles:
- Docker image building
- File transfer to TrueNAS Scale
- Container deployment
- Network configuration

## Post-Deployment Configuration

### Setting Up Domain Access

#### Option 1: Router DNS (Recommended)
1. Access your router's admin panel
2. Add DNS entry:
   - Hostname: `management.yourdomain.com`
   - IP: `your-truenas-ip`

#### Option 2: Local Hosts File
**Windows:** Edit `C:\Windows\System32\drivers\etc\hosts`
**macOS/Linux:** Edit `/etc/hosts`

Add line:
```
************* management.yourdomain.com
```

### SSL/TLS Setup (Optional)

For production use, consider setting up a reverse proxy:

1. **Install Nginx Proxy Manager** on TrueNAS Scale
2. **Create proxy host:**
   - Domain: `management.yourdomain.com`
   - Forward to: `localhost:30300`
   - Enable SSL certificate

## Maintenance and Updates

### Updating the Application

1. **Build new image:**
   ```bash
   docker build -t yourusername/dash-admin:latest .
   docker push yourusername/dash-admin:latest
   ```

2. **Update in TrueNAS Scale:**
   - Go to Apps dashboard
   - Click on your app
   - Click "Update"
   - Confirm update

### Backup Configuration

1. **Backup app data:**
   ```bash
   tar -czf dash-admin-backup.tar.gz /mnt/tank/appdata/dash-admin
   ```

2. **Export app configuration:**
   - In TrueNAS Scale Apps
   - Click on app → "Export"
   - Save configuration file

## Troubleshooting

### Common Issues

1. **Container won't start:**
   - Check environment variables
   - Verify image exists in registry
   - Check TrueNAS Scale logs

2. **Can't access via domain:**
   - Verify DNS configuration
   - Check firewall rules
   - Test direct IP access first

3. **Performance issues:**
   - Monitor resource usage in TrueNAS Scale
   - Check application logs
   - Verify network connectivity

### Getting Help

1. Check application logs in TrueNAS Scale Apps
2. Verify container status and resource usage
3. Test network connectivity
4. Review environment variable configuration

## Security Considerations

1. **Use strong JWT secrets** in production
2. **Configure proper firewall rules**
3. **Use SSL/TLS certificates** for external access
4. **Regularly update** the application
5. **Monitor access logs** for suspicious activity
6. **Use strong admin passwords**
7. **Limit admin email domains** appropriately

## Next Steps

After successful deployment:

1. **Configure admin users** via the web interface
2. **Set up monitoring** and alerting
3. **Configure backups** for application data
4. **Test disaster recovery** procedures
5. **Document your configuration** for team members

Your Dash Admin Dashboard should now be running successfully on TrueNAS Scale!
