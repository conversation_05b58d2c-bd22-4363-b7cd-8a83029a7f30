const jwt = require('jsonwebtoken');
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized and credentials are valid
let firebaseInitialized = false;

// Check if we have valid Firebase credentials (not placeholder values)
const hasValidFirebaseCredentials = () => {
  return process.env.FIREBASE_PROJECT_ID && 
         process.env.FIREBASE_PROJECT_ID !== 'your-project-id' &&
         process.env.FIREBASE_PRIVATE_KEY && 
         process.env.FIREBASE_PRIVATE_KEY !== '-----BEGIN PRIVATE KEY-----\\nYOUR_PRIVATE_KEY_HERE\\n-----END PRIVATE KEY-----' &&
         !process.env.FIREBASE_PRIVATE_KEY.includes('YOUR_PRIVATE_KEY_HERE') &&
         process.env.FIREBASE_CLIENT_EMAIL &&
         process.env.FIREBASE_CLIENT_EMAIL !== '<EMAIL>' &&
         !process.env.FIREBASE_CLIENT_EMAIL.includes('xxxxx');
};

if (!admin.apps.length && hasValidFirebaseCredentials()) {
  try {
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: process.env.FIREBASE_AUTH_URI,
      token_uri: process.env.FIREBASE_TOKEN_URI,
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID
    });
    
    firebaseInitialized = true;
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.warn('Firebase initialization failed, running in demo mode:', error.message);
    firebaseInitialized = false;
  }
} else {
  console.log('Running in demo mode - Firebase credentials not configured');
  firebaseInitialized = false;
}

const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Access token required'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if user still exists and is authorized
    const allowedEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!allowedEmails.includes(decoded.email)) {
      return res.status(403).json({
        success: false,
        error: 'Access denied - unauthorized email'
      });
    }

    req.user = decoded;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: 'Token expired'
      });
    }
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: 'Invalid token'
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
};

const requireTwoFactor = (req, res, next) => {
  // Check if user has a 2FA timestamp (meaning they've completed 2FA)
  const twoFactorTimestamp = req.user.twoFactorTimestamp;
  
  if (!twoFactorTimestamp) {
    return res.status(403).json({
      success: false,
      error: 'Two-factor authentication required',
      requiresReauth: true
    });
  }

  // Check if 2FA verification is still within grace period (5 minutes)
  const gracePeriodMs = 5 * 60 * 1000; // 5 minutes in milliseconds
  const now = Date.now();

  if ((now - twoFactorTimestamp) > gracePeriodMs) {
    return res.status(403).json({
      success: false,
      error: 'Two-factor authentication expired. Please re-authenticate.',
      requiresReauth: true
    });
  }

  next();
};

module.exports = {
  authenticateToken,
  requireTwoFactor,
  admin
};
