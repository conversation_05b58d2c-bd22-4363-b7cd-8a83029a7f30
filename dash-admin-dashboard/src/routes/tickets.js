const express = require('express');
const { authenticateToken, requireTwoFactor } = require('../middleware/auth');
const axios = require('axios');

const router = express.Router();

// Apply authentication to all ticket routes
router.use(authenticateToken);
router.use(requireTwoFactor);

// FreshDesk API configuration
const getFreshdeskConfig = () => {
  if (!process.env.FRESHDESK_API_KEY || !process.env.FRESHDESK_DOMAIN) {
    throw new Error('FreshDesk configuration missing');
  }
  
  return {
    baseURL: `https://${process.env.FRESHDESK_DOMAIN}/api/v2`,
    auth: {
      username: process.env.FRESHDESK_API_KEY,
      password: 'X'
    },
    headers: {
      'Content-Type': 'application/json'
    }
  };
};

// Get FreshDesk embed URL for iframe
router.get('/embed-url', (req, res) => {
  try {
    if (!process.env.FRESHDESK_DOMAIN) {
      return res.status(500).json({
        success: false,
        error: 'FreshDesk domain not configured'
      });
    }

    const embedUrl = `https://${process.env.FRESHDESK_DOMAIN}/a/dashboard/default`;
    
    res.json({
      success: true,
      data: {
        embedUrl,
        domain: process.env.FRESHDESK_DOMAIN
      }
    });

  } catch (error) {
    console.error('Get embed URL error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get embed URL'
    });
  }
});

// Get tickets with pagination and filters
router.get('/', async (req, res) => {
  try {
    console.log('DEBUG: Tickets API called by user:', req.user?.email);
    console.log('DEBUG: Query params:', req.query);
    
    const { 
      page = 1, 
      per_page = 30, 
      status = '', 
      priority = '', 
      requester_id = '',
      updated_since = ''
    } = req.query;

    console.log('DEBUG: Getting FreshDesk config...');
    const config = getFreshdeskConfig();
    console.log('DEBUG: FreshDesk config created successfully');
    
    // Build query parameters
    const params = {
      page: parseInt(page),
      per_page: Math.min(parseInt(per_page), 100)
    };

    if (status) params.status = status;
    if (priority) params.priority = priority;
    if (requester_id) params.requester_id = requester_id;
    if (updated_since) params.updated_since = updated_since;

    console.log('DEBUG: Making FreshDesk API call with params:', params);
    console.log('DEBUG: FreshDesk URL:', config.baseURL + '/tickets');

    const response = await axios.get(`${config.baseURL}/tickets`, {
      auth: config.auth,
      headers: config.headers,
      params
    });

    console.log('DEBUG: FreshDesk API response status:', response.status);
    console.log('DEBUG: FreshDesk API response data length:', response.data?.length);

    res.json({
      success: true,
      data: {
        tickets: response.data,
        pagination: {
          page: parseInt(page),
          per_page: parseInt(per_page),
          total: response.headers['x-total-count'] ? parseInt(response.headers['x-total-count']) : response.data.length
        }
      }
    });

  } catch (error) {
    console.error('Get tickets error:', error.message);
    console.error('Get tickets error response:', error.response?.data);
    console.error('Get tickets error status:', error.response?.status);
    
    if (error.response?.status === 401) {
      return res.status(401).json({
        success: false,
        error: 'FreshDesk authentication failed'
      });
    }

    if (error.message === 'FreshDesk configuration missing') {
      return res.status(500).json({
        success: false,
        error: 'FreshDesk not configured'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch tickets: ' + error.message
    });
  }
});

// Get specific ticket by ID
router.get('/:ticketId', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const config = getFreshdeskConfig();

    const response = await axios.get(`${config.baseURL}/tickets/${ticketId}`, {
      auth: config.auth,
      headers: config.headers
    });

    res.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    console.error('Get ticket error:', error);
    
    if (error.response?.status === 404) {
      return res.status(404).json({
        success: false,
        error: 'Ticket not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch ticket'
    });
  }
});

// Update ticket
router.patch('/:ticketId', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const updateData = req.body;
    const config = getFreshdeskConfig();

    const response = await axios.put(`${config.baseURL}/tickets/${ticketId}`, updateData, {
      auth: config.auth,
      headers: config.headers
    });

    // Log the action
    console.log(`Ticket ${ticketId} updated by admin ${req.user.email}:`, updateData);

    res.json({
      success: true,
      data: response.data,
      message: 'Ticket updated successfully'
    });

  } catch (error) {
    console.error('Update ticket error:', error);
    
    if (error.response?.status === 404) {
      return res.status(404).json({
        success: false,
        error: 'Ticket not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to update ticket'
    });
  }
});

// Add reply to ticket
router.post('/:ticketId/reply', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { body, private = false, attachments = [] } = req.body;

    if (!body) {
      return res.status(400).json({
        success: false,
        error: 'Reply body is required'
      });
    }

    const config = getFreshdeskConfig();
    
    const replyData = {
      body,
      private,
      attachments
    };

    const response = await axios.post(`${config.baseURL}/tickets/${ticketId}/reply`, replyData, {
      auth: config.auth,
      headers: config.headers
    });

    // Log the action
    console.log(`Reply added to ticket ${ticketId} by admin ${req.user.email}`);

    res.json({
      success: true,
      data: response.data,
      message: 'Reply added successfully'
    });

  } catch (error) {
    console.error('Add reply error:', error);
    
    if (error.response?.status === 404) {
      return res.status(404).json({
        success: false,
        error: 'Ticket not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to add reply'
    });
  }
});

// Get ticket conversations
router.get('/:ticketId/conversations', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const config = getFreshdeskConfig();

    const response = await axios.get(`${config.baseURL}/tickets/${ticketId}/conversations`, {
      auth: config.auth,
      headers: config.headers
    });

    res.json({
      success: true,
      data: response.data
    });

  } catch (error) {
    console.error('Get conversations error:', error);
    
    if (error.response?.status === 404) {
      return res.status(404).json({
        success: false,
        error: 'Ticket not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch conversations'
    });
  }
});

// Create new ticket
router.post('/', async (req, res) => {
  try {
    const { 
      subject, 
      description, 
      email, 
      priority = 1, 
      status = 2, 
      source = 2,
      tags = []
    } = req.body;

    if (!subject || !description || !email) {
      return res.status(400).json({
        success: false,
        error: 'Subject, description, and email are required'
      });
    }

    const config = getFreshdeskConfig();
    
    const ticketData = {
      subject,
      description,
      email,
      priority,
      status,
      source,
      tags
    };

    const response = await axios.post(`${config.baseURL}/tickets`, ticketData, {
      auth: config.auth,
      headers: config.headers
    });

    // Log the action
    console.log(`Ticket created by admin ${req.user.email}:`, { subject, email });

    res.json({
      success: true,
      data: response.data,
      message: 'Ticket created successfully'
    });

  } catch (error) {
    console.error('Create ticket error:', error);
    
    if (error.response?.status === 400) {
      return res.status(400).json({
        success: false,
        error: 'Invalid ticket data'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to create ticket'
    });
  }
});

// Get ticket statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const config = getFreshdeskConfig();
    
    // Get tickets with different statuses
    const [openTickets, pendingTickets, resolvedTickets] = await Promise.all([
      axios.get('/tickets?status=2', config), // Open
      axios.get('/tickets?status=3', config), // Pending
      axios.get('/tickets?status=4', config)  // Resolved
    ]);

    const stats = {
      open: openTickets.headers['x-total-count'] ? parseInt(openTickets.headers['x-total-count']) : openTickets.data.length,
      pending: pendingTickets.headers['x-total-count'] ? parseInt(pendingTickets.headers['x-total-count']) : pendingTickets.data.length,
      resolved: resolvedTickets.headers['x-total-count'] ? parseInt(resolvedTickets.headers['x-total-count']) : resolvedTickets.data.length,
      total: 0
    };

    stats.total = stats.open + stats.pending + stats.resolved;

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get ticket stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch ticket statistics'
    });
  }
});

// Delete ticket
router.delete('/:ticketId', async (req, res) => {
  try {
    const { ticketId } = req.params;
    const config = getFreshdeskConfig();

    await axios.delete(`/tickets/${ticketId}`, config);

    // Log the action
    console.log(`Ticket ${ticketId} deleted by admin ${req.user.email}`);

    res.json({
      success: true,
      message: 'Ticket deleted successfully'
    });

  } catch (error) {
    console.error('Delete ticket error:', error);
    
    if (error.response?.status === 404) {
      return res.status(404).json({
        success: false,
        error: 'Ticket not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete ticket'
    });
  }
});

module.exports = router;
