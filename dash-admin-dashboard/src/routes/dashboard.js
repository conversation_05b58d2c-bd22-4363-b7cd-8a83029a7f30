const express = require('express');
const { authenticateToken, requireTwoFactor, admin } = require('../middleware/auth');
const axios = require('axios');

const router = express.Router();

// Apply authentication to all dashboard routes
router.use(authenticateToken);
router.use(requireTwoFactor);

// Get dashboard overview (metrics)
router.get('/overview', async (req, res) => {
  try {
    const metrics = {
      totalUsers: 0,
      supportTickets: 0,
      securityIncidents: 0,
      lastUpdated: new Date().toISOString()
    };

    // Get total users from Firebase
    try {
      const listUsersResult = await admin.auth().listUsers(1000);
      metrics.totalUsers = listUsersResult.users.length;
    } catch (firebaseError) {
      console.error('Firebase users error:', firebaseError);
      metrics.totalUsers = 'Error loading';
    }

    // Get support tickets from FreshDesk
    try {
      if (process.env.FRESHDESK_API_KEY && process.env.FRESHDESK_DOMAIN) {
        // First try to get tickets with pagination to get total count
        const freshdeskResponse = await axios.get(
          `https://${process.env.FRESHDESK_DOMAIN}/api/v2/tickets`,
          {
            auth: {
              username: process.env.FRESHDESK_API_KEY,
              password: 'X'
            },
            headers: {
              'Content-Type': 'application/json'
            },
            params: {
              per_page: 1,
              page: 1
            },
            timeout: 10000
          }
        );
        
        // Get total count from headers or fallback to getting all tickets count
        let totalTickets = 0;
        if (freshdeskResponse.headers['x-total-count']) {
          totalTickets = parseInt(freshdeskResponse.headers['x-total-count']);
        } else {
          // Fallback: get stats from tickets endpoint
          try {
            const statsResponse = await axios.get(
              `https://${process.env.FRESHDESK_DOMAIN}/api/v2/tickets`,
              {
                auth: {
                  username: process.env.FRESHDESK_API_KEY,
                  password: 'X'
                },
                headers: {
                  'Content-Type': 'application/json'
                },
                params: {
                  per_page: 100
                },
                timeout: 10000
              }
            );
            totalTickets = statsResponse.data.length;
          } catch (statsError) {
            console.error('FreshDesk stats fallback error:', statsError);
            totalTickets = 0;
          }
        }
        
        metrics.supportTickets = totalTickets;
        console.log('FreshDesk tickets loaded:', totalTickets);
      } else {
        console.log('FreshDesk not configured - missing API key or domain');
        metrics.supportTickets = 0;
      }
    } catch (freshdeskError) {
      console.error('FreshDesk API error:', freshdeskError.message);
      if (freshdeskError.response) {
        console.error('FreshDesk API response:', freshdeskError.response.status, freshdeskError.response.data);
      }
      metrics.supportTickets = 0; // Show 0 instead of error message
    }

    // Get security incidents from Firebase (assuming you have a security_logs collection)
    try {
      const db = admin.firestore();
      const securityLogsRef = db.collection('security_audit_logs');
      const snapshot = await securityLogsRef.count().get();
      metrics.securityIncidents = snapshot.data().count;
    } catch (securityError) {
      console.error('Security logs error:', securityError);
      // Return 0 instead of mock data when Firestore is not available
      metrics.securityIncidents = 0;
    }

    res.json({
      success: true,
      data: metrics
    });

  } catch (error) {
    console.error('Dashboard metrics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to load dashboard metrics'
    });
  }
});

// Get recent activity
router.get('/activity', async (req, res) => {
  try {
    const activities = [];

    // Get recent admin dashboard logins from Firestore ONLY
    try {
      const db = admin.firestore();
      const dashboardLoginsRef = db.collection('admin_dashboard_logins');
      
      // Check if collection exists first
      const recentLogins = await dashboardLoginsRef
        .orderBy('loginTime', 'desc')
        .limit(20)
        .get();

      console.log(`Found ${recentLogins.size} admin dashboard logins in collection`);

      recentLogins.forEach(doc => {
        const data = doc.data();
        console.log('Processing admin login document:', {
          id: doc.id,
          email: data.email,
          loginTime: data.loginTime,
          loginType: data.loginType,
          success: data.success
        });

        // Only process successful logins with valid email and loginTime
        if (data.success && data.email && data.loginTime) {
          const loginTime = data.loginTime?.toDate?.() || new Date();
          const now = new Date();
          const timeDiff = now - loginTime;
          const hoursAgo = Math.floor(timeDiff / (1000 * 60 * 60));
          const daysAgo = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          
          let timeAgoText;
          if (hoursAgo < 1) {
            const minutesAgo = Math.floor(timeDiff / (1000 * 60));
            timeAgoText = minutesAgo < 1 ? 'Just now' : `${minutesAgo} minutes ago`;
          } else if (hoursAgo < 24) {
            timeAgoText = `${hoursAgo} hours ago`;
          } else {
            timeAgoText = `${daysAgo} days ago`;
          }

          const displayName = data.email.split('@')[0];
          const loginTypeText = data.loginType === 'setup_completed' ? 'completed 2FA setup and logged in to admin dashboard' : 'logged in to admin dashboard';

          activities.push({
            type: 'admin_login',
            message: `${displayName} ${loginTypeText}`,
            timestamp: loginTime,
            severity: 'info',
            timeAgo: timeAgoText,
            userEmail: data.email,
            displayName: displayName,
            ipAddress: data.ipAddress,
            loginType: data.loginType
          });

          console.log(`Added valid admin login activity: ${displayName} ${loginTypeText} - ${timeAgoText}`);
        } else {
          console.log('Skipping invalid admin login document:', {
            success: data.success,
            hasEmail: !!data.email,
            hasLoginTime: !!data.loginTime
          });
        }
      });
    } catch (error) {
      console.error('Error fetching admin dashboard login activity:', error);
      // If collection doesn't exist or there's an error, that's fine - we'll show the fallback message
    }

    // If no admin dashboard logins exist, show a helpful message
    if (activities.length === 0) {
      activities.push({
        type: 'info',
        message: 'No admin dashboard logins recorded yet',
        timestamp: new Date(),
        severity: 'info',
        timeAgo: 'Just now'
      });
    }

    console.log(`Returning ${activities.length} activities`);

    res.json({
      success: true,
      data: activities
    });

  } catch (error) {
    console.error('Dashboard activity error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to load recent activity'
    });
  }
});

// Get system status
router.get('/status', async (req, res) => {
  try {
    const status = {
      server: 'healthy',
      firebase: 'unknown',
      freshdesk: 'unknown',
      lastCheck: new Date().toISOString()
    };

    // Check Firebase connection
    try {
      await admin.auth().listUsers(1);
      status.firebase = 'healthy';
    } catch (error) {
      status.firebase = 'error';
    }

    // Check FreshDesk connection
    try {
      if (process.env.FRESHDESK_API_KEY && process.env.FRESHDESK_DOMAIN) {
        await axios.get(
          `https://${process.env.FRESHDESK_DOMAIN}/api/v2/tickets?per_page=1`,
          {
            auth: {
              username: process.env.FRESHDESK_API_KEY,
              password: 'X'
            },
            timeout: 5000
          }
        );
        status.freshdesk = 'healthy';
      } else {
        status.freshdesk = 'not_configured';
      }
    } catch (error) {
      status.freshdesk = 'error';
    }

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('System status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check system status'
    });
  }
});

// Broadcast notification to all users
router.post('/broadcast-notification', async (req, res) => {
  try {
    const { title, message, type, priority, sendImmediately, expiresAt } = req.body;

    // Validate input
    if (!title || !message || !type) {
      return res.status(400).json({
        success: false,
        error: 'Title, message, and type are required'
      });
    }

    if (!expiresAt) {
      return res.status(400).json({
        success: false,
        error: 'Expiration date and time are required'
      });
    }

    // Validate title and message length
    if (title.length > 50) {
      return res.status(400).json({
        success: false,
        error: 'Title must be 50 characters or less'
      });
    }

    if (message.length > 200) {
      return res.status(400).json({
        success: false,
        error: 'Message must be 200 characters or less'
      });
    }

    console.log('Broadcasting notification:', { title, message, type, priority });

    // Get all users from Firebase
    const listUsersResult = await admin.auth().listUsers(1000);
    const users = listUsersResult.users;

    if (users.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No users found to send notifications to',
        userCount: 0
      });
    }

    // Create notification payload
    const notificationPayload = {
      notification: {
        title: title,
        body: message
      },
      data: {
        type: type,
        priority: priority || 'normal',
        timestamp: new Date().toISOString(),
        source: 'admin_broadcast'
      }
    };

    // Get user tokens from Firestore (if you store FCM tokens there)
    // For now, we'll create a notification document for each user
    const db = admin.firestore();
    const batch = db.batch();

    let successCount = 0;
    let errorCount = 0;

    // Create notification documents for each user
    for (const user of users) {
      try {
        const notificationRef = db.collection('notifications').doc();
        batch.set(notificationRef, {
          userId: user.uid,
          title: title,
          message: message,
          type: type,
          priority: priority || 'normal',
          isRead: false,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          expiresAt: admin.firestore.Timestamp.fromDate(new Date(expiresAt)),
          source: 'admin_broadcast'
        });
        successCount++;
      } catch (error) {
        console.error(`Error creating notification for user ${user.uid}:`, error);
        errorCount++;
      }
    }

    // Commit the batch
    await batch.commit();

    // If you have FCM tokens stored, you could also send push notifications here
    // Example:
    // const tokens = await getUserFCMTokens(users);
    // if (tokens.length > 0) {
    //   const fcmResponse = await admin.messaging().sendMulticast({
    //     tokens: tokens,
    //     ...notificationPayload
    //   });
    // }

    console.log(`Broadcast notification sent to ${successCount} users, ${errorCount} errors`);

    res.json({
      success: true,
      message: `Notification sent successfully to ${successCount} users`,
      userCount: successCount,
      errorCount: errorCount
    });

  } catch (error) {
    console.error('Error broadcasting notification:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to broadcast notification: ' + error.message
    });
  }
});

module.exports = router;
