const express = require('express');
const { authenticateToken, requireTwoFactor, admin } = require('../middleware/auth');
const { Resend } = require('resend');

const router = express.Router();

// Initialize Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Apply authentication to all user routes
router.use(authenticateToken);
router.use(requireTwoFactor);

// Get all users with pagination
router.get('/', async (req, res) => {
  console.log('DEBUG: GET /api/users called');
  console.log('DEBUG: Query params:', req.query);
  console.log('DEBUG: User from auth middleware:', req.user?.email);
  
  try {
    const { page = 1, limit = 50, search = '' } = req.query;
    const maxResults = Math.min(parseInt(limit), 1000); // Firebase limit

    console.log('DEBUG: Fetching users with maxResults:', maxResults);

    let listUsersResult;
    
    if (search) {
      console.log('DEBUG: Searching for users with term:', search);
      // For search, we need to get all users and filter (Firebase doesn't support email search)
      listUsersResult = await admin.auth().listUsers(1000);
      const filteredUsers = listUsersResult.users.filter(user => 
        user.email?.toLowerCase().includes(search.toLowerCase()) ||
        user.displayName?.toLowerCase().includes(search.toLowerCase())
      );
      
      const startIndex = (parseInt(page) - 1) * maxResults;
      const endIndex = startIndex + maxResults;
      
      listUsersResult.users = filteredUsers.slice(startIndex, endIndex);
      console.log('DEBUG: Filtered users count:', filteredUsers.length);
    } else {
      console.log('DEBUG: Fetching all users...');
      listUsersResult = await admin.auth().listUsers(maxResults);
      console.log('DEBUG: Firebase returned users count:', listUsersResult.users.length);
    }

    const users = listUsersResult.users.map(user => ({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      emailVerified: user.emailVerified,
      disabled: user.disabled,
      creationTime: user.metadata.creationTime,
      lastSignInTime: user.metadata.lastSignInTime,
      providerData: user.providerData.map(provider => ({
        providerId: provider.providerId,
        uid: provider.uid,
        email: provider.email
      }))
    }));

    console.log('DEBUG: Mapped users count:', users.length);
    console.log('DEBUG: First user sample:', users[0] ? { email: users[0].email, uid: users[0].uid } : 'No users');

    const response = {
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: maxResults,
          total: search ? users.length : listUsersResult.users.length,
          hasMore: listUsersResult.pageToken ? true : false
        }
      }
    };

    console.log('DEBUG: Sending response with users count:', response.data.users.length);
    res.json(response);

  } catch (error) {
    console.error('DEBUG: Get users error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch users'
    });
  }
});

// Get specific user by UID
router.get('/:uid', async (req, res) => {
  try {
    const { uid } = req.params;
    
    const userRecord = await admin.auth().getUser(uid);
    
    // Get additional user data from Firestore if available
    let userData = null;
    try {
      const db = admin.firestore();
      const userDoc = await db.collection('users').doc(uid).get();
      if (userDoc.exists) {
        userData = userDoc.data();
      }
    } catch (firestoreError) {
      console.error('Firestore user data error:', firestoreError);
    }

    const user = {
      uid: userRecord.uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
      photoURL: userRecord.photoURL,
      emailVerified: userRecord.emailVerified,
      disabled: userRecord.disabled,
      creationTime: userRecord.metadata.creationTime,
      lastSignInTime: userRecord.metadata.lastSignInTime,
      lastRefreshTime: userRecord.metadata.lastRefreshTime,
      providerData: userRecord.providerData,
      customClaims: userRecord.customClaims,
      additionalData: userData
    };

    res.json({
      success: true,
      data: user
    });

  } catch (error) {
    console.error('Get user error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch user'
    });
  }
});

// Update user (enable/disable, update email, etc.)
router.patch('/:uid', async (req, res) => {
  try {
    const { uid } = req.params;
    const { disabled, email, displayName, emailVerified, twoFactorCode } = req.body;

    // Require 2FA verification for user updates
    if (!twoFactorCode) {
      return res.status(400).json({
        success: false,
        error: '2FA code is required for user updates'
      });
    }

    // Validate 2FA code
    const db = admin.firestore();
    const SECRETS_COLLECTION = 'admin_2fa_secrets';

    const secretDoc = await db.collection(SECRETS_COLLECTION).doc(req.user.email).get();
    if (!secretDoc.exists || !secretDoc.data().setupComplete) {
      return res.status(400).json({
        success: false,
        error: 'Admin 2FA not setup'
      });
    }

    const secret = secretDoc.data().secret;

    // Import the TOTP verification function
    const speakeasy = require('speakeasy');
    const verifyTOTP = (token, secret) => {
      return speakeasy.totp.verify({
        secret: secret,
        encoding: 'base32',
        token: token,
        window: 2
      });
    };

    const verified = verifyTOTP(twoFactorCode, secret);

    if (!verified) {
      return res.status(400).json({
        success: false,
        error: 'Invalid 2FA code'
      });
    }

    const updateData = {};

    if (typeof disabled === 'boolean') {
      updateData.disabled = disabled;
    }

    if (email) {
      // Basic email validation (no domain restriction for user editing)
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          success: false,
          error: 'Invalid email format'
        });
      }
      updateData.email = email;
    }

    if (displayName !== undefined) {
      updateData.displayName = displayName;
    }

    if (typeof emailVerified === 'boolean') {
      updateData.emailVerified = emailVerified;
    }

    const userRecord = await admin.auth().updateUser(uid, updateData);

    // Also update the Firestore user document if displayName was changed
    if (updateData.displayName !== undefined) {
      try {
        const userDocRef = db.collection('users').doc(uid);
        const userDoc = await userDocRef.get();

        if (userDoc.exists) {
          await userDocRef.update({
            displayName: updateData.displayName,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedBy: req.user.email
          });
          console.log(`Firestore user document updated for ${uid} with new displayName: ${updateData.displayName}`);
        } else {
          // Create user document if it doesn't exist
          await userDocRef.set({
            email: userRecord.email,
            displayName: updateData.displayName,
            uid: uid,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            createdBy: req.user.email,
            updatedBy: req.user.email
          });
          console.log(`Created new Firestore user document for ${uid} with displayName: ${updateData.displayName}`);
        }
      } catch (firestoreError) {
        console.error('Failed to update Firestore user document:', firestoreError);
        // Continue anyway - the Firebase Auth update was successful
      }
    }

    // Log the action with 2FA verification
    console.log(`User ${uid} (${userRecord.email}) updated by admin ${req.user.email} with 2FA verification:`, updateData);

    // Log to admin audit trail
    try {
      await db.collection('admin_audit_logs').add({
        action: 'user_update',
        adminEmail: req.user.email,
        targetUserUid: uid,
        targetUserEmail: userRecord.email,
        changes: updateData,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        twoFactorVerified: true
      });
    } catch (logError) {
      console.error('Failed to log admin action:', logError);
    }

    res.json({
      success: true,
      data: {
        uid: userRecord.uid,
        email: userRecord.email,
        displayName: userRecord.displayName,
        emailVerified: userRecord.emailVerified,
        disabled: userRecord.disabled
      },
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Update user error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }
    
    if (error.code === 'auth/email-already-exists') {
      return res.status(400).json({
        success: false,
        error: 'Email already exists'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to update user'
    });
  }
});

// Reset user password
router.post('/:uid/reset-password', async (req, res) => {
  try {
    const { uid } = req.params;
    
    // Get user to get their email
    const userRecord = await admin.auth().getUser(uid);
    
    if (!userRecord.email) {
      return res.status(400).json({
        success: false,
        error: 'User does not have an email address'
      });
    }

    // Generate password reset link
    const resetLink = await admin.auth().generatePasswordResetLink(userRecord.email);

    // Send password reset email using Resend
    try {
      const emailResult = await resend.emails.send({
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: userRecord.email,
        subject: 'Reset Your Dash Password',
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Dash Password</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; }
              .container { max-width: 600px; margin: 0 auto; background-color: white; }
              .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center; }
              .logo { color: white; font-size: 32px; font-weight: bold; margin-bottom: 10px; }
              .header-text { color: white; font-size: 18px; opacity: 0.9; }
              .content { padding: 40px 20px; }
              .title { font-size: 24px; font-weight: 600; color: #333; margin-bottom: 20px; }
              .message { font-size: 16px; line-height: 1.6; color: #666; margin-bottom: 30px; }
              .button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; font-size: 16px; margin-bottom: 30px; }
              .button:hover { opacity: 0.9; }
              .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
              .security-notice { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0; }
              .security-notice strong { color: #856404; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <div class="logo">Dash</div>
                <div class="header-text">Password Reset Request</div>
              </div>
              
              <div class="content">
                <h1 class="title">Reset Your Password</h1>
                
                <p class="message">
                  Hello,<br><br>
                  We received a request to reset the password for your Dash account (${userRecord.email}). 
                  This request was initiated by an administrator.
                </p>
                
                <p class="message">
                  Click the button below to reset your password. This link will expire in 1 hour for security reasons.
                </p>
                
                <a href="${resetLink}" class="button">Reset Password</a>
                
                <div class="security-notice">
                  <strong>Security Notice:</strong> If you didn't request this password reset, please ignore this email. 
                  Your account remains secure and no changes have been made.
                </div>
                
                <p class="message">
                  If the button above doesn't work, you can copy and paste this link into your browser:<br>
                  <a href="${resetLink}" style="color: #667eea; word-break: break-all;">${resetLink}</a>
                </p>
              </div>
              
              <div class="footer">
                <p>This email was sent by Dash Finance App.<br>
                If you have any questions, please contact our support team.</p>
              </div>
            </div>
          </body>
          </html>
        `,
        text: `
Reset Your Dash Password

Hello,

We received a request to reset the password for your Dash account (${userRecord.email}). This request was initiated by an administrator.

Click the link below to reset your password. This link will expire in 1 hour for security reasons.

${resetLink}

Security Notice: If you didn't request this password reset, please ignore this email. Your account remains secure and no changes have been made.

This email was sent by Dash Finance App.
If you have any questions, please contact our support team.
        `
      });

      console.log(`Password reset email sent successfully to ${userRecord.email}:`, emailResult.id || emailResult.data?.id);
      
      // Log the action
      console.log(`Password reset initiated for user ${uid} (${userRecord.email}) by admin ${req.user.email}`);

      res.json({
        success: true,
        data: {
          email: userRecord.email,
          emailId: emailResult.id || emailResult.data?.id
        },
        message: `Password reset email sent successfully to ${userRecord.email}`
      });

    } catch (emailError) {
      console.error('Failed to send password reset email:', emailError);
      
      // Still return success with the reset link, but indicate email failed
      res.json({
        success: true,
        data: {
          resetLink,
          email: userRecord.email
        },
        message: 'Password reset link generated successfully, but email delivery failed',
        warning: 'Email could not be sent. Please provide the reset link manually.'
      });
    }

  } catch (error) {
    console.error('Reset password error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to generate password reset link'
    });
  }
});

// Get user audit logs
router.get('/:uid/audit-logs', async (req, res) => {
  try {
    const { uid } = req.params;
    const { limit = 50 } = req.query;

    const logs = [];

    // Get user authentication logs from Firestore (if you have them)
    try {
      const db = admin.firestore();
      const auditLogsRef = db.collection('audit_logs');
      const userLogs = await auditLogsRef
        .where('userId', '==', uid)
        .orderBy('timestamp', 'desc')
        .limit(parseInt(limit))
        .get();

      userLogs.forEach(doc => {
        logs.push({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate?.() || new Date()
        });
      });
    } catch (firestoreError) {
      console.error('Firestore audit logs error:', firestoreError);
    }

    // Add Firebase user metadata as audit entries
    const userRecord = await admin.auth().getUser(uid);
    
    if (userRecord.metadata.creationTime) {
      logs.push({
        id: 'creation',
        action: 'user_created',
        timestamp: new Date(userRecord.metadata.creationTime),
        details: 'User account created'
      });
    }

    if (userRecord.metadata.lastSignInTime) {
      logs.push({
        id: 'last_signin',
        action: 'user_signin',
        timestamp: new Date(userRecord.metadata.lastSignInTime),
        details: 'Last sign in'
      });
    }

    // Sort logs by timestamp (most recent first)
    logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    res.json({
      success: true,
      data: logs
    });

  } catch (error) {
    console.error('Get audit logs error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to fetch audit logs'
    });
  }
});

// Get user transactions
router.get('/:uid/transactions', async (req, res) => {
  try {
    const { uid } = req.params;
    const { limit = 50, page = 1 } = req.query;
    
    console.log('DEBUG: Getting transactions for user:', uid);
    
    const db = admin.firestore();
    const transactions = [];
    
    // Get transactions from the user's subcollection (matching iOS app structure)
    try {
      console.log('DEBUG: Querying user transactions subcollection...');
      const userTransactionsRef = db.collection('users').doc(uid).collection('transactions');
      
      // Query without orderBy first to avoid index issues, then sort in memory
      const userTransactionsQuery = await userTransactionsRef
        .limit(parseInt(limit) * 3) // Get more to account for filtering and sorting
        .get();
      
      console.log('DEBUG: Raw query returned', userTransactionsQuery.size, 'documents');
      
      userTransactionsQuery.forEach(doc => {
        const data = doc.data();
        console.log('DEBUG: Processing transaction', doc.id, 'with data keys:', Object.keys(data));
        
        // Convert the transaction data to match frontend expectations
        const transaction = {
          id: doc.id,
          ...data,
          // Ensure we have a timestamp field for sorting (iOS app uses 'date' field)
          timestamp: data.date?.toDate?.() || data.timestamp?.toDate?.() || new Date(),
          // Determine user role based on transaction data
          userRole: data.senderId === uid ? 'sender' : 
                   data.recipientId === uid ? 'recipient' : 
                   data.type === 'income' ? 'recipient' : 
                   data.name?.includes('Received') ? 'recipient' : 'sender'
        };
        
        transactions.push(transaction);
      });
      
      console.log('DEBUG: Found', transactions.length, 'transactions in user subcollection');
      
      // Log sample transaction for debugging
      if (transactions.length > 0) {
        const sample = transactions[0];
        console.log('DEBUG: Sample transaction:', {
          id: sample.id,
          name: sample.name,
          amount: sample.amount,
          type: sample.type,
          status: sample.status,
          date: sample.date,
          timestamp: sample.timestamp,
          userRole: sample.userRole
        });
      }
      
    } catch (firestoreError) {
      console.error('DEBUG: Firestore transactions error:', firestoreError);
      console.error('DEBUG: Error details:', {
        code: firestoreError.code,
        message: firestoreError.message,
        stack: firestoreError.stack
      });
      
      // Return empty result with error info rather than throwing
      return res.json({
        success: true,
        data: {
          transactions: [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: 0,
            hasMore: false
          }
        },
        warning: `Failed to query transactions: ${firestoreError.message}`
      });
    }
    
    // Sort by timestamp (most recent first) - important since we query without orderBy
    transactions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const paginatedTransactions = transactions.slice(startIndex, startIndex + parseInt(limit));
    
    console.log('DEBUG: Returning', paginatedTransactions.length, 'paginated transactions out of', transactions.length, 'total');
    
    const response = {
      success: true,
      data: {
        transactions: paginatedTransactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: transactions.length,
          hasMore: transactions.length > startIndex + parseInt(limit)
        }
      }
    };
    
    res.json(response);

  } catch (error) {
    console.error('Get user transactions error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Provide more specific error messages
    let errorMessage = 'Failed to fetch user transactions';
    if (error.message && error.message.includes('index')) {
      errorMessage = 'Database indexes are missing. Please run: firebase deploy --only firestore:indexes';
    } else if (error.code) {
      errorMessage = `Database error (${error.code}): ${error.message}`;
    }

    res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
});

// Delete user (soft delete - disable account)
router.delete('/:uid', async (req, res) => {
  try {
    const { uid } = req.params;
    const { permanent = false } = req.body;

    if (permanent) {
      // Permanent deletion
      await admin.auth().deleteUser(uid);
      
      // Also delete user data from Firestore
      try {
        const db = admin.firestore();
        await db.collection('users').doc(uid).delete();
      } catch (firestoreError) {
        console.error('Firestore user deletion error:', firestoreError);
      }

      console.log(`User ${uid} permanently deleted by admin ${req.user.email}`);

      res.json({
        success: true,
        message: 'User permanently deleted'
      });
    } else {
      // Soft delete - just disable the account
      await admin.auth().updateUser(uid, { disabled: true });

      console.log(`User ${uid} disabled by admin ${req.user.email}`);

      res.json({
        success: true,
        message: 'User account disabled'
      });
    }

  } catch (error) {
    console.error('Delete user error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to delete user'
    });
  }
});

// Helper function to extract name from email
function extractNameFromEmail(email) {
  const localPart = email.split('@')[0];
  return localPart
    .replace(/[._-]/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

module.exports = router;
