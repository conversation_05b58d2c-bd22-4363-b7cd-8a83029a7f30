const express = require('express');
const { authenticateToken, requireTwoFactor, admin } = require('../middleware/auth');

const router = express.Router();

// Helper function to generate human-readable messages from security events
function getEventMessage(event, success, metadata) {
  const eventMessages = {
    'login': success ? 'User logged in successfully' : 'Login attempt failed',
    'login_failed': 'Failed login attempt',
    'logout': 'User logged out',
    '2fa_enabled': 'Two-factor authentication enabled',
    '2fa_disabled': 'Two-factor authentication disabled',
    '2fa_verified': 'Two-factor authentication verified',
    '2fa_failed': 'Two-factor authentication failed',
    'password_changed': success ? 'Password changed successfully' : 'Password change failed',
    'account_locked': 'Account locked due to security concerns',
    'account_unlocked': 'Account unlocked',
    'suspicious_activity': 'Suspicious activity detected',
    'device_registered': 'New device registered',
    'device_removed': 'Device removed from account',
    'biometric_enabled': 'Biometric authentication enabled',
    'biometric_disabled': 'Biometric authentication disabled',
    'transaction_attempt': 'Transaction attempted',
    'transaction_blocked': 'Transaction blocked by security system',
    'security_settings_changed': 'Security settings modified',
    'pin_authenticated': 'PIN authentication successful',
    'pin_failed': 'PIN authentication failed',
    'pin_reset': 'PIN reset'
  };

  let message = eventMessages[event] || `Security event: ${event}`;
  
  // Add metadata context if available
  if (metadata && typeof metadata === 'object') {
    if (metadata.original_event) {
      message += ` (triggered by ${metadata.original_event})`;
    }
    if (metadata.risk_score) {
      message += ` [Risk Score: ${metadata.risk_score}]`;
    }
  }
  
  return message;
}

// Apply authentication to all security routes
router.use(authenticateToken);
router.use(requireTwoFactor);

// Get security overview (combines metrics and recent logs)
router.get('/', async (req, res) => {
  try {
    const result = {
      metrics: {
        totalIncidents: 0,
        criticalIncidents: 0,
        failedLogins: 0,
        suspiciousActivity: 0,
        lastUpdated: new Date().toISOString()
      },
      logs: []
    };

    try {
      const db = admin.firestore();
      const securityLogsRef = db.collection('security_audit_logs');

      // Get metrics
      const totalSnapshot = await securityLogsRef.count().get();
      result.metrics.totalIncidents = totalSnapshot.data().count;

      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      // Get recent logs and count them manually to avoid index requirements
      const recentLogsForMetrics = await securityLogsRef
        .where('timestamp', '>=', yesterday)
        .get();

      let criticalCount = 0;
      let failedLoginCount = 0;
      let suspiciousCount = 0;

      recentLogsForMetrics.forEach(doc => {
        const data = doc.data();
        if (data.riskScore >= 8) {
          criticalCount++;
        }
        if (data.event === 'login_failed') {
          failedLoginCount++;
        }
        if (data.event === 'suspicious_activity') {
          suspiciousCount++;
        }
      });

      result.metrics.criticalIncidents = criticalCount;
      result.metrics.failedLogins = failedLoginCount;
      result.metrics.suspiciousActivity = suspiciousCount;

      // Debug: First check if collection exists and has documents
      console.log('DEBUG: Checking security_audit_logs collection...');
      
      // Get recent logs for display - try without orderBy first to avoid index issues
      let recentLogsForDisplay;
      try {
        recentLogsForDisplay = await securityLogsRef
          .orderBy('timestamp', 'desc')
          .limit(20)
          .get();
        console.log('DEBUG: Ordered query successful, found', recentLogsForDisplay.size, 'documents');
      } catch (indexError) {
        console.log('DEBUG: Index not available for timestamp ordering, getting unordered logs:', indexError.message);
        // Fallback: get logs without ordering if index doesn't exist
        recentLogsForDisplay = await securityLogsRef
          .limit(20)
          .get();
        console.log('DEBUG: Unordered query found', recentLogsForDisplay.size, 'documents');
      }

      const logs = [];
      recentLogsForDisplay.forEach(doc => {
        const data = doc.data();
        console.log('DEBUG: Processing document', doc.id, 'with data:', JSON.stringify(data, null, 2));
        
        logs.push({
          id: doc.id,
          type: data.event || 'unknown', // Map 'event' to 'type' for frontend
          severity: data.riskScore >= 8 ? 'critical' : data.riskScore >= 5 ? 'high' : data.riskScore >= 3 ? 'warning' : 'info',
          message: getEventMessage(data.event, data.success, data.metadata),
          timestamp: data.timestamp?.toDate?.() || new Date(),
          userId: data.userId,
          deviceId: data.deviceId,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          success: data.success,
          riskScore: data.riskScore,
          metadata: data.metadata
        });
      });

      console.log('DEBUG: Processed', logs.length, 'logs for frontend');

      // Sort logs by timestamp in JavaScript if we couldn't sort in Firestore
      logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      result.logs = logs;

    } catch (firestoreError) {
      console.error('Firestore security overview error:', firestoreError);
      
      // Return empty data if Firestore is not available
      result.metrics = {
        totalIncidents: 0,
        criticalIncidents: 0,
        failedLogins: 0,
        suspiciousActivity: 0,
        lastUpdated: new Date().toISOString()
      };

      result.logs = [];
    }

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Get security overview error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security overview'
    });
  }
});

// Get security logs
router.get('/logs', async (req, res) => {
  try {
    const { page = 1, limit = 50, severity = '', startDate = '', endDate = '' } = req.query;
    const maxResults = Math.min(parseInt(limit), 100);

    const logs = [];

    try {
      const db = admin.firestore();
      let query = db.collection('security_audit_logs');

      // Apply filters
      if (severity) {
        query = query.where('severity', '==', severity);
      }

      if (startDate) {
        query = query.where('timestamp', '>=', new Date(startDate));
      }

      if (endDate) {
        query = query.where('timestamp', '<=', new Date(endDate));
      }

      // Order by timestamp and apply pagination
      query = query.orderBy('timestamp', 'desc')
                   .limit(maxResults)
                   .offset((parseInt(page) - 1) * maxResults);

      const snapshot = await query.get();

      snapshot.forEach(doc => {
        logs.push({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate?.() || new Date()
        });
      });

    } catch (firestoreError) {
      console.error('Firestore security logs error:', firestoreError);
      
      // Return mock data if Firestore is not available
      const mockLogs = [
        {
          id: 'mock-1',
          type: 'failed_login',
          severity: 'warning',
          message: 'Failed login attempt',
          userId: 'unknown',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          timestamp: new Date(Date.now() - 3600000)
        },
        {
          id: 'mock-2',
          type: 'suspicious_activity',
          severity: 'high',
          message: 'Multiple failed login attempts from same IP',
          userId: 'user123',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 7200000)
        }
      ];
      
      logs.push(...mockLogs);
    }

    res.json({
      success: true,
      data: {
        logs,
        pagination: {
          page: parseInt(page),
          limit: maxResults,
          total: logs.length
        }
      }
    });

  } catch (error) {
    console.error('Get security logs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security logs'
    });
  }
});

// Get security metrics
router.get('/metrics', async (req, res) => {
  try {
    const metrics = {
      totalIncidents: 0,
      criticalIncidents: 0,
      failedLogins: 0,
      suspiciousActivity: 0,
      lastUpdated: new Date().toISOString()
    };

    try {
      const db = admin.firestore();
      const securityLogsRef = db.collection('security_audit_logs');

      // Get total incidents
      const totalSnapshot = await securityLogsRef.count().get();
      metrics.totalIncidents = totalSnapshot.data().count;

      // Get critical incidents (last 24 hours)
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const criticalSnapshot = await securityLogsRef
        .where('riskScore', '>=', 8)
        .where('timestamp', '>=', yesterday)
        .count()
        .get();
      metrics.criticalIncidents = criticalSnapshot.data().count;

      // Get failed logins (last 24 hours)
      const failedLoginsSnapshot = await securityLogsRef
        .where('event', '==', 'login_failed')
        .where('timestamp', '>=', yesterday)
        .count()
        .get();
      metrics.failedLogins = failedLoginsSnapshot.data().count;

      // Get suspicious activity (last 24 hours)
      const suspiciousSnapshot = await securityLogsRef
        .where('event', '==', 'suspicious_activity')
        .where('timestamp', '>=', yesterday)
        .count()
        .get();
      metrics.suspiciousActivity = suspiciousSnapshot.data().count;

    } catch (firestoreError) {
      console.error('Firestore security metrics error:', firestoreError);
      
      // Return mock data if Firestore is not available
      metrics.totalIncidents = 42;
      metrics.criticalIncidents = 2;
      metrics.failedLogins = 15;
      metrics.suspiciousActivity = 3;
    }

    res.json({
      success: true,
      data: metrics
    });

  } catch (error) {
    console.error('Get security metrics error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security metrics'
    });
  }
});

// Create security log entry
router.post('/logs', async (req, res) => {
  try {
    const { type, severity, message, userId, ipAddress, userAgent, metadata } = req.body;

    if (!type || !severity || !message) {
      return res.status(400).json({
        success: false,
        error: 'Type, severity, and message are required'
      });
    }

    const logEntry = {
      type,
      severity,
      message,
      userId: userId || null,
      ipAddress: ipAddress || req.ip,
      userAgent: userAgent || req.get('User-Agent'),
      metadata: metadata || {},
      timestamp: new Date(),
      createdBy: req.user.email
    };

    try {
      const db = admin.firestore();
      const docRef = await db.collection('security_audit_logs').add(logEntry);

      res.json({
        success: true,
        data: {
          id: docRef.id,
          ...logEntry
        },
        message: 'Security log entry created successfully'
      });

    } catch (firestoreError) {
      console.error('Firestore create log error:', firestoreError);
      
      // Log to console if Firestore is not available
      console.log('Security Log Entry:', logEntry);
      
      res.json({
        success: true,
        data: {
          id: 'console-log',
          ...logEntry
        },
        message: 'Security log entry logged to console (Firestore unavailable)'
      });
    }

  } catch (error) {
    console.error('Create security log error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create security log entry'
    });
  }
});

// Get security alerts/rules
router.get('/alerts', async (req, res) => {
  try {
    const alerts = [
      {
        id: 'failed-login-threshold',
        name: 'Failed Login Threshold',
        description: 'Alert when more than 5 failed login attempts from same IP in 1 hour',
        enabled: true,
        threshold: 5,
        timeWindow: '1h',
        severity: 'warning'
      },
      {
        id: 'suspicious-location',
        name: 'Suspicious Location Login',
        description: 'Alert when login from unusual geographic location',
        enabled: true,
        severity: 'high'
      },
      {
        id: 'admin-action',
        name: 'Admin Actions',
        description: 'Log all admin actions for audit trail',
        enabled: true,
        severity: 'info'
      }
    ];

    res.json({
      success: true,
      data: alerts
    });

  } catch (error) {
    console.error('Get security alerts error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch security alerts'
    });
  }
});

// Update security alert
router.patch('/alerts/:alertId', async (req, res) => {
  try {
    const { alertId } = req.params;
    const { enabled, threshold, timeWindow } = req.body;

    // In a real implementation, you would update the alert configuration
    // For now, we'll just return success
    
    console.log(`Security alert ${alertId} updated by admin ${req.user.email}:`, {
      enabled,
      threshold,
      timeWindow
    });

    res.json({
      success: true,
      message: 'Security alert updated successfully'
    });

  } catch (error) {
    console.error('Update security alert error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update security alert'
    });
  }
});

// Get blocked IPs
router.get('/blocked-ips', async (req, res) => {
  try {
    const blockedIPs = [
      {
        ip: '*************',
        reason: 'Multiple failed login attempts',
        blockedAt: new Date(Date.now() - 3600000),
        blockedBy: 'system',
        expiresAt: new Date(Date.now() + 23 * 3600000)
      },
      {
        ip: '*********',
        reason: 'Suspicious activity pattern',
        blockedAt: new Date(Date.now() - 7200000),
        blockedBy: req.user.email,
        expiresAt: null // Permanent block
      }
    ];

    res.json({
      success: true,
      data: blockedIPs
    });

  } catch (error) {
    console.error('Get blocked IPs error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch blocked IPs'
    });
  }
});

// Block IP address
router.post('/block-ip', async (req, res) => {
  try {
    const { ip, reason, duration } = req.body;

    if (!ip || !reason) {
      return res.status(400).json({
        success: false,
        error: 'IP address and reason are required'
      });
    }

    const blockEntry = {
      ip,
      reason,
      blockedAt: new Date(),
      blockedBy: req.user.email,
      expiresAt: duration ? new Date(Date.now() + duration * 1000) : null
    };

    // In a real implementation, you would store this in a database
    // and implement actual IP blocking logic
    console.log('IP blocked:', blockEntry);

    res.json({
      success: true,
      data: blockEntry,
      message: 'IP address blocked successfully'
    });

  } catch (error) {
    console.error('Block IP error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to block IP address'
    });
  }
});

// Unblock IP address
router.delete('/blocked-ips/:ip', async (req, res) => {
  try {
    const { ip } = req.params;

    // In a real implementation, you would remove the IP from the blocked list
    console.log(`IP ${ip} unblocked by admin ${req.user.email}`);

    res.json({
      success: true,
      message: 'IP address unblocked successfully'
    });

  } catch (error) {
    console.error('Unblock IP error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unblock IP address'
    });
  }
});

module.exports = router;
