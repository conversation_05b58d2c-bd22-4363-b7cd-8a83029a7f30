const express = require('express');
const jwt = require('jsonwebtoken');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const admin = require('firebase-admin');
const { authenticateToken, requireTwoFactor } = require('../middleware/auth');

const router = express.Router();

// Initialize Firebase Admin (if not already initialized)
if (!admin.apps.length) {
  const fs = require('fs');
  const path = require('path');

  // Try to load Firebase credentials from service account file first
  const serviceAccountPath = process.env.GOOGLE_APPLICATION_CREDENTIALS || '/app/config/firebase-service-account.json';

  try {
    console.log(`Checking for Firebase service account file at: ${serviceAccountPath}`);
    console.log(`File exists: ${fs.existsSync(serviceAccountPath)}`);

    if (fs.existsSync(serviceAccountPath)) {
      console.log(`✅ Loading Firebase credentials from: ${serviceAccountPath}`);
      const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.project_id
      });
      console.log('✅ Firebase Admin initialized successfully from service account file');
    } else {
      // Fallback to environment variables
      console.log('❌ Service account file not found, trying environment variables...');

      const requiredEnvVars = {
        FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
        FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY,
        FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL
      };

      // Log environment variable status for debugging
      console.log('Firebase Environment Variables Check:');
      Object.entries(requiredEnvVars).forEach(([key, value]) => {
        console.log(`${key}: ${value ? 'SET' : 'MISSING'} ${value ? `(length: ${value.length})` : ''}`);
      });

      // Check if all required variables are present
      const missingVars = Object.entries(requiredEnvVars)
        .filter(([key, value]) => !value)
        .map(([key]) => key);

      if (missingVars.length > 0) {
        console.log(`Missing required Firebase environment variables: ${missingVars}`);
        console.log('Running in demo mode - Firebase credentials not configured');
        throw new Error(`Missing Firebase environment variables: ${missingVars.join(', ')}`);
      }

      const serviceAccount = {
        type: "service_account",
        project_id: process.env.FIREBASE_PROJECT_ID,
        private_key: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
      };

      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: process.env.FIREBASE_PROJECT_ID
      });
      console.log('✅ Firebase Admin initialized successfully from environment variables');
    }
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Firebase Admin:', error.message);
    throw error;
  }
}

// Initialize Firestore for 2FA secret storage
const db = admin.firestore();
const SECRETS_COLLECTION = 'admin_2fa_secrets';

// Configuration
const ALLOWED_DOMAIN = process.env.ALLOWED_EMAIL_DOMAIN || 'dashfinanceapp.com';

// TOTP Verification Function (using speakeasy library for compatibility)
function verifyTOTP(code, secret) {
  try {
    // Use speakeasy to verify the token - this ensures compatibility with standard TOTP implementations
    const verified = speakeasy.totp.verify({
      secret: secret,
      encoding: 'base32',
      token: code,
      window: 1, // Allow 1 time step tolerance for clock skew
      step: 30   // 30 second time step
    });
    
    console.log('TOTP verification attempt:', { code, verified });
    return verified;
  } catch (error) {
    console.error('TOTP verification error:', error);
    return false;
  }
}

// Step 1: Firebase Authentication Login
router.post('/firebase-login', async (req, res) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({
        success: false,
        error: 'Firebase ID token is required'
      });
    }

    // Verify Firebase ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    const { email, uid } = decodedToken;

    console.log('Firebase login attempt:', { email, uid });

    // Validate domain restriction
    if (!email.endsWith(`@${ALLOWED_DOMAIN}`)) {
      console.log('Domain validation failed:', email);
      return res.status(403).json({
        success: false,
        error: 'Access restricted to authorized domain'
      });
    }

    // Get user record to check custom claims
    const userRecord = await admin.auth().getUser(uid);
    const customClaims = userRecord.customClaims || {};

    // Check admin privileges
    if (!customClaims.admin) {
      console.log('Admin privileges check failed:', email);
      return res.status(403).json({
        success: false,
        error: 'Admin privileges required'
      });
    }

    console.log('Firebase auth successful, checking 2FA setup...');

    // Check if user has 2FA setup in Firestore
    const secretDoc = await db.collection(SECRETS_COLLECTION).doc(email).get();
    
    if (secretDoc.exists) {
      // User has 2FA, require verification
      const partialToken = jwt.sign(
        { 
          email, 
          uid, 
          type: 'partial',
          firebaseVerified: true 
        },
        process.env.JWT_SECRET,
        { expiresIn: '5m' }
      );
      
      return res.json({
        success: true,
        requiresTwoFactor: true,
        token: partialToken,
        message: 'Please enter your 2FA code'
      });
    }

    // Generate new 2FA secret for first-time setup
    const secret = speakeasy.generateSecret({
      name: `Dash Admin (${email})`,
      issuer: 'Dash Finance App'
    });

    // Store secret temporarily in Firestore with setup flag
    await db.collection(SECRETS_COLLECTION).doc(email).set({
      secret: secret.base32,
      setupComplete: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      uid: uid
    });

    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

    // Create setup token
    const setupToken = jwt.sign(
      { 
        email, 
        uid, 
        type: 'setup',
        firebaseVerified: true 
      },
      process.env.JWT_SECRET,
      { expiresIn: '10m' }
    );

    res.json({
      success: true,
      requiresSetup: true,
      token: setupToken,
      qrCode: qrCodeUrl,
      secret: secret.base32,
      message: 'Please set up 2FA to complete login'
    });

  } catch (error) {
    console.error('Firebase login error:', error);
    
    if (error.code === 'auth/id-token-expired') {
      return res.status(401).json({
        success: false,
        error: 'Firebase token expired. Please login again.'
      });
    }
    
    if (error.code === 'auth/id-token-revoked') {
      return res.status(401).json({
        success: false,
        error: 'Firebase token revoked. Please login again.'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Authentication failed'
    });
  }
});

// Step 2: Complete 2FA setup
router.post('/setup-2fa', async (req, res) => {
  try {
    const { token, code } = req.body;

    if (!token || !code) {
      return res.status(400).json({
        success: false,
        error: 'Token and verification code are required'
      });
    }

    // Verify setup token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'setup' || !decoded.firebaseVerified) {
      return res.status(400).json({
        success: false,
        error: 'Invalid setup token'
      });
    }

    // Get user's secret from Firestore
    const secretDoc = await db.collection(SECRETS_COLLECTION).doc(decoded.email).get();
    if (!secretDoc.exists || secretDoc.data().setupComplete) {
      return res.status(400).json({
        success: false,
        error: 'Setup session expired or already completed'
      });
    }

    const secret = secretDoc.data().secret;

    // Verify 2FA code using same algorithm as main app
    console.log('Setup verification attempt:', {
      email: decoded.email,
      code: code,
      secret: secret,
      secretLength: secret.length
    });
    
    const verified = verifyTOTP(code, secret);
    console.log('Setup verification result:', verified);

    if (!verified) {
      // Try generating current token for comparison
      const currentToken = speakeasy.totp({
        secret: secret,
        encoding: 'base32'
      });
      console.log('Expected current token:', currentToken);
      
      return res.status(400).json({
        success: false,
        error: 'Invalid verification code'
      });
    }

    // Mark setup as complete
    await db.collection(SECRETS_COLLECTION).doc(decoded.email).update({
      setupComplete: true,
      setupCompletedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    // Log admin dashboard login
    try {
      await db.collection('admin_dashboard_logins').add({
        email: decoded.email,
        uid: decoded.uid,
        loginTime: admin.firestore.FieldValue.serverTimestamp(),
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        loginType: 'setup_completed',
        success: true
      });
    } catch (logError) {
      console.error('Failed to log admin dashboard login:', logError);
    }

    // Generate final JWT token
    const finalToken = jwt.sign(
      { 
        email: decoded.email,
        uid: decoded.uid,
        twoFactorVerified: true,
        firebaseVerified: true,
        loginTime: Date.now(),
        twoFactorTimestamp: Date.now()
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.SESSION_TIMEOUT_HOURS ? `${process.env.SESSION_TIMEOUT_HOURS}h` : '24h' }
    );

    console.log('2FA setup completed for:', decoded.email);

    res.json({
      success: true,
      token: finalToken,
      message: '2FA setup completed successfully'
    });

  } catch (error) {
    console.error('Setup 2FA error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Step 3: Verify 2FA for existing users
router.post('/verify-2fa', async (req, res) => {
  try {
    const { token, code } = req.body;

    if (!token || !code) {
      return res.status(400).json({
        success: false,
        error: 'Token and verification code are required'
      });
    }

    // Verify partial token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'partial' || !decoded.firebaseVerified) {
      return res.status(400).json({
        success: false,
        error: 'Invalid token'
      });
    }

    // Get user's secret from Firestore
    const secretDoc = await db.collection(SECRETS_COLLECTION).doc(decoded.email).get();
    if (!secretDoc.exists || !secretDoc.data().setupComplete) {
      return res.status(400).json({
        success: false,
        error: 'User not found or 2FA not setup'
      });
    }

    const secret = secretDoc.data().secret;

    // Verify 2FA code using same algorithm as main app
    const verified = verifyTOTP(code, secret);

    if (!verified) {
      return res.status(400).json({
        success: false,
        error: 'Invalid verification code'
      });
    }

    // Log admin dashboard login
    try {
      await db.collection('admin_dashboard_logins').add({
        email: decoded.email,
        uid: decoded.uid,
        loginTime: admin.firestore.FieldValue.serverTimestamp(),
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        loginType: 'regular_login',
        success: true
      });
    } catch (logError) {
      console.error('Failed to log admin dashboard login:', logError);
    }

    // Generate final JWT token
    const finalToken = jwt.sign(
      { 
        email: decoded.email,
        uid: decoded.uid,
        twoFactorVerified: true,
        firebaseVerified: true,
        loginTime: Date.now(),
        twoFactorTimestamp: Date.now()
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.SESSION_TIMEOUT_HOURS ? `${process.env.SESSION_TIMEOUT_HOURS}h` : '24h' }
    );

    console.log('2FA verification successful for:', decoded.email);

    res.json({
      success: true,
      token: finalToken,
      message: 'Authentication successful'
    });

  } catch (error) {
    console.error('Verify 2FA error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Get current user info
router.get('/me', authenticateToken, requireTwoFactor, async (req, res) => {
  try {
    // Get additional user info from Firebase
    let userInfo = {
      email: req.user.email,
      uid: req.user.uid,
      loginTime: req.user.loginTime,
      twoFactorVerified: req.user.twoFactorVerified,
      firebaseVerified: req.user.firebaseVerified
    };

    // Optionally get more user details from Firebase
    if (req.user.uid) {
      try {
        const userRecord = await admin.auth().getUser(req.user.uid);
        userInfo.displayName = userRecord.displayName;
        userInfo.emailVerified = userRecord.emailVerified;
        userInfo.customClaims = userRecord.customClaims;
      } catch (firebaseError) {
        console.warn('Could not fetch additional user info:', firebaseError.message);
      }
    }

    res.json({
      success: true,
      user: userInfo
    });
  } catch (error) {
    console.error('Get user info error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user information'
    });
  }
});

// Refresh 2FA grace period
router.post('/refresh-2fa', authenticateToken, (req, res) => {
  try {
    // Check if user has valid 2FA verification
    if (!req.user.twoFactorVerified) {
      return res.status(403).json({
        success: false,
        error: 'Two-factor authentication required'
      });
    }

    // Check if user has a 2FA timestamp (meaning they've completed 2FA at some point)
    const twoFactorTimestamp = req.user.twoFactorTimestamp;
    if (!twoFactorTimestamp) {
      return res.status(403).json({
        success: false,
        error: 'No 2FA timestamp found. Please re-authenticate.',
        requiresReauth: true
      });
    }

    // Check if the 2FA timestamp is not too old (allow refresh within reasonable time)
    const maxRefreshPeriodMs = 24 * 60 * 60 * 1000; // 24 hours - same as session timeout
    const now = Date.now();

    if ((now - twoFactorTimestamp) > maxRefreshPeriodMs) {
      return res.status(403).json({
        success: false,
        error: 'Session too old. Please re-authenticate.',
        requiresReauth: true
      });
    }

    // Generate new token with refreshed 2FA timestamp
    const refreshedToken = jwt.sign(
      { 
        email: req.user.email,
        uid: req.user.uid,
        twoFactorVerified: true,
        firebaseVerified: req.user.firebaseVerified,
        loginTime: req.user.loginTime,
        twoFactorTimestamp: now // Refresh the 2FA timestamp
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.SESSION_TIMEOUT_HOURS ? `${process.env.SESSION_TIMEOUT_HOURS}h` : '24h' }
    );

    console.log('2FA grace period refreshed for:', req.user.email);

    res.json({
      success: true,
      token: refreshedToken,
      message: '2FA grace period refreshed successfully'
    });

  } catch (error) {
    console.error('Refresh 2FA error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Logout
router.post('/logout', authenticateToken, (req, res) => {
  // In a real app, you might want to blacklist the token
  // For now, client-side token removal is sufficient
  console.log('User logged out:', req.user.email);
  
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Admin management endpoints

// List all admin users
router.get('/admin/users', authenticateToken, async (req, res) => {
  try {
    // Verify user is admin
    if (!req.user.firebaseVerified) {
      return res.status(403).json({
        success: false,
        error: 'Firebase verification required'
      });
    }

    const listUsersResult = await admin.auth().listUsers();
    const adminUsers = listUsersResult.users.filter(user => 
      user.customClaims && user.customClaims.admin === true
    );

    const userList = adminUsers.map(user => ({
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      emailVerified: user.emailVerified,
      customClaims: user.customClaims,
      creationTime: user.metadata.creationTime,
      lastSignInTime: user.metadata.lastSignInTime
    }));

    res.json({
      success: true,
      users: userList
    });

  } catch (error) {
    console.error('List admin users error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to list admin users'
    });
  }
});

// Grant admin privileges
router.post('/admin/grant', authenticateToken, async (req, res) => {
  try {
    const { email, twoFactorCode } = req.body;

    if (!email || !twoFactorCode) {
      return res.status(400).json({
        success: false,
        error: 'Email and 2FA code are required'
      });
    }

    // Validate 2FA code
    const secretDoc = await db.collection(SECRETS_COLLECTION).doc(req.user.email).get();
    if (!secretDoc.exists || !secretDoc.data().setupComplete) {
      return res.status(400).json({
        success: false,
        error: 'Admin 2FA not setup'
      });
    }

    const secret = secretDoc.data().secret;
    const verified = verifyTOTP(twoFactorCode, secret);

    if (!verified) {
      return res.status(400).json({
        success: false,
        error: 'Invalid 2FA code'
      });
    }

    // Validate domain
    if (!email.endsWith(`@${ALLOWED_DOMAIN}`)) {
      return res.status(400).json({
        success: false,
        error: 'Email must be from authorized domain'
      });
    }

    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);

    // Set admin custom claims
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin',
      domain: ALLOWED_DOMAIN,
      grantedBy: req.user.email,
      grantedAt: new Date().toISOString()
    });

    console.log(`Admin privileges granted to ${email} by ${req.user.email}`);

    res.json({
      success: true,
      message: `Admin privileges granted to ${email}`
    });

  } catch (error) {
    console.error('Grant admin error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to grant admin privileges'
    });
  }
});

// Revoke admin privileges
router.post('/admin/revoke', authenticateToken, async (req, res) => {
  try {
    const { email, twoFactorCode } = req.body;

    if (!email || !twoFactorCode) {
      return res.status(400).json({
        success: false,
        error: 'Email and 2FA code are required'
      });
    }

    // Validate 2FA code
    const secretDoc = await db.collection(SECRETS_COLLECTION).doc(req.user.email).get();
    if (!secretDoc.exists || !secretDoc.data().setupComplete) {
      return res.status(400).json({
        success: false,
        error: 'Admin 2FA not setup'
      });
    }

    const secret = secretDoc.data().secret;
    const verified = verifyTOTP(twoFactorCode, secret);

    if (!verified) {
      return res.status(400).json({
        success: false,
        error: 'Invalid 2FA code'
      });
    }

    // Prevent self-revocation
    if (email === req.user.email) {
      return res.status(400).json({
        success: false,
        error: 'Cannot revoke your own admin privileges'
      });
    }

    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);

    // Remove admin custom claims
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: false,
      role: null,
      revokedBy: req.user.email,
      revokedAt: new Date().toISOString()
    });

    console.log(`Admin privileges revoked from ${email} by ${req.user.email}`);

    res.json({
      success: true,
      message: `Admin privileges revoked from ${email}`
    });

  } catch (error) {
    console.error('Revoke admin error:', error);
    
    if (error.code === 'auth/user-not-found') {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to revoke admin privileges'
    });
  }
});

module.exports = router;
