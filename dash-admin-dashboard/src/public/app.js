
// Use Firebase instance from firebase-config.js
const auth = window.firebaseAuth;

// Global state
let currentUser = null;
let authToken = null;
let currentView = 'overview';

// DOM Elements
const loadingScreen = document.getElementById('loading-screen');
const loginScreen = document.getElementById('login-screen');
const dashboard = document.getElementById('dashboard');
const loginMessage = document.getElementById('login-message');

// Login steps
const loginStepEmail = document.getElementById('login-step-email');
const loginStep2FA = document.getElementById('login-step-2fa');
const loginStepSetup = document.getElementById('login-step-setup');

// Forms
const emailForm = document.getElementById('email-form');
const twoFAForm = document.getElementById('2fa-form');
const setupForm = document.getElementById('setup-form');

// Utility Functions
function showMessage(message, type = 'info') {
    loginMessage.textContent = message;
    loginMessage.className = `message ${type}`;
    loginMessage.classList.remove('hidden');
    
    setTimeout(() => {
        loginMessage.classList.add('hidden');
    }, 5000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    const container = document.getElementById('toast-container');
    container.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => container.removeChild(toast), 300);
    }, 3000);
}

function showLoginStep(step) {
    // Hide all steps
    loginStepEmail.classList.add('hidden');
    loginStep2FA.classList.add('hidden');
    loginStepSetup.classList.add('hidden');
    
    // Show requested step
    switch(step) {
        case 'email':
            loginStepEmail.classList.remove('hidden');
            break;
        case '2fa':
            loginStep2FA.classList.remove('hidden');
            break;
        case 'setup':
            loginStepSetup.classList.remove('hidden');
            break;
    }
}

function setLoading(isLoading, element = null) {
    if (element) {
        const button = element.querySelector('button[type="submit"]');
        if (button) {
            button.disabled = isLoading;
            if (isLoading) {
                button.classList.add('loading');
            } else {
                button.classList.remove('loading');
            }
        }
    }
}

function copySecret() {
    const secretText = document.getElementById('backup-secret-text').textContent;
    navigator.clipboard.writeText(secretText).then(() => {
        showToast('Secret copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = secretText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('Secret copied to clipboard!', 'success');
    });
}

// API Functions
async function makeAuthenticatedRequest(url, options = {}) {
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    const response = await fetch(url, {
        ...options,
        headers
    });
    
    if (response.status === 401) {
        // Token expired, redirect to login
        logout();
        return null;
    }
    
    if (response.status === 403) {
        // Check if it's a 2FA expiration issue
        try {
            const errorData = await response.clone().json();
            if (errorData.requiresReauth && errorData.error && errorData.error.includes('Two-factor authentication expired')) {
                console.log('2FA grace period expired, attempting to refresh...');
                
                // Try to refresh the 2FA grace period
                const refreshSuccess = await refresh2FAGracePeriod();
                if (refreshSuccess) {
                    console.log('2FA grace period refreshed, retrying original request...');
                    // Retry the original request with the new token
                    const newHeaders = {
                        'Content-Type': 'application/json',
                        ...options.headers
                    };
                    
                    if (authToken) {
                        newHeaders['Authorization'] = `Bearer ${authToken}`;
                    }
                    
                    return await fetch(url, {
                        ...options,
                        headers: newHeaders
                    });
                } else {
                    console.log('Failed to refresh 2FA grace period, user needs to re-authenticate');
                    showToast('Your session has expired. Please log in again.', 'warning');
                    logout();
                    return null;
                }
            }
        } catch (parseError) {
            console.error('Error parsing 403 response:', parseError);
        }
    }
    
    return response;
}

// Function to refresh 2FA grace period
async function refresh2FAGracePeriod() {
    try {
        const response = await fetch('/api/auth/refresh-2fa', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.token) {
                // Update the auth token with the refreshed one
                authToken = data.token;
                localStorage.setItem('authToken', authToken);
                console.log('2FA grace period refreshed successfully');
                return true;
            }
        }
        
        console.log('Failed to refresh 2FA grace period:', response.status);
        return false;
    } catch (error) {
        console.error('Error refreshing 2FA grace period:', error);
        return false;
    }
}

// Authentication Functions
async function handleFirebaseLogin(email, password) {
    try {
        setLoading(true, emailForm);
        
        // Sign in with Firebase
        const userCredential = await auth.signInWithEmailAndPassword(email, password);
        const user = userCredential.user;
        
        // Get Firebase ID token
        const idToken = await user.getIdToken();
        
        // Send to backend for verification and 2FA check
        const response = await fetch('/api/auth/firebase-login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ idToken })
        });
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || 'Authentication failed');
        }
        
        if (data.requiresTwoFactor) {
            // User has 2FA setup, show 2FA form
            authToken = data.token; // Partial token
            showLoginStep('2fa');
            showMessage('Please enter your 2FA code', 'info');
        } else if (data.requiresSetup) {
            // First time login, setup 2FA
            authToken = data.token; // Setup token
            document.getElementById('qr-code').src = data.qrCode;
            document.getElementById('backup-secret-text').textContent = data.secret;
            showLoginStep('setup');
            showMessage('Please set up 2FA to complete login', 'info');
        } else {
            // Should not happen with current flow
            throw new Error('Unexpected authentication state');
        }
        
    } catch (error) {
        console.error('Firebase login error:', error);
        showMessage(error.message || 'Login failed', 'error');
    } finally {
        setLoading(false, emailForm);
    }
}

async function handleTwoFactorVerification(code) {
    try {
        setLoading(true, twoFAForm);
        
        const response = await fetch('/api/auth/verify-2fa', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                token: authToken,
                code: code
            })
        });
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || '2FA verification failed');
        }
        
        // Login successful
        authToken = data.token;
        localStorage.setItem('authToken', authToken); // Store the new token
        await loadUserInfo();
        showDashboard();
        showToast('Login successful!', 'success');
        
    } catch (error) {
        console.error('2FA verification error:', error);
        showMessage(error.message || '2FA verification failed', 'error');
    } finally {
        setLoading(false, twoFAForm);
    }
}

async function handleTwoFactorSetup(code) {
    try {
        setLoading(true, setupForm);
        
        const response = await fetch('/api/auth/setup-2fa', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                token: authToken,
                code: code
            })
        });
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || '2FA setup failed');
        }
        
        // Setup successful
        authToken = data.token;
        localStorage.setItem('authToken', authToken); // Store the new token
        await loadUserInfo();
        showDashboard();
        showToast('2FA setup completed successfully!', 'success');
        
    } catch (error) {
        console.error('2FA setup error:', error);
        showMessage(error.message || '2FA setup failed', 'error');
    } finally {
        setLoading(false, setupForm);
    }
}

async function loadUserInfo() {
    try {
        const response = await makeAuthenticatedRequest('/api/auth/me');
        if (response && response.ok) {
            const data = await response.json();
            if (data.success) {
                currentUser = data.user;
                
                // Update user display information
                const displayName = currentUser.displayName || extractNameFromEmail(currentUser.email);
                const initials = generateInitials(displayName);
                
                document.getElementById('user-name').textContent = displayName;
                document.getElementById('user-email').textContent = currentUser.email;
                document.getElementById('user-initials').textContent = initials;
                
                return true;
            }
        }
        return false;
    } catch (error) {
        console.error('Failed to load user info:', error);
        return false;
    }
}

// Helper function to extract name from email
function extractNameFromEmail(email) {
    const localPart = email.split('@')[0];
    // Convert common patterns like firstname.lastname or firstname_lastname
    const name = localPart
        .replace(/[._-]/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
    return name;
}

// Helper function to generate initials from name
function generateInitials(name) {
    return name
        .split(' ')
        .map(word => word.charAt(0).toUpperCase())
        .slice(0, 2) // Take first 2 initials
        .join('');
}

function logout() {
    // Sign out from Firebase
    auth.signOut().catch(console.error);
    
    // Clear local state
    authToken = null;
    currentUser = null;
    
    // Clear stored token
    localStorage.removeItem('authToken');
    
    // Show login screen
    showLoginScreen();
    showToast('Logged out successfully', 'info');
}

// Screen Management
function showLoadingScreen() {
    loadingScreen.classList.remove('hidden');
    loginScreen.classList.add('hidden');
    dashboard.classList.add('hidden');
}

function showLoginScreen() {
    loadingScreen.classList.add('hidden');
    loginScreen.classList.remove('hidden');
    dashboard.classList.add('hidden');
    showLoginStep('email');
}

function showDashboard() {
    loadingScreen.classList.add('hidden');
    loginScreen.classList.add('hidden');
    dashboard.classList.remove('hidden');
    loadDashboardData();
}

// Dashboard Functions
async function loadDashboardData() {
    try {
        // Load overview metrics
        await loadOverviewMetrics();
        
        // Load users if on user management view
        if (currentView === 'user-management') {
            await loadUserManagementList();
        }
        
        // Load security data if on security view
        if (currentView === 'security') {
            await loadSecurityData();
        }
        
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
        showToast('Failed to load dashboard data', 'error');
    }
}

async function loadOverviewMetrics() {
    try {
        const response = await makeAuthenticatedRequest('/api/dashboard/overview');
        if (response && response.ok) {
            const data = await response.json();
            if (data.success) {
                // Update metrics - data is in data.data, not data.metrics
                const metrics = data.data;
                document.getElementById('total-users').textContent = metrics.totalUsers || '-';
                document.getElementById('support-tickets').textContent = metrics.supportTickets || '-';
                document.getElementById('security-incidents').textContent = metrics.securityIncidents || '-';
            }
        }
        
        // Load system status separately
        const statusResponse = await makeAuthenticatedRequest('/api/dashboard/status');
        if (statusResponse && statusResponse.ok) {
            const statusData = await statusResponse.json();
            if (statusData.success) {
                const status = statusData.data;
                
                // Update system status
                const systemStatus = document.getElementById('system-status');
                systemStatus.textContent = status.server === 'healthy' ? 'Healthy' : 'Error';
                systemStatus.className = `status-indicator ${status.server === 'healthy' ? 'healthy' : 'warning'}`;
                
                // Update Firebase status
                const firebaseStatus = document.getElementById('firebase-status');
                firebaseStatus.textContent = status.firebase === 'healthy' ? 'Healthy' : 'Error';
                firebaseStatus.className = `status-indicator ${status.firebase === 'healthy' ? 'healthy' : 'warning'}`;
                
                // Update FreshDesk status
                const freshdeskStatus = document.getElementById('freshdesk-status');
                const freshdeskText = status.freshdesk === 'not_configured' ? 'Not Configured' : 
                                    status.freshdesk === 'healthy' ? 'Healthy' : 'Error';
                freshdeskStatus.textContent = freshdeskText;
                freshdeskStatus.className = `status-indicator ${status.freshdesk === 'healthy' ? 'healthy' : 'warning'}`;
            }
        }

        // Load recent activity
        await loadRecentActivity();
    } catch (error) {
        console.error('Failed to load overview metrics:', error);
    }
}

async function loadRecentActivity() {
    try {
        const response = await makeAuthenticatedRequest('/api/dashboard/activity');
        if (response && response.ok) {
            const data = await response.json();
            if (data.success) {
                const activities = data.data || [];
                const activityContainer = document.getElementById('recent-activity');
                
                if (activities.length === 0) {
                    activityContainer.innerHTML = `
                        <div class="activity-item">
                            <div class="activity-icon">📊</div>
                            <div class="activity-content">
                                <p>No recent activity</p>
                                <small>Activity will appear here as users interact with the system</small>
                            </div>
                        </div>
                    `;
                    return;
                }
                
                activityContainer.innerHTML = '';
                
                activities.forEach(activity => {
                    const activityItem = document.createElement('div');
                    activityItem.className = 'activity-item';
                    
                    // Choose icon based on activity type
                    let icon = '📊'; // default
                    switch(activity.type) {
                        case 'admin_login':
                            icon = '🔐';
                            break;
                        case 'info':
                            icon = 'ℹ️';
                            break;
                        default:
                            icon = '📊';
                            break;
                    }
                    
                    // Use the message as-is from the backend
                    let displayMessage = activity.message;
                    
                    activityItem.innerHTML = `
                        <div class="activity-icon">${icon}</div>
                        <div class="activity-content">
                            <p>${displayMessage}</p>
                            <small>${activity.timeAgo || 'Recently'}</small>
                        </div>
                    `;
                    
                    activityContainer.appendChild(activityItem);
                });
            }
        }
    } catch (error) {
        console.error('Failed to load recent activity:', error);
        const activityContainer = document.getElementById('recent-activity');
        activityContainer.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon">⚠️</div>
                <div class="activity-content">
                    <p>Failed to load recent activity</p>
                    <small>Please try refreshing the page</small>
                </div>
            </div>
        `;
    }
}



let allSecurityLogs = [];
let filteredSecurityLogs = [];
let currentSecurityPage = 1;
const securityLogsPerPage = 20;

async function loadSecurityData() {
    try {
        console.log('DEBUG: Loading security data with token:', authToken ? 'present' : 'missing');
        const response = await makeAuthenticatedRequest('/api/security');
        
        if (!response) {
            console.log('DEBUG: No response received (likely 401 redirect)');
            showToast('Authentication required - please log in again', 'error');
            return;
        }
        
        console.log('DEBUG: Security API response status:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('DEBUG: Security API response data:', data);
            
            if (data.success) {
                // Update security metrics
                document.getElementById('total-incidents').textContent = data.metrics.totalIncidents || '-';
                document.getElementById('critical-incidents').textContent = data.metrics.criticalIncidents || '-';
                document.getElementById('failed-logins').textContent = data.metrics.failedLogins || '-';
                document.getElementById('suspicious-activity').textContent = data.metrics.suspiciousActivity || '-';
                
                // Store all logs for filtering
                allSecurityLogs = data.logs || [];
                filteredSecurityLogs = [...allSecurityLogs];
                
                console.log('DEBUG: Loaded', allSecurityLogs.length, 'security logs');
                
                // Display logs
                displaySecurityLogs();
            } else {
                console.error('DEBUG: Security API returned success=false:', data.error);
                showToast(`Failed to load security data: ${data.error}`, 'error');
            }
        } else {
            const errorText = await response.text();
            console.error('DEBUG: Security API error response:', response.status, errorText);
            showToast(`Failed to load security data (${response.status})`, 'error');
        }
    } catch (error) {
        console.error('Failed to load security data:', error);
        showToast('Failed to load security data', 'error');
    }
}

function displaySecurityLogs() {
    const tbody = document.getElementById('security-logs-body');
    tbody.innerHTML = '';
    
    // Calculate pagination
    const startIndex = (currentSecurityPage - 1) * securityLogsPerPage;
    const endIndex = startIndex + securityLogsPerPage;
    const logsToShow = filteredSecurityLogs.slice(startIndex, endIndex);
    
    console.log('DEBUG: Displaying', logsToShow.length, 'logs');
    console.log('DEBUG: First log structure:', logsToShow[0]);
    
    // Display logs
    logsToShow.forEach((log, index) => {
        try {
            const row = document.createElement('tr');
            
            // Safe property access with fallbacks
            const timestamp = log.timestamp ? new Date(log.timestamp).toLocaleString() : 'Unknown';
            const type = log.type || log.event || 'Unknown';
            const severity = log.severity || 'INFO';
            const message = log.message || log.description || 'No message';
            const ipAddress = log.ipAddress || log.ip_address || '-';
            const userId = log.userId || log.user_id || '-';
            
            row.innerHTML = `
                <td>${timestamp}</td>
                <td>${type}</td>
                <td>
                    <span class="severity-${severity.toLowerCase()}">${severity}</span>
                </td>
                <td>${message}</td>
                <td>${ipAddress}</td>
                <td>${userId}</td>
            `;
            tbody.appendChild(row);
        } catch (error) {
            console.error('DEBUG: Error displaying log at index', index, ':', error);
            console.error('DEBUG: Problematic log object:', log);
        }
    });
    
    // Update pagination info
    updateSecurityPagination();
}

function updateSecurityPagination() {
    const totalPages = Math.ceil(filteredSecurityLogs.length / securityLogsPerPage);
    const countElement = document.getElementById('security-logs-count');
    const pageInfoElement = document.getElementById('security-page-info');
    const prevButton = document.getElementById('security-prev-page');
    const nextButton = document.getElementById('security-next-page');
    
    countElement.textContent = `${filteredSecurityLogs.length} logs`;
    pageInfoElement.textContent = `Page ${currentSecurityPage} of ${totalPages || 1}`;
    
    prevButton.disabled = currentSecurityPage <= 1;
    nextButton.disabled = currentSecurityPage >= totalPages;
}

function filterSecurityLogs() {
    const searchTerm = document.getElementById('security-search').value.toLowerCase();
    const severityFilter = document.getElementById('severity-filter').value;
    const eventFilter = document.getElementById('event-filter').value;
    
    filteredSecurityLogs = allSecurityLogs.filter(log => {
        const matchesSearch = !searchTerm || 
            log.message.toLowerCase().includes(searchTerm) ||
            log.type.toLowerCase().includes(searchTerm) ||
            (log.userId && log.userId.toLowerCase().includes(searchTerm)) ||
            (log.ipAddress && log.ipAddress.toLowerCase().includes(searchTerm));
        
        const matchesSeverity = !severityFilter || log.severity === severityFilter;
        const matchesEvent = !eventFilter || log.type === eventFilter;
        
        return matchesSearch && matchesSeverity && matchesEvent;
    });
    
    // Reset to first page when filtering
    currentSecurityPage = 1;
    displaySecurityLogs();
}

// View Management
function switchView(viewName) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-view="${viewName}"]`).classList.add('active');
    
    // Update views
    document.querySelectorAll('.view').forEach(view => {
        view.classList.remove('active');
    });
    document.getElementById(`${viewName}-view`).classList.add('active');
    
    currentView = viewName;
    
    // Load view-specific data
    switch(viewName) {
        case 'user-management':
            loadUserManagementList();
            break;
        case 'security':
            loadSecurityData();
            break;
    }
}


// User Management Functions
async function viewUser(uid) {
    console.log('DEBUG: viewUser called with UID:', uid);
    try {
        console.log('DEBUG: Making request to /api/users/' + uid);
        const response = await makeAuthenticatedRequest(`/api/users/${uid}`);
        console.log('DEBUG: viewUser response:', response);
        
        if (response && response.ok) {
            const data = await response.json();
            console.log('DEBUG: viewUser data:', data);
            
            if (data.success) {
                const user = data.data || data.user; // Handle both response formats
                console.log('DEBUG: User data to display:', user);
                
                // Show user details in modal
                const modal = document.getElementById('user-modal');
                const details = document.getElementById('user-details');
                
                if (!modal || !details) {
                    console.error('DEBUG: Modal elements not found!', { modal, details });
                    throw new Error('Modal elements not found');
                }
                
                details.innerHTML = `
                    <div class="user-detail-grid">
                        <div class="detail-item">
                            <label>UID:</label>
                            <span>${user.uid}</span>
                        </div>
                        <div class="detail-item">
                            <label>Email:</label>
                            <span>${user.email}</span>
                        </div>
                        <div class="detail-item">
                            <label>Display Name:</label>
                            <span>${user.displayName || 'Not set'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Email Verified:</label>
                            <span class="status-indicator ${user.emailVerified ? 'healthy' : 'warning'}">
                                ${user.emailVerified ? 'Yes' : 'No'}
                            </span>
                        </div>
                        <div class="detail-item">
                            <label>Account Status:</label>
                            <span class="status-indicator ${user.disabled ? 'warning' : 'healthy'}">
                                ${user.disabled ? 'Disabled' : 'Active'}
                            </span>
                        </div>
                        <div class="detail-item">
                            <label>Created:</label>
                            <span>${new Date(user.creationTime).toLocaleString()}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Sign In:</label>
                            <span>${user.lastSignInTime ? new Date(user.lastSignInTime).toLocaleString() : 'Never'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Refresh:</label>
                            <span>${user.lastRefreshTime ? new Date(user.lastRefreshTime).toLocaleString() : 'Never'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Provider Data:</label>
                            <pre>${JSON.stringify(user.providerData || [], null, 2)}</pre>
                        </div>
                        <div class="detail-item">
                            <label>Custom Claims:</label>
                            <pre>${JSON.stringify(user.customClaims || {}, null, 2)}</pre>
                        </div>
                        ${user.additionalData ? `
                        <div class="detail-item">
                            <label>Additional Data:</label>
                            <pre>${JSON.stringify(user.additionalData, null, 2)}</pre>
                        </div>
                        ` : ''}
                    </div>
                `;
                
                console.log('DEBUG: Modal content set, setting up admin actions...');

                // Setup admin actions
                setupAdminActions(user);

                // Load audit logs for this user
                await loadUserAuditLogs(uid);

                // Load transactions for this user
                await loadUserTransactions(uid);

                console.log('DEBUG: Showing modal...');
                modal.classList.remove('hidden');
                console.log('DEBUG: Modal should now be visible');
            } else {
                throw new Error(data.error || 'Failed to load user details');
            }
        } else {
            console.error('DEBUG: Response not OK:', response?.status);
            throw new Error('Failed to fetch user details');
        }
    } catch (error) {
        console.error('DEBUG: viewUser error:', error);
        showToast(`Failed to load user details: ${error.message}`, 'error');
    }
}

async function resetUserPassword(uid) {
    console.log('DEBUG: resetUserPassword called with UID:', uid);
    
    if (!confirm('Are you sure you want to reset this user\'s password? This will send a password reset email to the user.')) {
        console.log('DEBUG: User cancelled password reset');
        return;
    }
    
    try {
        console.log('DEBUG: Making password reset request...');
        const response = await makeAuthenticatedRequest(`/api/users/${uid}/reset-password`, {
            method: 'POST'
        });
        
        console.log('DEBUG: Password reset response:', response);
        
        if (response && response.ok) {
            const data = await response.json();
            console.log('DEBUG: Password reset data:', data);
            
            if (data.success) {
                showToast(`Password reset email sent to ${data.data.email}`, 'success');
                
                // Show additional info if available
                if (data.data.resetLink) {
                    console.log('Password reset link generated:', data.data.resetLink);
                }
            } else {
                throw new Error(data.error || 'Failed to reset password');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            console.error('DEBUG: Password reset failed:', response?.status, errorData);
            throw new Error(errorData.error || `HTTP ${response.status}: Failed to reset password`);
        }
    } catch (error) {
        console.error('DEBUG: resetUserPassword error:', error);
        showToast(`Failed to reset password: ${error.message}`, 'error');
    }
}

// Load user audit logs
async function loadUserAuditLogs(uid) {
    try {
        const response = await makeAuthenticatedRequest(`/api/users/${uid}/audit-logs`);
        if (response && response.ok) {
            const data = await response.json();
            if (data.success) {
                const logs = data.data || [];
                const auditLogsContainer = document.getElementById('user-audit-logs');
                
                auditLogsContainer.innerHTML = '';
                
                if (logs.length === 0) {
                    auditLogsContainer.innerHTML = `
                        <tr>
                            <td colspan="4" style="text-align: center; padding: 20px; color: #666;">
                                No audit logs found for this user
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                logs.forEach(log => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${new Date(log.timestamp).toLocaleString()}</td>
                        <td>${log.action || 'Unknown'}</td>
                        <td>${log.details || 'No details'}</td>
                        <td>${log.ipAddress || '-'}</td>
                    `;
                    auditLogsContainer.appendChild(row);
                });
            }
        }
    } catch (error) {
        console.error('Failed to load user audit logs:', error);
        const auditLogsContainer = document.getElementById('user-audit-logs');
        auditLogsContainer.innerHTML = `
            <tr>
                <td colspan="4" style="text-align: center; padding: 20px; color: #e74c3c;">
                    Failed to load audit logs
                </td>
            </tr>
        `;
    }
}

// Load user transactions
async function loadUserTransactions(uid) {
    try {
        console.log('DEBUG: Loading transactions for user:', uid);
        const response = await makeAuthenticatedRequest(`/api/users/${uid}/transactions`);
        
        if (response && response.ok) {
            const data = await response.json();
            console.log('DEBUG: Transactions response:', data);
            
            if (data.success) {
                const transactions = data.data?.transactions || [];
                const transactionsContainer = document.getElementById('user-transactions');
                const transactionCount = document.getElementById('transaction-count');
                
                console.log('DEBUG: Found', transactions.length, 'transactions');
                
                transactionsContainer.innerHTML = '';
                transactionCount.textContent = `${transactions.length} transactions`;
                
                if (transactions.length === 0) {
                    transactionsContainer.innerHTML = `
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 20px; color: #666;">
                                No transactions found for this user
                            </td>
                        </tr>
                    `;
                    return;
                }
                
                transactions.forEach(transaction => {
                    const row = document.createElement('tr');
                    
                    // Format amount
                    const amount = transaction.amount ? `$${parseFloat(transaction.amount).toFixed(2)}` : '-';
                    
                    // Determine other party (from/to)
                    let otherParty = '-';
                    if (transaction.userRole === 'sender' && transaction.recipientEmail) {
                        otherParty = `To: ${transaction.recipientEmail}`;
                    } else if (transaction.userRole === 'recipient' && transaction.senderEmail) {
                        otherParty = `From: ${transaction.senderEmail}`;
                    } else if (transaction.recipientId && transaction.senderId) {
                        otherParty = transaction.userRole === 'sender' ? 
                            `To: ${transaction.recipientId}` : 
                            `From: ${transaction.senderId}`;
                    }
                    
                    // Format status
                    const status = transaction.status || 'unknown';
                    const statusClass = status === 'completed' ? 'healthy' : 
                                      status === 'pending' ? 'warning' : 
                                      status === 'failed' ? 'error' : 'neutral';
                    
                    // Format transaction type
                    const type = transaction.type || 'transfer';
                    
                    row.innerHTML = `
                        <td>${new Date(transaction.timestamp).toLocaleString()}</td>
                        <td>${type}</td>
                        <td>${amount}</td>
                        <td>${otherParty}</td>
                        <td>${transaction.description || transaction.memo || '-'}</td>
                        <td>
                            <span class="status-indicator ${statusClass}">
                                ${status.charAt(0).toUpperCase() + status.slice(1)}
                            </span>
                        </td>
                        <td>
                            <span class="role-badge ${transaction.userRole}">
                                ${transaction.userRole.charAt(0).toUpperCase() + transaction.userRole.slice(1)}
                            </span>
                        </td>
                    `;
                    transactionsContainer.appendChild(row);
                });
                
                console.log('DEBUG: Transactions loaded successfully');
            } else {
                throw new Error(data.error || 'Failed to load transactions');
            }
        } else {
            throw new Error('Failed to fetch transactions');
        }
    } catch (error) {
        console.error('DEBUG: Failed to load user transactions:', error);
        const transactionsContainer = document.getElementById('user-transactions');
        const transactionCount = document.getElementById('transaction-count');
        
        transactionCount.textContent = '0 transactions';
        transactionsContainer.innerHTML = `
            <tr>
                <td colspan="7" style="text-align: center; padding: 20px; color: #e74c3c;">
                    Failed to load transactions: ${error.message}
                </td>
            </tr>
        `;
    }
}

// Handle modal tab switching
function switchModalTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`user-${tabName}-tab`).classList.add('active');
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    
    // Check if DOM elements exist
    console.log('Loading screen element:', document.getElementById('loading-screen'));
    console.log('Login screen element:', document.getElementById('login-screen'));
    console.log('Dashboard element:', document.getElementById('dashboard'));
    
    // Show loading screen initially
    showLoadingScreen();
    console.log('Loading screen shown, setting timeout...');
    
    // Check for existing authentication after a short delay
    setTimeout(() => {
        console.log('Timeout executed, checking for stored token...');
        const storedToken = localStorage.getItem('authToken');
        if (storedToken) {
            console.log('Found stored token, verifying...');
            authToken = storedToken;
            loadUserInfo().then(success => {
                if (success) {
                    console.log('Token valid, showing dashboard');
                    showDashboard();
                } else {
                    console.log('Token invalid, showing login');
                    localStorage.removeItem('authToken');
                    authToken = null;
                    showLoginScreen();
                }
            }).catch(error => {
                console.error('Error loading user info:', error);
                localStorage.removeItem('authToken');
                authToken = null;
                showLoginScreen();
            });
        } else {
            console.log('No stored token, showing login');
            showLoginScreen();
        }
    }, 500); // 0.5 second delay to show loading screen
    
    // Email form submission
    emailForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        await handleFirebaseLogin(email, password);
    });
    
    // Forgot password link
    document.getElementById('forgot-password-link').addEventListener('click', async function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        if (!email) {
            showMessage('Please enter your email address first', 'error');
            return;
        }
        
        try {
            // Send password reset email using Firebase
            await auth.sendPasswordResetEmail(email);
            showMessage(`Password reset email sent to ${email}. Please check your inbox.`, 'success');
        } catch (error) {
            console.error('Password reset error:', error);
            let errorMessage = 'Failed to send password reset email';
            
            if (error.code === 'auth/user-not-found') {
                errorMessage = 'No account found with this email address';
            } else if (error.code === 'auth/invalid-email') {
                errorMessage = 'Please enter a valid email address';
            } else if (error.code === 'auth/too-many-requests') {
                errorMessage = 'Too many requests. Please try again later';
            }
            
            showMessage(errorMessage, 'error');
        }
    });
    
    // 2FA form submission
    twoFAForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const code = document.getElementById('2fa-code').value;
        await handleTwoFactorVerification(code);
    });
    
    // Setup form submission
    setupForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const code = document.getElementById('setup-code').value;
        await handleTwoFactorSetup(code);
    });
    
    // Navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const view = this.dataset.view;
            switchView(view);
        });
    });
    
    // Logout button
    document.getElementById('logout-btn').addEventListener('click', logout);
    
    // Refresh buttons
    document.getElementById('refresh-btn').addEventListener('click', function() {
        window.location.reload();
    });

    document.getElementById('refresh-security').addEventListener('click', loadSecurityData);
    
    // Modal close
    document.querySelector('.modal-close').addEventListener('click', function() {
        document.getElementById('user-modal').classList.add('hidden');
    });
    
    // Click outside modal to close
    document.getElementById('user-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            this.classList.add('hidden');
        }
    });
    

    
    // Security search and filters
    document.getElementById('security-search').addEventListener('input', filterSecurityLogs);
    document.getElementById('severity-filter').addEventListener('change', filterSecurityLogs);
    document.getElementById('event-filter').addEventListener('change', filterSecurityLogs);
    
    // Security pagination
    document.getElementById('security-prev-page').addEventListener('click', function() {
        if (currentSecurityPage > 1) {
            currentSecurityPage--;
            displaySecurityLogs();
        }
    });
    
    document.getElementById('security-next-page').addEventListener('click', function() {
        const totalPages = Math.ceil(filteredSecurityLogs.length / securityLogsPerPage);
        if (currentSecurityPage < totalPages) {
            currentSecurityPage++;
            displaySecurityLogs();
        }
    });
    
    // Store auth token when it changes
    const originalSetAuthToken = (token) => {
        authToken = token;
        if (token) {
            localStorage.setItem('authToken', token);
        } else {
            localStorage.removeItem('authToken');
        }
    };
    
    // Support tickets card click handler
    document.getElementById('support-tickets-card').addEventListener('click', function() {
        window.open('https://dashfinanceapp.freshdesk.com/a/dashboard/default', '_blank');
    });

    // Broadcast notification card click handler
    document.getElementById('broadcast-notification-card').addEventListener('click', function() {
        console.log('Broadcast notification card clicked');
        openBroadcastModal();
    });

    // Support tickets navigation click handler
    document.getElementById('support-tickets-nav').addEventListener('click', function(e) {
        e.preventDefault();
        window.open('https://dashfinanceapp.freshdesk.com/a/dashboard/default', '_blank');
    });
    
    // Modal tab switching
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('tab-btn')) {
            const tabName = e.target.getAttribute('data-tab');
            if (tabName) {
                switchModalTab(tabName);
            }
        }
    });
    
    // Event delegation for dynamically created buttons
    document.addEventListener('click', function(e) {
        // Handle FreshDesk open button
        if (e.target.id === 'open-freshdesk-btn') {
            const url = e.target.getAttribute('data-url');
            if (url) {
                window.open(url, '_blank');
            }
        }
        
        // Handle view tickets button
        if (e.target.id === 'view-tickets-btn') {
            loadTicketsList();
        }
        
        // Handle view user button
        if (e.target.classList.contains('view-user-btn')) {
            const uid = e.target.getAttribute('data-uid');
            if (uid) {
                console.log('DEBUG: View user button clicked for UID:', uid);
                viewUser(uid);
            }
        }
        
        // Handle reset password button
        if (e.target.classList.contains('reset-password-btn')) {
            const uid = e.target.getAttribute('data-uid');
            if (uid) {
                console.log('DEBUG: Reset password button clicked for UID:', uid);
                resetUserPassword(uid);
            }
        }
    });

    // User Management Event Listeners
    const refreshUserListBtn = document.getElementById('refresh-user-list');
    const userManagementSearch = document.getElementById('user-management-search');
    const userManagementNav = document.querySelector('[data-view="user-management"]');

    if (refreshUserListBtn) {
        refreshUserListBtn.addEventListener('click', function() {
            loadUserManagementList();
        });
    }

    if (userManagementSearch) {
        userManagementSearch.addEventListener('input', function(e) {
            filterUserManagementList(e.target.value);
        });
    }

    if (userManagementNav) {
        userManagementNav.addEventListener('click', function() {
            setTimeout(() => loadUserManagementList(), 100);
        });
    }

    // Admin 2FA Modal Event Listeners
    const admin2FAModal = document.getElementById('admin-2fa-modal');
    const admin2FAClose = document.getElementById('admin-2fa-close');
    const admin2FACancel = document.getElementById('admin-2fa-cancel');
    const admin2FAForm = document.getElementById('admin-2fa-form');

    if (admin2FAClose) {
        admin2FAClose.addEventListener('click', function() {
            admin2FAModal.classList.add('hidden');
            currentUserForAdminAction = null;
        });
    }

    if (admin2FACancel) {
        admin2FACancel.addEventListener('click', function() {
            admin2FAModal.classList.add('hidden');
            currentUserForAdminAction = null;
        });
    }

    if (admin2FAForm) {
        admin2FAForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await confirmAdminAction();
        });
    }

    // Click outside modal to close
    if (admin2FAModal) {
        admin2FAModal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
                currentUserForAdminAction = null;
            }
        });
    }

    // Edit User Form Event Listeners
    const cancelEditUserBtn = document.getElementById('cancel-edit-user');
    const cancelEditUser2Btn = document.getElementById('cancel-edit-user-2');
    const userEditForm = document.getElementById('user-edit-form');

    if (cancelEditUserBtn) {
        cancelEditUserBtn.addEventListener('click', function() {
            hideEditUserForm();
        });
    }

    if (cancelEditUser2Btn) {
        cancelEditUser2Btn.addEventListener('click', function() {
            hideEditUserForm();
        });
    }

    if (userEditForm) {
        userEditForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            await saveUserChanges();
        });
    }
});

// Firebase Auth State Observer
auth.onAuthStateChanged(function(user) {
    if (user) {
        console.log('Firebase user signed in:', user.email);
    } else {
        console.log('Firebase user signed out');
        // Only logout if we're currently authenticated
        if (authToken) {
            logout();
        }
    }
});

// Broadcast Notification Functions
function openBroadcastModal() {
    const modal = document.getElementById('broadcast-modal');
    modal.classList.remove('hidden');

    // Reset form
    document.getElementById('broadcast-form').reset();
    document.getElementById('send-immediately').checked = true;

    // Set default expiration to 7 days from now
    const now = new Date();
    const defaultExpiration = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000)); // 7 days

    document.getElementById('expiration-date').value = defaultExpiration.toISOString().split('T')[0];
    document.getElementById('expiration-time').value = '23:59';

    updateNotificationPreview();

    // Add event listeners
    setupBroadcastModalEventListeners();
}

function closeBroadcastModal() {
    const modal = document.getElementById('broadcast-modal');
    modal.classList.add('hidden');

    // Remove event listeners
    removeBroadcastModalEventListeners();
}

function setupBroadcastModalEventListeners() {
    // Close modal handlers
    document.getElementById('broadcast-modal-close').addEventListener('click', closeBroadcastModal);
    document.getElementById('broadcast-cancel').addEventListener('click', closeBroadcastModal);

    // Form submission
    document.getElementById('broadcast-form').addEventListener('submit', handleBroadcastSubmit);

    // Preview update handlers
    document.getElementById('notification-title').addEventListener('input', updateNotificationPreview);
    document.getElementById('notification-message').addEventListener('input', updateNotificationPreview);
    document.getElementById('notification-type').addEventListener('change', updateNotificationPreview);

    // Click outside to close
    document.getElementById('broadcast-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeBroadcastModal();
        }
    });
}

function removeBroadcastModalEventListeners() {
    // Remove all event listeners to prevent memory leaks
    const newCloseBtn = document.getElementById('broadcast-modal-close').cloneNode(true);
    document.getElementById('broadcast-modal-close').parentNode.replaceChild(newCloseBtn, document.getElementById('broadcast-modal-close'));

    const newCancelBtn = document.getElementById('broadcast-cancel').cloneNode(true);
    document.getElementById('broadcast-cancel').parentNode.replaceChild(newCancelBtn, document.getElementById('broadcast-cancel'));

    const newForm = document.getElementById('broadcast-form').cloneNode(true);
    document.getElementById('broadcast-form').parentNode.replaceChild(newForm, document.getElementById('broadcast-form'));
}

function updateNotificationPreview() {
    const title = document.getElementById('notification-title').value || 'Notification Title';
    const message = document.getElementById('notification-message').value || 'Your message will appear here...';
    const type = document.getElementById('notification-type').value;

    document.getElementById('preview-title').textContent = title;
    document.getElementById('preview-message').textContent = message;

    // Update preview icon based on type
    const previewIcon = document.querySelector('.preview-icon');
    const typeIcons = {
        'system_announcement': '📢',
        'maintenance': '🔧',
        'security_alert': '🔒',
        'feature_update': '✨',
        'general': '📝'
    };

    previewIcon.textContent = typeIcons[type] || '📢';
}

async function handleBroadcastSubmit(e) {
    e.preventDefault();

    const submitBtn = document.getElementById('broadcast-send');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');

    // Show loading state
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;

    try {
        // Combine date and time for expiration
        const expirationDate = document.getElementById('expiration-date').value;
        const expirationTime = document.getElementById('expiration-time').value;
        const expirationDateTime = new Date(`${expirationDate}T${expirationTime}`);

        const formData = {
            title: document.getElementById('notification-title').value,
            message: document.getElementById('notification-message').value,
            type: document.getElementById('notification-type').value,
            priority: document.getElementById('notification-priority').value,
            sendImmediately: document.getElementById('send-immediately').checked,
            expiresAt: expirationDateTime.toISOString()
        };

        console.log('Sending broadcast notification:', formData);

        const response = await makeAuthenticatedRequest('/api/dashboard/broadcast-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        if (response && response.ok) {
            const result = await response.json();
            if (result.success) {
                showToast('Notification sent successfully to all users!', 'success');
                closeBroadcastModal();
            } else {
                throw new Error(result.message || 'Failed to send notification');
            }
        } else {
            throw new Error('Failed to send notification');
        }

    } catch (error) {
        console.error('Error sending broadcast notification:', error);
        showToast('Failed to send notification: ' + error.message, 'error');
    } finally {
        // Hide loading state
        submitBtn.classList.remove('loading');
        submitBtn.disabled = false;
    }
}

// Admin Management Functions
let currentUserForAdminAction = null;

async function makeUserAdmin(userEmail, userUid, userName) {
    try {
        // Store user info for the action
        currentUserForAdminAction = { email: userEmail, uid: userUid, name: userName };

        // Show 2FA confirmation modal
        showAdmin2FAModal('make_admin', userEmail, userName);

    } catch (error) {
        console.error('Make admin error:', error);
        showToast(`Failed to initiate admin action: ${error.message}`, 'error');
    }
}

async function revokeUserAdmin(userEmail, userUid, userName) {
    try {
        // Store user info for the action
        currentUserForAdminAction = { email: userEmail, uid: userUid, name: userName };

        // Show 2FA confirmation modal
        showAdmin2FAModal('revoke_admin', userEmail, userName);

    } catch (error) {
        console.error('Revoke admin error:', error);
        showToast(`Failed to initiate admin action: ${error.message}`, 'error');
    }
}

function showAdmin2FAModal(action, userEmail, userName) {
    const modal = document.getElementById('admin-2fa-modal');
    const actionDetails = document.getElementById('admin-action-details');
    const form = document.getElementById('admin-2fa-form');

    // Generate user initials
    const initials = generateInitials(userName || extractNameFromEmail(userEmail));

    // Set action details
    if (action === 'make_admin') {
        actionDetails.innerHTML = `
            <h4>🔐 Grant Admin Privileges</h4>
            <p>You are about to grant administrative privileges to:</p>
            <div class="user-info">
                <div class="user-avatar">${initials}</div>
                <div class="user-details">
                    <div class="user-name">${userName || extractNameFromEmail(userEmail)}</div>
                    <div class="user-email">${userEmail}</div>
                </div>
            </div>
            <div class="env-var-notice">
                <h4>📝 Manual Step Required</h4>
                <p>After confirming, you must manually add this email to your TrueNAS environment variable:</p>
                <p><code>ADMIN_EMAILS = <EMAIL>,<EMAIL>,${userEmail}</code></p>
                <p>Then restart the application for the changes to take effect.</p>
            </div>
        `;
    } else if (action === 'revoke_admin') {
        actionDetails.innerHTML = `
            <h4>🚫 Revoke Admin Privileges</h4>
            <p>You are about to revoke administrative privileges from:</p>
            <div class="user-info">
                <div class="user-avatar">${initials}</div>
                <div class="user-details">
                    <div class="user-name">${userName || extractNameFromEmail(userEmail)}</div>
                    <div class="user-email">${userEmail}</div>
                </div>
            </div>
            <div class="env-var-notice">
                <h4>📝 Manual Step Required</h4>
                <p>After confirming, you must manually remove this email from your TrueNAS environment variable:</p>
                <p><code>ADMIN_EMAILS</code> (remove <code>${userEmail}</code>)</p>
                <p>Then restart the application for the changes to take effect.</p>
            </div>
        `;
    } else if (action === 'edit_user') {
        const newData = currentUserForAdminAction?.newData;
        const changes = [];

        if (newData) {
            if (newData.email !== userEmail) {
                changes.push(`Email: ${userEmail} → ${newData.email}`);
            }
            if (newData.displayName !== (currentUserForEdit?.displayName || '')) {
                changes.push(`Name: ${currentUserForEdit?.displayName || 'Not set'} → ${newData.displayName || 'Not set'}`);
            }
            if (newData.disabled !== currentUserForEdit?.disabled) {
                changes.push(`Status: ${currentUserForEdit?.disabled ? 'Disabled' : 'Active'} → ${newData.disabled ? 'Disabled' : 'Active'}`);
            }
            if (newData.emailVerified !== currentUserForEdit?.emailVerified) {
                changes.push(`Email Verified: ${currentUserForEdit?.emailVerified ? 'Yes' : 'No'} → ${newData.emailVerified ? 'Yes' : 'No'}`);
            }
        }

        actionDetails.innerHTML = `
            <h4>✏️ Edit User Information</h4>
            <p>You are about to modify the account information for:</p>
            <div class="user-info">
                <div class="user-avatar">${initials}</div>
                <div class="user-details">
                    <div class="user-name">${userName || extractNameFromEmail(userEmail)}</div>
                    <div class="user-email">${userEmail}</div>
                </div>
            </div>
            <div class="env-var-notice">
                <h4>📝 Changes to be made:</h4>
                ${changes.map(change => `<p>• ${change}</p>`).join('')}
            </div>
        `;
    }

    // Reset form
    form.reset();

    // Store action type
    form.dataset.action = action;

    // Show modal
    modal.classList.remove('hidden');
    document.getElementById('admin-2fa-code').focus();
}

async function confirmAdminAction() {
    try {
        const form = document.getElementById('admin-2fa-form');
        const code = document.getElementById('admin-2fa-code').value;
        const action = form.dataset.action;

        if (!code || code.length !== 6) {
            showToast('Please enter a valid 6-digit 2FA code', 'error');
            return;
        }

        if (!currentUserForAdminAction) {
            showToast('No user selected for admin action', 'error');
            return;
        }

        const confirmButton = document.getElementById('admin-2fa-confirm');
        const buttonText = confirmButton.querySelector('.btn-text');
        const buttonLoading = confirmButton.querySelector('.btn-loading');

        // Show loading state
        confirmButton.disabled = true;
        buttonText.classList.add('hidden');
        buttonLoading.classList.remove('hidden');

        // Make API request based on action type
        let endpoint, requestBody, successMessage;

        if (action === 'make_admin') {
            endpoint = '/api/auth/admin/grant';
            requestBody = {
                email: currentUserForAdminAction.email,
                twoFactorCode: code
            };
            successMessage = `Admin privileges granted successfully for ${currentUserForAdminAction.email}`;
        } else if (action === 'revoke_admin') {
            endpoint = '/api/auth/admin/revoke';
            requestBody = {
                email: currentUserForAdminAction.email,
                twoFactorCode: code
            };
            successMessage = `Admin privileges revoked successfully for ${currentUserForAdminAction.email}`;
        } else if (action === 'edit_user') {
            endpoint = `/api/users/${currentUserForAdminAction.uid}`;
            requestBody = {
                ...currentUserForAdminAction.newData,
                twoFactorCode: code
            };
            successMessage = `User information updated successfully for ${currentUserForAdminAction.email}`;
        }

        const response = await makeAuthenticatedRequest(endpoint, {
            method: action === 'edit_user' ? 'PATCH' : 'POST',
            body: JSON.stringify(requestBody)
        });

        if (response && response.ok) {
            const data = await response.json();
            if (data.success) {
                showToast(successMessage, 'success');

                // Close modal
                document.getElementById('admin-2fa-modal').classList.add('hidden');

                // Hide edit form if editing
                if (action === 'edit_user') {
                    hideEditUserForm();
                }

                // Refresh user details
                if (document.getElementById('user-modal').classList.contains('hidden') === false) {
                    await viewUser(currentUserForAdminAction.uid);
                }

                // Show environment variable reminder for admin actions
                if (action === 'make_admin' || action === 'revoke_admin') {
                    showEnvironmentVariableReminder(action, currentUserForAdminAction.email);
                }

            } else {
                throw new Error(data.error || 'Failed to complete action');
            }
        } else {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || 'Failed to complete action');
        }

    } catch (error) {
        console.error('Confirm admin action error:', error);
        showToast(`Failed to update admin privileges: ${error.message}`, 'error');
    } finally {
        // Reset button state
        const confirmButton = document.getElementById('admin-2fa-confirm');
        const buttonText = confirmButton.querySelector('.btn-text');
        const buttonLoading = confirmButton.querySelector('.btn-loading');

        confirmButton.disabled = false;
        buttonText.classList.remove('hidden');
        buttonLoading.classList.add('hidden');

        // Clear current user
        currentUserForAdminAction = null;
    }
}

function showEnvironmentVariableReminder(action, email) {
    const actionText = action === 'make_admin' ? 'add' : 'remove';
    const instruction = action === 'make_admin'
        ? `Add "${email}" to your ADMIN_EMAILS environment variable`
        : `Remove "${email}" from your ADMIN_EMAILS environment variable`;

    showToast(`⚠️ Don't forget to ${instruction} in TrueNAS and restart the app!`, 'warning');
}

// User Edit Functions
let currentUserForEdit = null;

function showEditUserForm(user) {
    currentUserForEdit = user;

    // Hide user details and show edit form
    document.getElementById('user-details').style.display = 'none';
    document.getElementById('user-admin-actions').style.display = 'none';
    document.getElementById('edit-user-form').style.display = 'block';

    // Populate form with current user data
    document.getElementById('edit-user-email').value = user.email || '';
    document.getElementById('edit-user-name').value = user.displayName || '';
    document.getElementById('edit-user-status').value = user.disabled ? 'true' : 'false';
    document.getElementById('edit-user-verified').value = user.emailVerified ? 'true' : 'false';
}

function hideEditUserForm() {
    // Show user details and hide edit form
    document.getElementById('user-details').style.display = 'block';
    document.getElementById('user-admin-actions').style.display = 'block';
    document.getElementById('edit-user-form').style.display = 'none';

    // Clear form
    document.getElementById('user-edit-form').reset();
    currentUserForEdit = null;
}

async function saveUserChanges() {
    try {
        if (!currentUserForEdit) {
            showToast('No user selected for editing', 'error');
            return;
        }

        const form = document.getElementById('user-edit-form');
        const formData = new FormData(form);

        const userData = {
            email: formData.get('email'),
            displayName: formData.get('displayName'),
            disabled: formData.get('disabled') === 'true',
            emailVerified: formData.get('emailVerified') === 'true'
        };

        // Basic email validation (no domain restriction for user editing)
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(userData.email)) {
            showToast('Please enter a valid email address', 'error');
            return;
        }

        // Check if any changes were made
        const hasChanges =
            userData.email !== currentUserForEdit.email ||
            userData.displayName !== (currentUserForEdit.displayName || '') ||
            userData.disabled !== currentUserForEdit.disabled ||
            userData.emailVerified !== currentUserForEdit.emailVerified;

        if (!hasChanges) {
            showToast('No changes detected', 'info');
            hideEditUserForm();
            return;
        }

        // Store user data for 2FA confirmation
        currentUserForAdminAction = {
            uid: currentUserForEdit.uid,
            email: currentUserForEdit.email,
            name: currentUserForEdit.displayName || extractNameFromEmail(currentUserForEdit.email),
            newData: userData
        };

        // Show 2FA confirmation modal for user edit
        showAdmin2FAModal('edit_user', currentUserForEdit.email, currentUserForEdit.displayName || extractNameFromEmail(currentUserForEdit.email));

    } catch (error) {
        console.error('Save user changes error:', error);
        showToast(`Failed to save changes: ${error.message}`, 'error');
    }
}

function setupAdminActions(user) {
    const adminActionsSection = document.getElementById('user-admin-actions');
    const editUserBtn = document.getElementById('edit-user-btn');
    const makeAdminBtn = document.getElementById('make-admin-btn');
    const revokeAdminBtn = document.getElementById('revoke-admin-btn');

    if (!adminActionsSection || !editUserBtn || !makeAdminBtn || !revokeAdminBtn) {
        console.error('Admin actions elements not found');
        return;
    }

    // Check if user is already an admin
    const isAdmin = user.customClaims?.admin === true;
    const isCurrentUser = user.email === currentUser?.email;

    // Show admin actions section (edit is always available)
    adminActionsSection.style.display = 'block';

    // Setup edit user button (always available)
    editUserBtn.onclick = () => {
        showEditUserForm(user);
    };

    // Setup admin privilege buttons
    if (isCurrentUser) {
        // Current user can edit themselves but not change admin status
        makeAdminBtn.style.display = 'none';
        revokeAdminBtn.style.display = 'none';
    } else {
        // Other users - show appropriate admin buttons
        if (isAdmin) {
            makeAdminBtn.style.display = 'none';
            revokeAdminBtn.style.display = 'flex';

            // Setup revoke admin button
            revokeAdminBtn.onclick = () => {
                revokeUserAdmin(user.email, user.uid, user.displayName || extractNameFromEmail(user.email));
            };
        } else {
            makeAdminBtn.style.display = 'flex';
            revokeAdminBtn.style.display = 'none';

            // Setup make admin button
            makeAdminBtn.onclick = () => {
                makeUserAdmin(user.email, user.uid, user.displayName || extractNameFromEmail(user.email));
            };
        }
    }
}

async function loadUserManagementList() {
    try {
        const listContainer = document.getElementById('user-management-list');
        listContainer.innerHTML = `
            <div class="loading-state">
                <div class="spinner"></div>
                <p>Loading users...</p>
            </div>
        `;

        const response = await makeAuthenticatedRequest('/api/users');
        if (response && response.ok) {
            const data = await response.json();
            if (data.success) {
                const users = data.data?.users || [];
                displayUserManagementList(users);
            } else {
                throw new Error(data.error || 'Failed to load users');
            }
        } else {
            throw new Error('Failed to fetch users');
        }
    } catch (error) {
        console.error('Failed to load user management list:', error);
        const listContainer = document.getElementById('user-management-list');
        listContainer.innerHTML = `
            <div class="user-item">
                <div class="user-item-info">
                    <div class="user-item-avatar" style="background: #e74c3c;">⚠️</div>
                    <div class="user-item-details">
                        <div class="user-item-name">Error Loading Users</div>
                        <div class="user-item-email">${error.message}</div>
                    </div>
                </div>
            </div>
        `;
    }
}

let allUsers = [];

function displayUserManagementList(users) {
    allUsers = users;
    const listContainer = document.getElementById('user-management-list');

    if (users.length === 0) {
        listContainer.innerHTML = `
            <div class="user-item">
                <div class="user-item-info">
                    <div class="user-item-avatar">👤</div>
                    <div class="user-item-details">
                        <div class="user-item-name">No Users Found</div>
                        <div class="user-item-email">No users found in the system</div>
                    </div>
                </div>
            </div>
        `;
        return;
    }

    listContainer.innerHTML = '';

    users.forEach(user => {
        const userItem = document.createElement('div');
        userItem.className = 'user-item';

        const initials = generateInitials(user.displayName || extractNameFromEmail(user.email));
        const isActive = !user.disabled;
        const isAdmin = user.customClaims?.admin === true;

        userItem.innerHTML = `
            <div class="user-item-info">
                <div class="user-item-avatar">${initials}</div>
                <div class="user-item-details">
                    <div class="user-item-name">
                        ${user.displayName || extractNameFromEmail(user.email)}
                        ${isAdmin ? '<span style="color: #667eea; font-size: 12px; margin-left: 8px;">👑 Admin</span>' : ''}
                    </div>
                    <div class="user-item-email">${user.email}</div>
                    <div class="user-item-role">${user.customClaims?.role || 'user'}</div>
                </div>
            </div>
            <div class="user-item-actions">
                <div class="user-item-status ${isActive ? 'active' : 'inactive'}">
                    ${isActive ? 'Active' : 'Inactive'}
                </div>
                <button class="btn-text view-user-btn" data-uid="${user.uid}">View</button>
                <button class="btn-text reset-password-btn" data-uid="${user.uid}">Reset Password</button>
            </div>
        `;

        listContainer.appendChild(userItem);
    });
}

function filterUserManagementList(searchTerm) {
    if (!searchTerm) {
        displayUserManagementList(allUsers);
        return;
    }

    const filteredUsers = allUsers.filter(user => {
        const name = (user.displayName || extractNameFromEmail(user.email)).toLowerCase();
        const email = user.email.toLowerCase();
        const role = (user.customClaims?.role || 'user').toLowerCase();

        return name.includes(searchTerm.toLowerCase()) ||
               email.includes(searchTerm.toLowerCase()) ||
               role.includes(searchTerm.toLowerCase());
    });

    displayUserManagementList(filteredUsers);
}

// Global error handler
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    showToast('An unexpected error occurred', 'error');
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    showToast('An unexpected error occurred', 'error');
});
