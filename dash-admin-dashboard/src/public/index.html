<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <title>Dash Admin Dashboard</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="./styles.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="./firebase-config.js"></script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="dash-logo">
                <img src="./dashlogo.png" alt="Dash Logo" class="logo-image">
                <span>ash</span>
            </div>
            <div class="loading-spinner"></div>
            <p>Loading Dashboard...</p>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="login-screen hidden">
        <div class="login-container">
        <div class="login-header">
          <div class="dash-logo">
            <img src="./dashlogo-black.png" alt="Dash Logo" class="logo-image">
          </div>
        </div>

            <div class="login-form">
                <div id="login-step-email" class="login-step">
                    <div class="step-header">
                        <div class="step-icon">🔐</div>
                        <h2>Sign In</h2>
                        <p>Enter your credentials to continue</p>
                    </div>
                    <form id="email-form">
                        <div class="input-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" placeholder="Enter your email" required>
                        </div>
                        <div class="input-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" placeholder="Enter your password" required>
                        </div>
                        <button type="submit" class="btn-primary">
                            <span class="btn-text">Sign In</span>
                            <span class="btn-loading hidden">Signing In...</span>
                        </button>
                        <div class="forgot-password">
                            <a href="#" id="forgot-password-link">Forgot your password?</a>
                        </div>
                    </form>
                </div>

                <div id="login-step-2fa" class="login-step hidden">
                    <div class="step-header">
                        <div class="step-icon">📱</div>
                        <h2>Two-Factor Authentication</h2>
                        <p>Enter the 6-digit code from your authenticator app</p>
                        <div class="step-progress">
                            <div class="progress-step completed"></div>
                            <div class="progress-step active"></div>
                        </div>
                    </div>
                    <form id="2fa-form">
                        <div class="input-group">
                            <label for="2fa-code">Authentication Code</label>
                            <input type="text" id="2fa-code" placeholder="000000" maxlength="6" pattern="[0-9]{6}" required autocomplete="one-time-code">
                            <small>Enter the code from your authenticator app (Google Authenticator, Authy, etc.)</small>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="showLoginStep('email')">Back</button>
                            <button type="submit" class="btn-primary">
                                <span class="btn-text">Verify Code</span>
                                <span class="btn-loading hidden">Verifying...</span>
                            </button>
                        </div>
                    </form>
                </div>

                <div id="login-step-setup" class="login-step hidden">
                    <div class="step-header">
                        <div class="step-icon">⚙️</div>
                        <h2>Setup Two-Factor Authentication</h2>
                        <p>Secure your account with 2FA for enhanced security</p>
                        <div class="step-progress">
                            <div class="progress-step completed"></div>
                            <div class="progress-step active"></div>
                        </div>
                    </div>
                    
                    <div class="setup-instructions">
                        <div class="instruction-step">
                            <span class="step-number">1</span>
                            <div class="step-content">
                                <h4>Install an Authenticator App</h4>
                                <p>Download Google Authenticator, Authy, or similar app on your phone</p>
                            </div>
                        </div>
                        
                        <div class="instruction-step">
                            <span class="step-number">2</span>
                            <div class="step-content">
                                <h4>Scan QR Code</h4>
                                <div class="qr-container">
                                    <img id="qr-code" src="" alt="QR Code">
                                </div>
                                <div class="backup-secret">
                                    <p><strong>Manual entry key (save this!):</strong></p>
                                    <div class="secret-box">
                                        <code id="backup-secret-text"></code>
                                        <button type="button" class="copy-btn" onclick="copySecret()">Copy</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="instruction-step">
                            <span class="step-number">3</span>
                            <div class="step-content">
                                <h4>Enter Verification Code</h4>
                                <form id="setup-form">
                                    <div class="input-group">
                                        <label for="setup-code">Verification Code</label>
                                        <input type="text" id="setup-code" placeholder="000000" maxlength="6" pattern="[0-9]{6}" required autocomplete="one-time-code">
                                        <small>Enter the 6-digit code from your authenticator app</small>
                                    </div>
                                    <div class="form-actions">
                                        <button type="button" class="btn-secondary" onclick="showLoginStep('email')">Back</button>
                                        <button type="submit" class="btn-primary">
                                            <span class="btn-text">Complete Setup</span>
                                            <span class="btn-loading hidden">Setting Up...</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="login-message" class="message hidden"></div>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="dashboard hidden">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="dash-logo">
                    <img src="./dashlogo-black.png" alt="Dash Logo" class="logo-image">
                    <span>Admin</span>
                </div>
            </div>

            <div class="sidebar-nav">
                <a href="#" class="nav-item active" data-view="overview">
                    <span class="nav-icon">📊</span>
                    <span>Overview</span>
                </a>
                <a href="#" class="nav-item" data-view="user-management">
                    <span class="nav-icon">👥</span>
                    <span>User Management</span>
                </a>
                <a href="#" class="nav-item" id="support-tickets-nav" style="cursor: pointer;">
                    <span class="nav-icon">🎫</span>
                    <span>Support Tickets</span>
                </a>
                <a href="#" class="nav-item" data-view="security">
                    <span class="nav-icon">🔒</span>
                    <span>Security</span>
                </a>
            </div>

            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <span id="user-initials">RS</span>
                    </div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">Ricky Schwartz</div>
                        <div class="user-email" id="user-email"><EMAIL></div>
                        <div class="user-role">Administrator</div>
                    </div>
                </div>
                <button id="logout-btn" class="logout-btn" title="Sign Out">
                    <span class="logout-icon">↗</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Overview View -->
            <div id="overview-view" class="view active">
                <header class="view-header">
                    <h1>Dashboard Overview</h1>
                    <div class="header-actions">
                        <button class="btn-secondary" id="refresh-btn">Refresh</button>
                    </div>
                </header>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">👥</div>
                        <div class="metric-content">
                            <h3>Total Users</h3>
                            <div class="metric-value" id="total-users">-</div>
                            <div class="metric-change positive">+12% this month</div>
                        </div>
                    </div>

                    <div class="metric-card" id="support-tickets-card" style="cursor: pointer;">
                        <div class="metric-icon">🎫</div>
                        <div class="metric-content">
                            <h3>Support Tickets</h3>
                            <div class="metric-value" id="support-tickets">-</div>
                            <div class="metric-change neutral">Click to open FreshDesk</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🔒</div>
                        <div class="metric-content">
                            <h3>Security Incidents</h3>
                            <div class="metric-value" id="security-incidents">-</div>
                            <div class="metric-change negative">2 critical</div>
                        </div>
                    </div>

                    <div class="metric-card" id="broadcast-notification-card" style="cursor: pointer;">
                        <div class="metric-icon">📢</div>
                        <div class="metric-content">
                            <h3>Broadcast Notification</h3>
                            <div class="metric-value">Send to All</div>
                            <div class="metric-change neutral">Click to send system message</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">⚡</div>
                        <div class="metric-content">
                            <h3>System Status</h3>
                            <div class="metric-value">
                                <span class="status-indicator healthy" id="system-status">Healthy</span>
                            </div>
                            <div class="metric-change positive">All systems operational</div>
                        </div>
                    </div>
                </div>

                <div class="content-grid">
                    <div class="card">
                        <div class="card-header">
                            <h2>Recent Activity</h2>
                        </div>
                        <div class="card-content">
                            <div id="recent-activity" class="activity-list">
                                <div class="activity-item">
                                    <div class="activity-icon">👤</div>
                                    <div class="activity-content">
                                        <p>New user registered: <EMAIL></p>
                                        <small>2 minutes ago</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h2>System Health</h2>
                        </div>
                        <div class="card-content">
                            <div class="health-status">
                                <div class="health-item">
                                    <span>Server</span>
                                    <span class="status-indicator healthy">Healthy</span>
                                </div>
                                <div class="health-item">
                                    <span>Firebase</span>
                                    <span class="status-indicator healthy" id="firebase-status">Healthy</span>
                                </div>
                                <div class="health-item">
                                    <span>FreshDesk</span>
                                    <span class="status-indicator healthy" id="freshdesk-status">Healthy</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Management View -->
            <div id="user-management-view" class="view">
                <header class="view-header">
                    <h1>User Management</h1>
                    <div class="header-actions">
                        <button class="btn-secondary" id="refresh-user-list">Refresh</button>
                    </div>
                </header>

                <div class="view-content">
                    <!-- User List -->
                    <div class="card">
                        <div class="card-header">
                            <h3>All Users</h3>
                            <div class="header-actions">
                                <input type="search" id="user-management-search" placeholder="Search users..." class="search-input">
                            </div>
                        </div>
                        <div class="card-content">
                            <div id="user-management-list" class="user-list">
                                <div class="loading-state">
                                    <div class="spinner"></div>
                                    <p>Loading users...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security View -->
            <div id="security-view" class="view">
                <header class="view-header">
                    <h1>Security Dashboard</h1>
                    <div class="header-actions">
                        <button class="btn-secondary" id="refresh-security">Refresh</button>
                    </div>
                </header>

                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-icon">⚠️</div>
                        <div class="metric-content">
                            <h3>Total Incidents</h3>
                            <div class="metric-value" id="total-incidents">-</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🚨</div>
                        <div class="metric-content">
                            <h3>Critical (24h)</h3>
                            <div class="metric-value" id="critical-incidents">-</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🔐</div>
                        <div class="metric-content">
                            <h3>Failed Logins (24h)</h3>
                            <div class="metric-value" id="failed-logins">-</div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <div class="metric-icon">🕵️</div>
                        <div class="metric-content">
                            <h3>Suspicious Activity (24h)</h3>
                            <div class="metric-value" id="suspicious-activity">-</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h2>Security Logs</h2>
                        <div class="card-actions">
                            <input type="search" id="security-search" placeholder="Search logs..." class="search-input">
                            <select id="severity-filter" class="filter-select">
                                <option value="">All Severities</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="warning">Warning</option>
                                <option value="info">Info</option>
                            </select>
                            <select id="event-filter" class="filter-select">
                                <option value="">All Events</option>
                                <option value="login_failed">Failed Logins</option>
                                <option value="suspicious_activity">Suspicious Activity</option>
                                <option value="2fa_failed">2FA Failed</option>
                                <option value="password_changed">Password Changed</option>
                                <option value="account_locked">Account Locked</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Type</th>
                                        <th>Severity</th>
                                        <th>Message</th>
                                        <th>IP Address</th>
                                        <th>User ID</th>
                                    </tr>
                                </thead>
                                <tbody id="security-logs-body">
                                    <!-- Security logs will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        <div class="table-footer">
                            <div class="table-info">
                                <span id="security-logs-count">0 logs</span>
                            </div>
                            <div class="table-pagination">
                                <button id="security-prev-page" class="btn-secondary btn-sm" disabled>Previous</button>
                                <span id="security-page-info">Page 1 of 1</span>
                                <button id="security-next-page" class="btn-secondary btn-sm" disabled>Next</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- User Detail Modal -->
    <div id="user-modal" class="modal hidden">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2>User Details</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="modal-tabs">
                    <button class="tab-btn active" data-tab="details">Details</button>
                    <button class="tab-btn" data-tab="audit">Audit Logs</button>
                    <button class="tab-btn" data-tab="transactions">Transactions</button>
                </div>
                <div id="user-details-tab" class="tab-content active">
                    <div id="user-details">
                        <!-- User details will be loaded here -->
                    </div>

                    <!-- Edit User Form -->
                    <div id="edit-user-form" class="edit-form-section" style="display: none;">
                        <div class="edit-form-header">
                            <h4>Edit User Information</h4>
                            <button class="btn-text" id="cancel-edit-user">Cancel</button>
                        </div>
                        <form id="user-edit-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-user-email">Email Address</label>
                                    <input type="email" id="edit-user-email" name="email" required
                                           placeholder="<EMAIL>" class="form-input">
                                    <small class="form-help">Any valid email address (domain restriction only applies to admin accounts)</small>
                                </div>

                                <div class="form-group">
                                    <label for="edit-user-name">Display Name</label>
                                    <input type="text" id="edit-user-name" name="displayName"
                                           placeholder="John Doe" class="form-input">
                                    <small class="form-help">Updates both Firebase Auth and app database for immediate effect</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-user-status">Account Status</label>
                                    <select id="edit-user-status" name="disabled" class="form-input">
                                        <option value="false">Active</option>
                                        <option value="true">Disabled</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="edit-user-verified">Email Verified</label>
                                    <select id="edit-user-verified" name="emailVerified" class="form-input">
                                        <option value="true">Verified</option>
                                        <option value="false">Unverified</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn-primary">Save Changes</button>
                                <button type="button" class="btn-secondary" id="cancel-edit-user-2">Cancel</button>
                            </div>
                        </form>
                    </div>

                    <div id="user-admin-actions" class="admin-actions-section" style="display: none;">
                        <div class="admin-actions-header">
                            <h4>Admin Actions</h4>
                        </div>
                        <div class="admin-actions-content">
                            <button id="edit-user-btn" class="btn-secondary admin-action-btn">
                                <span class="btn-icon">✏️</span>
                                Edit User
                            </button>
                            <button id="make-admin-btn" class="btn-primary admin-action-btn">
                                <span class="btn-icon">👑</span>
                                Make Admin
                            </button>
                            <button id="revoke-admin-btn" class="btn-danger admin-action-btn" style="display: none;">
                                <span class="btn-icon">🚫</span>
                                Revoke Admin
                            </button>
                            <p class="admin-action-note">
                                All admin actions require 2FA confirmation for security.
                            </p>
                        </div>
                    </div>
                </div>
                <div id="user-audit-tab" class="tab-content">
                    <div class="audit-logs">
                        <div class="audit-filters">
                            <input type="search" id="audit-search" placeholder="Search logs..." class="search-input">
                            <select id="audit-type-filter">
                                <option value="">All Types</option>
                                <option value="login">Login</option>
                                <option value="password_reset">Password Reset</option>
                                <option value="profile_update">Profile Update</option>
                                <option value="admin_action">Admin Action</option>
                            </select>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Action</th>
                                        <th>Details</th>
                                        <th>IP Address</th>
                                    </tr>
                                </thead>
                                <tbody id="user-audit-logs">
                                    <!-- Audit logs will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div id="user-transactions-tab" class="tab-content">
                    <div class="user-transactions">
                        <div class="transaction-filters">
                            <input type="search" id="transaction-search" placeholder="Search transactions..." class="search-input">
                            <select id="transaction-type-filter">
                                <option value="">All Types</option>
                                <option value="sent">Sent</option>
                                <option value="received">Received</option>
                                <option value="split">Split</option>
                                <option value="request">Request</option>
                            </select>
                            <select id="transaction-status-filter">
                                <option value="">All Status</option>
                                <option value="completed">Completed</option>
                                <option value="pending">Pending</option>
                                <option value="failed">Failed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Type</th>
                                        <th>Amount</th>
                                        <th>From/To</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Role</th>
                                    </tr>
                                </thead>
                                <tbody id="user-transactions">
                                    <!-- Transactions will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                        <div class="table-footer">
                            <div class="table-info">
                                <span id="transaction-count">0 transactions</span>
                            </div>
                            <div class="table-pagination">
                                <button id="transaction-prev-page" class="btn-secondary btn-sm" disabled>Previous</button>
                                <span id="transaction-page-info">Page 1 of 1</span>
                                <button id="transaction-next-page" class="btn-secondary btn-sm" disabled>Next</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Broadcast Notification Modal -->
    <div id="broadcast-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📢 Broadcast Notification</h2>
                <button class="modal-close" id="broadcast-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="broadcast-form">
                    <div class="form-group">
                        <label for="notification-title">Notification Title</label>
                        <input type="text" id="notification-title" placeholder="e.g., System Maintenance Notice" required maxlength="50">
                        <small>Maximum 50 characters</small>
                    </div>

                    <div class="form-group">
                        <label for="notification-message">Message</label>
                        <textarea id="notification-message" placeholder="Enter your message to all users..." required maxlength="200" rows="4"></textarea>
                        <small>Maximum 200 characters</small>
                    </div>

                    <div class="form-group">
                        <label for="notification-type">Notification Type</label>
                        <select id="notification-type" required>
                            <option value="system_announcement">📢 System Announcement</option>
                            <option value="maintenance">🔧 Maintenance Notice</option>
                            <option value="security_alert">🔒 Security Alert</option>
                            <option value="feature_update">✨ Feature Update</option>
                            <option value="general">📝 General Notice</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="notification-priority">Priority</label>
                        <select id="notification-priority" required>
                            <option value="normal">Normal</option>
                            <option value="high">High Priority</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="expiration-date">Expiration Date & Time</label>
                        <div class="datetime-group">
                            <input type="date" id="expiration-date" required>
                            <input type="time" id="expiration-time" required>
                        </div>
                        <small>Notification will automatically disappear after this date/time</small>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="send-immediately" checked>
                            <label for="send-immediately">Send immediately</label>
                        </div>
                        <small>Notification will be sent to all active users</small>
                    </div>
                </form>

                <div class="notification-preview">
                    <h4>Preview:</h4>
                    <div class="preview-notification">
                        <div class="preview-icon">📢</div>
                        <div class="preview-content">
                            <div class="preview-title" id="preview-title">Notification Title</div>
                            <div class="preview-message" id="preview-message">Your message will appear here...</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="broadcast-cancel">Cancel</button>
                <button type="submit" form="broadcast-form" class="btn-primary" id="broadcast-send">
                    <span class="btn-text">📤 Send to All Users</span>
                    <span class="btn-loading hidden">Sending...</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 2FA Confirmation Modal for Admin Actions -->
    <div id="admin-2fa-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🔐 Confirm Admin Action</h2>
                <button class="modal-close" id="admin-2fa-close">&times;</button>
            </div>
            <div class="modal-body">
                <div id="admin-action-details" class="action-details">
                    <!-- Action details will be populated here -->
                </div>

                <form id="admin-2fa-form">
                    <div class="form-group">
                        <label for="admin-2fa-code">Enter your 2FA code to confirm:</label>
                        <input type="text" id="admin-2fa-code" placeholder="000000" maxlength="6"
                               pattern="[0-9]{6}" class="form-input 2fa-input" required>
                        <small class="form-help">Enter the 6-digit code from your authenticator app</small>
                    </div>

                    <div class="security-warning">
                        <div class="warning-icon">⚠️</div>
                        <div class="warning-text">
                            <strong>Security Notice:</strong> This action will modify admin privileges.
                            Make sure you trust this user with administrative access.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="admin-2fa-cancel">Cancel</button>
                <button type="submit" form="admin-2fa-form" class="btn-primary" id="admin-2fa-confirm">
                    <span class="btn-text">Confirm Action</span>
                    <span class="btn-loading hidden">Processing...</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Load app.js -->
    <script src="./app.js?v=3"></script>
</body>
</html>
