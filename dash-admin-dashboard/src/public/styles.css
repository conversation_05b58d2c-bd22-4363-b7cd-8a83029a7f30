/* Apple-inspired Admin Dashboard Styles */

:root {
  /* Apple Color Palette */
  --primary-blue: #007AFF;
  --primary-blue-hover: #0056CC;
  --primary-blue-light: #E3F2FD;
  
  --secondary-gray: #8E8E93;
  --tertiary-gray: #C7C7CC;
  --quaternary-gray: #F2F2F7;
  
  --text-primary: #1D1D1F;
  --text-secondary: #86868B;
  --text-tertiary: #AEAEB2;
  
  --background-primary: #FFFFFF;
  --background-secondary: #F5F5F7;
  --background-tertiary: #FAFAFA;
  
  --border-color: #E5E5E7;
  --divider-color: #D2D2D7;
  
  --success-green: #34C759;
  --warning-orange: #FF9500;
  --error-red: #FF3B30;
  
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.15);
  
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --radius-xl: 20px;
  
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-blue) 0%, #5856D6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.dash-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.dash-logo span {
  margin-left: -6px;
}

.sidebar-header .dash-logo span {
  margin-left: 8px;
}

.logo-circle {
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 50%;
  position: relative;
}

.logo-circle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: var(--primary-blue);
  border-radius: 50%;
}

.logo-image {
  width: 32px;
  height: 32px;
  object-fit: contain;
  flex-shrink: 0;
}

.dash-logo span {
  font-size: 28px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Login Screen */
.login-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, var(--background-secondary) 0%, var(--quaternary-gray) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow-y: auto;
}

.login-container {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: 32px;
  width: 100%;
  max-width: 420px;
  min-height: auto;
  box-shadow: var(--shadow-heavy);
  border: 1px solid var(--border-color);
  margin: auto;
  position: relative;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header .dash-logo {
  margin-bottom: 16px;
}

.login-header .dash-logo .logo-circle {
  width: 40px;
  height: 40px;
}

.login-header .dash-logo .logo-circle::before {
  width: 20px;
  height: 20px;
}

.login-header .dash-logo .logo-image {
  width: 40px;
  height: 40px;
}

.sidebar-header .dash-logo .logo-image {
  width: 28px;
  height: 28px;
}

.loading-content .dash-logo .logo-image {
  width: 40px;
  height: 40px;
}

.login-header .dash-logo span {
  font-size: 28px;
  color: var(--text-primary);
}

.login-header h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 6px;
  letter-spacing: -0.5px;
}

.login-header p {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Login Steps */
.login-step {
  animation: slideIn 0.3s ease;
}

.step-header {
  text-align: center;
  margin-bottom: 24px;
}

.step-icon {
  font-size: 40px;
  margin-bottom: 12px;
  display: block;
}

.login-step h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 6px;
}

.login-step p {
  color: var(--text-secondary);
  margin-bottom: 20px;
  font-size: 14px;
}

/* Progress Steps */
.step-progress {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.progress-step {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--tertiary-gray);
  transition: var(--transition-fast);
}

.progress-step.completed {
  background: var(--success-green);
}

.progress-step.active {
  background: var(--primary-blue);
}

/* Form Elements */
.input-group {
  margin-bottom: 24px;
  text-align: left;
}

.input-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.input-group input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: 16px;
  background: var(--background-primary);
  transition: var(--transition-fast);
}

.input-group input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.input-group small {
  display: block;
  margin-top: 6px;
  color: var(--text-secondary);
  font-size: 14px;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  align-items: center;
}

.form-actions .btn-secondary {
  flex: 0 0 auto;
}

.form-actions .btn-primary {
  flex: 1;
}

.btn-primary {
  width: 100%;
  padding: 16px 24px;
  background: var(--primary-blue);
  color: white;
  border: none;
  border-radius: var(--radius-medium);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-fast);
}

.btn-primary:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.forgot-password {
    text-align: center;
    margin-top: 15px;
}

.forgot-password a {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;
}

.forgot-password a:hover {
    color: #764ba2;
    text-decoration: underline;
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  padding: 12px 20px;
  background: var(--background-primary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  background: rgba(103, 126, 234, 0.05);
  transform: translateY(-1px);
}

.qr-container {
  text-align: center;
  margin: 24px 0;
}

.qr-container img {
  max-width: 200px;
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
}

.backup-secret {
  background: var(--quaternary-gray);
  padding: 16px;
  border-radius: var(--radius-medium);
  margin: 16px 0;
  text-align: center;
}

.backup-secret p {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
}

.backup-secret code {
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 14px;
  background: var(--background-primary);
  padding: 8px 12px;
  border-radius: var(--radius-small);
  border: 1px solid var(--border-color);
}

/* Setup Instructions */
.setup-instructions {
  text-align: left;
}

.instruction-step {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: var(--primary-blue);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.step-content p {
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.secret-box {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--background-primary);
  padding: 12px;
  border-radius: var(--radius-small);
  border: 1px solid var(--border-color);
}

.secret-box code {
  flex: 1;
  background: none;
  border: none;
  padding: 0;
  font-family: 'SF Mono', Monaco, monospace;
  font-size: 12px;
  word-break: break-all;
}

.copy-btn {
  padding: 6px 12px;
  background: var(--quaternary-gray);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  font-size: 12px;
  cursor: pointer;
  transition: var(--transition-fast);
}

.copy-btn:hover {
  background: var(--tertiary-gray);
}

/* Button Loading States */
.btn-primary .btn-loading,
.btn-secondary .btn-loading {
  display: none;
}

.btn-primary.loading .btn-text,
.btn-secondary.loading .btn-text {
  display: none;
}

.btn-primary.loading .btn-loading,
.btn-secondary.loading .btn-loading {
  display: inline;
}

.btn-primary:disabled,
.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Dashboard Layout */
.dashboard {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 280px;
  background: var(--background-primary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: 100;
}

.sidebar-header {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header .dash-logo span {
  font-size: 20px;
  color: var(--text-primary);
}

.sidebar-nav {
  flex: 1;
  padding: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: var(--radius-medium);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  transition: var(--transition-fast);
  margin-bottom: 4px;
}

.nav-item:hover {
  background: var(--quaternary-gray);
  color: var(--text-primary);
}

.nav-item.active {
  background: var(--primary-blue-light);
  color: var(--primary-blue);
}

.nav-icon {
  font-size: 18px;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--quaternary-gray);
  border-radius: var(--radius-medium);
  transition: var(--transition-fast);
}

.user-info:hover {
  background: var(--tertiary-gray);
}

.user-avatar {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--primary-blue), #5856D6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
  box-shadow: var(--shadow-light);
}

.user-details {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.user-email {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.user-role {
  font-size: 11px;
  color: var(--primary-blue);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.logout-btn {
  width: 40px;
  height: 40px;
  background: var(--background-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logout-btn:hover {
  background: var(--error-red);
  color: white;
  border-color: var(--error-red);
  transform: translateY(-1px);
}

.logout-icon {
  font-size: 16px;
  transform: rotate(45deg);
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 24px;
  overflow-y: auto;
}

.view {
  display: none;
}

.view.active {
  display: block;
}

.view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.view-header h1 {
  font-size: 32px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: 14px;
  width: 240px;
  transition: var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px var(--primary-blue-light);
}

/* Cards */
.card {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.card-header {
  padding: 24px 24px 0;
}

.card-header h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.card-content {
  padding: 24px;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: var(--background-primary);
  border-radius: var(--radius-large);
  padding: 24px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: var(--transition-fast);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.metric-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--quaternary-gray);
  border-radius: var(--radius-medium);
}

.metric-content h3 {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.metric-change {
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--success-green);
}

.metric-change.negative {
  color: var(--error-red);
}

.metric-change.neutral {
  color: var(--text-secondary);
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

/* Activity List */
.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: var(--quaternary-gray);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.activity-content p {
  font-weight: 500;
  margin-bottom: 4px;
}

.activity-content small {
  color: var(--text-secondary);
  font-size: 12px;
}

/* Health Status */
.health-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.health-item:last-child {
  border-bottom: none;
}

.status-indicator {
  padding: 6px 12px;
  border-radius: var(--radius-small);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-indicator.healthy {
  background: rgba(52, 199, 89, 0.1);
  color: var(--success-green);
}

.status-indicator.warning {
  background: rgba(255, 149, 0, 0.1);
  color: var(--warning-orange);
}

.status-indicator.error {
  background: rgba(255, 59, 48, 0.1);
  color: var(--error-red);
}

/* Tables */
.table-container {
  overflow-x: auto;
  margin-bottom: 24px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  text-align: left;
  padding: 16px;
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 2px solid var(--border-color);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-table td {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.data-table tr:hover {
  background: var(--quaternary-gray);
}

/* FreshDesk Container */
.freshdesk-container {
  height: 600px;
  border-radius: var(--radius-medium);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.freshdesk-container iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.modal-content {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-heavy);
}

.modal-content.large-modal {
  max-width: 900px;
  max-height: 90vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: 20px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 4px;
  border-radius: var(--radius-small);
  transition: var(--transition-fast);
}

.modal-close:hover {
  background: var(--quaternary-gray);
}

.modal-body {
  padding: 24px;
}

/* Modal Tabs */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 24px;
}

.tab-btn {
  padding: 12px 24px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: var(--transition-fast);
}

.tab-btn.active {
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

.tab-btn:hover {
  color: var(--text-primary);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Audit Logs */
.audit-logs {
  max-height: 500px;
  overflow-y: auto;
}

.audit-filters {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.audit-filters select {
  padding: 8px 12px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-small);
  font-size: 14px;
  background: var(--background-primary);
  transition: var(--transition-fast);
}

.audit-filters select:focus {
  outline: none;
  border-color: var(--primary-blue);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toast {
  background: var(--background-primary);
  border-radius: var(--radius-medium);
  padding: 16px 20px;
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-color);
  min-width: 300px;
  animation: slideIn 0.3s ease;
}

.toast.success {
  border-left: 4px solid var(--success-green);
}

.toast.error {
  border-left: 4px solid var(--error-red);
}

.toast.warning {
  border-left: 4px solid var(--warning-orange);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 24px;
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  background: var(--background-primary);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: var(--transition-fast);
}

.pagination button:hover {
  background: var(--quaternary-gray);
}

.pagination button.active {
  background: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.message {
  padding: 16px;
  border-radius: var(--radius-medium);
  margin-top: 16px;
  font-weight: 500;
}

.message.success {
  background: rgba(52, 199, 89, 0.1);
  color: var(--success-green);
  border: 1px solid rgba(52, 199, 89, 0.2);
}

.message.error {
  background: rgba(255, 59, 48, 0.1);
  color: var(--error-red);
  border: 1px solid rgba(255, 59, 48, 0.2);
}

.message.warning {
  background: rgba(255, 149, 0, 0.1);
  color: var(--warning-orange);
  border: 1px solid rgba(255, 149, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-medium);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
    padding: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .login-container {
    padding: 32px 24px;
  }
  
  .view-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
.search-input {
    padding: 12px 16px;
    border: 2px solid transparent;
    border-radius: 12px;
    background: var(--quaternary-gray);
    color: var(--text-primary);
    font-size: 14px;
    width: 280px;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: var(--background-primary);
    box-shadow: 0 0 0 4px rgba(103, 126, 234, 0.1);
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.card-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-top: 1px solid #eee;
    margin-top: 16px;
}

.table-info {
    color: #666;
    font-size: 14px;
}

.table-pagination {
    display: flex;
    align-items: center;
    gap: 12px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
}

.severity-critical {
    background: #ff3b30;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.severity-high {
    background: #ff9500;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.severity-warning {
    background: #ffcc00;
    color: #333;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.severity-info {
    background: #007AFF;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* FreshDesk Integration Styles */
.freshdesk-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-primary);
}

.freshdesk-header h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.freshdesk-actions {
    display: flex;
    gap: 12px;
}

.freshdesk-content {
    background: var(--background-primary);
}

.tickets-list-container {
    background: var(--background-primary);
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loading-state .loading-spinner {
    margin-bottom: 16px;
}

.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.error-state p {
    color: var(--error-red);
    margin-bottom: 16px;
    font-weight: 500;
}

.tickets-filters {
    display: flex;
    gap: 12px;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--quaternary-gray);
}

.tickets-filters select {
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-small);
    font-size: 14px;
    background: var(--background-primary);
    transition: var(--transition-fast);
}

.tickets-filters select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px var(--primary-blue-light);
}

/* Ticket Status Indicators */
.status-indicator.open {
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-blue);
}

.status-indicator.pending {
    background: rgba(255, 149, 0, 0.1);
    color: var(--warning-orange);
}

.status-indicator.resolved {
    background: rgba(52, 199, 89, 0.1);
    color: var(--success-green);
}

.status-indicator.closed {
    background: rgba(142, 142, 147, 0.1);
    color: var(--secondary-gray);
}

/* Priority Indicators */
.priority-low {
    color: var(--success-green);
    font-weight: 500;
}

.priority-medium {
    color: var(--warning-orange);
    font-weight: 500;
}

.priority-high {
    color: var(--error-red);
    font-weight: 500;
}

.priority-urgent {
    color: var(--error-red);
    font-weight: 700;
    text-transform: uppercase;
}

/* Placeholder Content */
.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    background: var(--quaternary-gray);
    border-radius: var(--radius-medium);
    margin: 20px;
}

.placeholder-content h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.placeholder-content p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    max-width: 400px;
}

/* FreshDesk Redirect Message */
.freshdesk-redirect-message {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 40px;
}

.redirect-card {
    background: var(--background-primary);
    border-radius: var(--radius-large);
    padding: 40px;
    text-align: center;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    max-width: 500px;
    width: 100%;
}

.redirect-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.redirect-card p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    font-size: 16px;
}

.freshdesk-info {
    margin-top: 32px;
    text-align: left;
    background: var(--quaternary-gray);
    padding: 20px;
    border-radius: var(--radius-medium);
}

.freshdesk-info p {
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.freshdesk-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.freshdesk-info li {
    margin-bottom: 8px;
}

.freshdesk-info a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.freshdesk-info a:hover {
    text-decoration: underline;
}

/* User Detail Grid */
.user-detail-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 12px;
    background: var(--quaternary-gray);
    border-radius: var(--radius-small);
}

.detail-item label {
    font-weight: 600;
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    font-size: 14px;
    color: var(--text-primary);
    word-break: break-word;
}

.detail-item pre {
    font-family: 'SF Mono', Monaco, monospace;
    font-size: 12px;
    background: var(--background-primary);
    padding: 8px;
    border-radius: var(--radius-small);
    border: 1px solid var(--border-color);
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Role Badges */
.role-badge {
    padding: 4px 8px;
    border-radius: var(--radius-small);
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-badge.sender {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-red);
}

.role-badge.recipient {
    background: rgba(52, 199, 89, 0.1);
    color: var(--success-green);
}

/* Transaction Filters */
.transaction-filters {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    align-items: center;
}

.transaction-filters select {
    padding: 8px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-small);
    font-size: 14px;
    background: var(--background-primary);
    transition: var(--transition-fast);
}

.transaction-filters select:focus {
    outline: none;
    border-color: var(--primary-blue);
}

/* User Transactions */
.user-transactions {
    max-height: 500px;
    overflow-y: auto;
}
  
  .card-content {
    padding: 16px;
  }
  
  .metric-card {
    padding: 16px;
  }
  
  .search-input {
    width: 150px;
  }
}

/* Broadcast Notification Modal Styles */
.notification-preview {
  margin-top: 24px;
  padding: 16px;
  background: var(--quaternary-gray);
  border-radius: var(--radius-medium);
  border: 1px solid var(--border-color);
}

.notification-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.preview-notification {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: var(--background-primary);
  padding: 16px;
  border-radius: var(--radius-small);
  box-shadow: var(--shadow-light);
}

.preview-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-blue);
  border-radius: 50%;
  color: white;
  flex-shrink: 0;
}

.preview-content {
  flex: 1;
}

.preview-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.preview-message {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-small);
  font-size: 14px;
  background: var(--background-primary);
  transition: var(--transition-fast);
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group small {
  display: block;
  margin-top: 6px;
  color: var(--text-secondary);
  font-size: 12px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.checkbox-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-blue);
}

.datetime-group {
  display: flex;
  gap: 12px;
}

.datetime-group input {
  flex: 1;
}

.checkbox-group label {
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
  margin: 0;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid var(--border-color);
  background: var(--quaternary-gray);
}

.modal-footer .btn-secondary {
  padding: 12px 24px;
}

.modal-footer .btn-primary {
  padding: 12px 24px;
  min-width: 160px;
}

/* Broadcast Notification Card Styling */
#broadcast-notification-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

#broadcast-notification-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

#broadcast-notification-card:hover::before {
  animation: shimmer 1.5s ease-in-out;
}

#broadcast-notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

#broadcast-notification-card .metric-icon {
  font-size: 2.5rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

#broadcast-notification-card .metric-value {
  color: rgba(255,255,255,0.95);
  font-weight: 600;
}

#broadcast-notification-card .metric-change {
  color: rgba(255,255,255,0.8);
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

/* User Management Styles */
.user-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.user-management-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.user-management-search {
  position: relative;
}

.user-management-search input {
  padding: 12px 16px 12px 44px;
  border: 2px solid transparent;
  border-radius: 12px;
  background: var(--quaternary-gray);
  color: var(--text-primary);
  font-size: 14px;
  width: 280px;
  transition: all 0.2s ease;
}

.user-management-search input:focus {
  outline: none;
  border-color: var(--primary-blue);
  background: var(--background-primary);
  box-shadow: 0 0 0 4px rgba(103, 126, 234, 0.1);
}

.user-management-search::before {
  content: "🔍";
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: var(--text-secondary);
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.user-item {
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.user-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-blue), #5856D6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.user-item:hover {
  border-color: var(--primary-blue);
  box-shadow: 0 8px 25px rgba(103, 126, 234, 0.15);
  transform: translateY(-2px);
}

.user-item:hover::before {
  transform: scaleX(1);
}

.user-item-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.user-item-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-blue), #5856D6);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(103, 126, 234, 0.3);
  flex-shrink: 0;
}

.user-item-details {
  flex: 1;
  min-width: 0;
}

.user-item-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 17px;
  margin-bottom: 6px;
  line-height: 1.2;
}

.user-item-email {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 6px;
  font-weight: 500;
}

.user-item-role {
  color: var(--text-tertiary);
  font-size: 11px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  background: var(--quaternary-gray);
  padding: 2px 8px;
  border-radius: 6px;
  display: inline-block;
}

.user-item-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-item-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 2px solid transparent;
}

.user-item-status.active {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.15), rgba(52, 199, 89, 0.1));
  color: var(--success-green);
  border-color: rgba(52, 199, 89, 0.2);
}

.user-item-status.inactive {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.15), rgba(255, 149, 0, 0.1));
  color: #ff9500;
  border-color: rgba(255, 149, 0, 0.2);
}

.btn-text {
  background: none;
  border: 2px solid var(--border-color);
  color: var(--text-primary);
  padding: 8px 16px;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-text:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  background: rgba(103, 126, 234, 0.05);
  transform: translateY(-1px);
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-small);
  font-size: 14px;
  background: var(--background-primary);
  transition: var(--transition-fast);
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px var(--primary-blue-light);
}

.form-help {
  display: block;
  margin-top: 6px;
  color: var(--text-secondary);
  font-size: 12px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

.loading-state p {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Admin Actions Styles */
.admin-actions-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 2px solid var(--border-color);
}

.admin-actions-header h4 {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.admin-actions-header h4::before {
  content: "🔐";
  font-size: 18px;
}

.admin-actions-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.admin-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: var(--radius-medium);
  font-weight: 600;
  font-size: 14px;
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
  max-width: 200px;
}

.admin-action-btn .btn-icon {
  font-size: 16px;
}

.admin-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #c0392b, #a93226);
}

.admin-action-note {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
  padding: 8px 12px;
  background: var(--quaternary-gray);
  border-radius: var(--radius-small);
  border-left: 3px solid var(--primary-blue);
}

/* 2FA Modal Styles */
.action-details {
  background: var(--quaternary-gray);
  border-radius: var(--radius-medium);
  padding: 16px;
  margin-bottom: 20px;
  border-left: 4px solid var(--primary-blue);
}

.action-details h4 {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.action-details p {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 4px 0;
}

.action-details .user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.action-details .user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-blue), #5856D6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.action-details .user-details {
  flex: 1;
}

.action-details .user-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.action-details .user-email {
  color: var(--text-secondary);
  font-size: 12px;
}

.2fa-input {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.security-warning {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: var(--radius-medium);
  padding: 16px;
  margin-top: 16px;
}

.warning-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-text strong {
  color: #856404;
  font-weight: 600;
}

.warning-text {
  color: #856404;
  font-size: 14px;
  line-height: 1.4;
}

/* Environment Variable Notice */
.env-var-notice {
  background: var(--primary-blue-light);
  border: 1px solid var(--primary-blue);
  border-radius: var(--radius-medium);
  padding: 16px;
  margin-top: 16px;
}

.env-var-notice h4 {
  color: var(--primary-blue);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
}

.env-var-notice p {
  color: var(--text-primary);
  font-size: 13px;
  margin: 4px 0;
}

.env-var-notice code {
  background: var(--background-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 12px;
  color: var(--primary-blue);
  border: 1px solid var(--border-color);
}

/* Edit User Form Styles */
.edit-form-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 2px solid var(--border-color);
  background: var(--quaternary-gray);
  border-radius: var(--radius-medium);
  padding: 20px;
}

.edit-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.edit-form-header h4 {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-form-header h4::before {
  content: "✏️";
  font-size: 18px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.form-actions button {
  padding: 10px 20px;
  border-radius: var(--radius-small);
  font-weight: 600;
  font-size: 14px;
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
}

.form-actions .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue), #5856D6);
  color: white;
}

.form-actions .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.form-actions .btn-secondary {
  background: var(--tertiary-gray);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.form-actions .btn-secondary:hover {
  background: var(--secondary-gray);
}

/* User Edit Success/Error States */
.edit-success {
  background: rgba(52, 199, 89, 0.1);
  border: 1px solid rgba(52, 199, 89, 0.3);
  border-radius: var(--radius-medium);
  padding: 12px 16px;
  margin-bottom: 16px;
  color: var(--success-green);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-success::before {
  content: "✅";
  font-size: 16px;
}

.edit-error {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.3);
  border-radius: var(--radius-medium);
  padding: 12px 16px;
  margin-bottom: 16px;
  color: #e74c3c;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.edit-error::before {
  content: "❌";
  font-size: 16px;
}
