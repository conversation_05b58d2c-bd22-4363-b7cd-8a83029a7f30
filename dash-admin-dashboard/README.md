# 🚀 Dash Admin Dashboard

A secure, Apple-inspired admin dashboard for the Dash Finance App with Firebase authentication, 2FA, and comprehensive user management.

![Dashboard Preview](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)
![Node.js](https://img.shields.io/badge/Node.js-18+-green)
![Docker](https://img.shields.io/badge/Docker-Ready-blue)
![Security](https://img.shields.io/badge/Security-2FA%20Enabled-red)

## ✨ Features

### 🎨 **Apple-Inspired Design**

- Clean, modern interface following Apple's design principles
- Responsive layout optimized for desktop and tablet
- Smooth animations and transitions
- Dark/light theme support

### 🔐 **Enterprise Security**

- **Firebase Authentication** with domain restrictions
- **Two-Factor Authentication (2FA)** using TOTP
- **JWT-based sessions** with configurable timeouts
- **Rate limiting** and request throttling
- **Admin-only access** with custom claims

### 👥 **User Management**

- View all app users with detailed information
- Reset user passwords
- Monitor user activity and login history
- Export user data for compliance

### 📊 **Dashboard Analytics**

- Real-time metrics and KPIs
- User growth tracking
- System health monitoring
- Security incident reporting

### 🎫 **Support Integration**

- **FreshDesk integration** for ticket management
- Embedded ticket dashboard
- Direct ticket creation and management

### 🛡️ **Security Dashboard**

- Firebase security logs monitoring
- Failed login attempt tracking
- Suspicious activity detection
- Real-time security alerts

### 🐳 **Docker Ready**

- Complete Docker containerization
- Docker Compose for easy deployment
- TrueNAS Scale compatibility
- Health checks and monitoring

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Firebase project with Admin SDK
- Domain email for admin access

### 1. Installation

```bash
git clone <repository-url>
cd dash-admin-dashboard
npm install
```

### 2. Configuration

```bash
cp .env.example .env
# Edit .env with your Firebase and domain settings
```

### 3. Setup Admin User

```bash
npm run setup-admin
```

### 4. Start Application

```bash
npm start
```

Visit `http://localhost:3001` to access the dashboard.

## 🔧 Configuration

### Environment Variables

| Variable                | Description                         | Required |
| ----------------------- | ----------------------------------- | -------- |
| `FIREBASE_PROJECT_ID`   | Your Firebase project ID            | ✅       |
| `FIREBASE_PRIVATE_KEY`  | Firebase Admin SDK private key      | ✅       |
| `FIREBASE_CLIENT_EMAIL` | Firebase service account email      | ✅       |
| `ALLOWED_EMAIL_DOMAIN`  | Domain restriction for admin access | ✅       |
| `JWT_SECRET`            | Secret for JWT token signing        | ✅       |
| `FRESHDESK_DOMAIN`      | FreshDesk domain for integration    | ❌       |
| `FRESHDESK_API_KEY`     | FreshDesk API key                   | ❌       |

### Firebase Setup

1. Create a Firebase project
2. Enable Authentication with Email/Password
3. Generate Admin SDK credentials
4. Set custom claims for admin users

## 🐳 Docker Deployment

### Using Docker Compose

```bash
docker-compose up -d
```

### Manual Docker Build

```bash
docker build -t dash-admin-dashboard .
docker run -d -p 3001:3001 --env-file .env dash-admin-dashboard
```

## 🏠 TrueNAS Scale Deployment

For detailed TrueNAS Scale deployment instructions, see [DEPLOYMENT.md](./DEPLOYMENT.md).

### Quick TrueNAS Setup

1. Create app dataset: `/mnt/tank/apps/dash-admin`
2. Copy project files to the dataset
3. Use TrueNAS Scale Apps to deploy Docker container
4. Configure networking and environment variables

## 🔒 Security Features

### Authentication Flow

1. **Firebase Login** - Email/password authentication
2. **Domain Validation** - Restrict to authorized domains
3. **Admin Claims Check** - Verify admin privileges
4. **2FA Setup/Verification** - TOTP-based second factor
5. **JWT Session** - Secure session management

### Security Best Practices

- All admin users require 2FA
- Sessions expire after configurable timeout
- Rate limiting prevents brute force attacks
- All API endpoints require authentication
- Audit logging for security events

## 📱 2FA Setup

### For Users

1. Login with Firebase credentials
2. Scan QR code with authenticator app (Google Authenticator, Authy, etc.)
3. Enter verification code to complete setup
4. Save backup codes securely

### Supported Authenticator Apps

- Google Authenticator
- Authy
- Microsoft Authenticator
- 1Password
- Bitwarden

## 🛠️ API Endpoints

### Authentication

- `POST /api/auth/firebase-login` - Firebase authentication
- `POST /api/auth/setup-2fa` - Setup two-factor authentication
- `POST /api/auth/verify-2fa` - Verify 2FA code
- `GET /api/auth/me` - Get current user info

### Dashboard

- `GET /api/dashboard/overview` - Dashboard metrics
- `GET /api/users` - List all users
- `GET /api/security` - Security logs and metrics

### User Management

- `GET /api/users/:uid` - Get user details
- `POST /api/users/:uid/reset-password` - Reset user password

## 🔍 Monitoring

### Health Checks

```bash
curl http://localhost:3001/health
```

### Logs

```bash
# Docker logs
docker logs dash-admin-dashboard

# Application logs
tail -f logs/app.log
```

## 🚨 Troubleshooting

### Common Issues

**Port Already in Use**

```bash
PORT=3002 npm start
```

**Firebase Authentication Errors**

- Verify Firebase credentials in `.env`
- Check Firebase project settings
- Ensure admin custom claims are set

**2FA Issues**

- Verify time synchronization
- Clear browser localStorage
- Check TOTP app configuration

## 📈 Performance

### Optimization Features

- Efficient Firebase queries
- Client-side caching
- Lazy loading for large datasets
- Optimized Docker images
- Health check endpoints

### Resource Usage

- **Memory**: ~100MB typical usage
- **CPU**: Low usage, spikes during authentication
- **Storage**: Minimal, logs only
- **Network**: Firebase API calls only

## 🔄 Updates

### Application Updates

```bash
git pull origin main
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Security Updates

- Monitor Firebase security advisories
- Update dependencies regularly
- Rotate JWT secrets periodically
- Review access logs monthly

## 📋 Roadmap

### Planned Features

- [ ] Advanced analytics dashboard
- [ ] Bulk user operations
- [ ] Custom security rules
- [ ] Email notifications
- [ ] API rate limiting per user
- [ ] Advanced audit logging
- [ ] Multi-tenant support

### Integration Roadmap

- [ ] Slack notifications
- [ ] Webhook support
- [ ] LDAP/Active Directory integration
- [ ] SSO providers (Google, Microsoft)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For technical support:

1. Check the [DEPLOYMENT.md](./DEPLOYMENT.md) guide
2. Review application logs
3. Verify Firebase configuration
4. Test network connectivity

## 🙏 Acknowledgments

- Firebase for authentication infrastructure
- Express.js for the web framework
- Speakeasy for 2FA implementation
- Docker for containerization
- TrueNAS Scale for deployment platform

---

**Built with ❤️ for the Dash Finance App**

_Secure • Scalable • Production Ready_
