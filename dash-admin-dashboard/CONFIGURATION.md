# 🔧 Configuration Guide for Dash Admin Dashboard

## ✅ Current Status

### Already Configured ✅

- **Firebase Project ID**: `dashfinanceapp-51a69` (matches your existing project)
- **Firebase Admin SDK**: Service account credentials are configured
- **JWT Secret**: Secure key is set
- **Admin Emails**: Your email is already added
- **Domain Restriction**: Set to `dashfinanceapp.com`
- **Resend API**: Email service is configured

### Needs Configuration ⚠️

## 🔥 Firebase Setup Required

### 1. Enable Firebase Authentication

```bash
# In Firebase Console (https://console.firebase.google.com/project/dashfinanceapp-51a69)
1. Go to Authentication > Sign-in method
2. Enable "Email/Password" provider
3. Go to Authentication > Users
4. Add your admin user manually or use the setup script
```

### 2. Set Admin Custom Claims

You need to set admin custom claims for your user. Run this script:

```bash
cd dash-admin-dashboard
npm run setup-admin
```

Or manually in Firebase Console:

1. Go to Authentication > Users
2. Find your user
3. Set custom claims: `{"admin": true}`

## 🎫 FreshDesk Integration (Optional)

### Current Configuration:

```env
FRESHDESK_DOMAIN=your-domain.freshdesk.com
FRESHDESK_API_KEY=your-freshdesk-api-key
```

### To Configure:

1. **Get your FreshDesk domain**:
   - Example: If your FreshDesk URL is `https://mycompany.freshdesk.com`
   - Set `FRESHDESK_DOMAIN=mycompany.freshdesk.com`

2. **Get API Key**:
   - Login to FreshDesk
   - Go to Profile Settings > API Key
   - Copy the API key
   - Set `FRESHDESK_API_KEY=your_actual_api_key`

3. **Update .env file**:

```bash
# Replace these values in dash-admin-dashboard/.env
FRESHDESK_DOMAIN=your-actual-domain.freshdesk.com
FRESHDESK_API_KEY=your_actual_freshdesk_api_key
```

## 🔐 Security Configuration

### Current Security Settings ✅

- **Rate Limiting**: 100 requests per 15 minutes
- **Session Timeout**: 24 hours
- **JWT Secret**: Secure random key
- **Domain Restriction**: Only `@dashfinanceapp.com` emails

### Additional Security (Recommended)

1. **Firewall Rules**: Restrict access to your VPN IP range
2. **SSL Certificate**: Use HTTPS in production
3. **Regular Key Rotation**: Update JWT_SECRET monthly

## 🚀 Deployment Configuration

### For Local Development ✅

Everything is ready! Just run:

```bash
cd dash-admin-dashboard
npm start
```

### For Production Server

1. **Update Environment**:

```bash
NODE_ENV=production
PORT=3001
```

2. **SSL Configuration** (if using HTTPS):

```bash
# Add to .env if using SSL
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

## 📧 Email Configuration ✅

Email notifications are configured with Resend:

- **API Key**: Already set
- **From Email**: `<EMAIL>`

## 🐳 Docker Configuration ✅

Docker is ready to use:

```bash
# Build and run
docker-compose up -d

# Or manual build
docker build -t dash-admin .
docker run -d -p 3001:3001 --env-file .env dash-admin
```

## 🏠 TrueNAS Scale Configuration

See `DEPLOYMENT.md` for complete TrueNAS Scale setup instructions.

## ⚡ Quick Start Checklist

### Before First Run:

- [ ] 1. Enable Firebase Authentication (Email/Password)
- [ ] 2. Run `npm run setup-admin` to set admin claims
- [ ] 3. (Optional) Configure FreshDesk credentials
- [ ] 4. Test login at `http://localhost:3001`

### For Production:

- [ ] 1. Set `NODE_ENV=production`
- [ ] 2. Configure SSL certificates
- [ ] 3. Set up firewall rules
- [ ] 4. Deploy with Docker
- [ ] 5. Configure backup strategy

## 🔍 Testing Configuration

### Test Firebase Connection:

```bash
# This will test Firebase Admin SDK
curl http://localhost:3001/api/dashboard/overview
```

### Test Authentication:

1. Go to `http://localhost:3001`
2. Try logging in with your `@dashfinanceapp.com` email
3. Complete 2FA setup

### Test FreshDesk (if configured):

1. Go to Support Tickets tab
2. Should load FreshDesk iframe

## 🆘 Troubleshooting

### Firebase Issues:

- **Error**: "Firebase Admin SDK not initialized"
  - **Solution**: Check FIREBASE_PRIVATE_KEY format (should have \n for line breaks)

### Authentication Issues:

- **Error**: "User not authorized"
  - **Solution**: Run `npm run setup-admin` to set admin claims

### FreshDesk Issues:

- **Error**: "FreshDesk integration failed"
  - **Solution**: Verify FRESHDESK_DOMAIN and FRESHDESK_API_KEY

### Port Issues:

- **Error**: "Port 3001 already in use"
  - **Solution**: Change PORT in .env or kill existing process

## 📞 Support

If you encounter issues:

1. Check the application logs: `docker logs dash-admin-dashboard`
2. Verify environment variables are set correctly
3. Test Firebase connection in Firebase Console
4. Check network connectivity for FreshDesk integration

---

**Next Steps**: Run `npm run setup-admin` and test the dashboard!
