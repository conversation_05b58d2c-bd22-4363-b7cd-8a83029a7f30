# Manual Deployment Guide - TrueNAS Scale (No SSH)

This guide helps you deploy the Dash Admin Dashboard when SSH is not available on your TrueNAS Scale server.

## 🚨 SSH Connection Issue

The automated deployment script failed because SSH is not accessible on your TrueNAS Scale server (`*************`). This is common and can be resolved.

## 🔧 Option 1: Enable SSH (Recommended)

### Step 1: Enable SSH Service

1. **Access TrueNAS Scale Web Interface**: `http://*************`
2. **Navigate to**: System Settings → Services
3. **Find SSH service** and click the toggle to enable it
4. **Configure SSH** (click the pencil icon):
   - Enable "Allow Password Authentication" (for initial setup)
   - Or set up SSH keys for better security
5. **Start the SSH service**

### Step 2: Set Up SSH Access

```bash
# Test SSH connection
ssh root@*************

# If password authentication is enabled, you'll be prompted for password
# If using SSH keys, make sure your public key is added to TrueNAS Scale
```

### Step 3: Run Deployment Script

```bash
cd dash-admin-dashboard
./deploy-to-truenas.sh
```

## 🔧 Option 2: Manual File Transfer

If you prefer not to enable SSH, follow these manual steps:

### Step 1: Prepare Files Locally

```bash
cd dash-admin-dashboard

# Build Docker image
docker build -t dash-admin-dashboard:latest .

# Save Docker image
docker save dash-admin-dashboard:latest -o dash-admin-dashboard.tar
```

### Step 2: Transfer Files to TrueNAS Scale

#### Method A: Using TrueNAS Scale Web Interface

1. **Access TrueNAS Scale**: `http://*************`
2. **Navigate to**: Storage → Pools
3. **Create directory**: `/mnt/tank/appdata/dash-admin/`
4. **Upload files** using the web interface:
   - `dash-admin-dashboard.tar`
   - `truenas-docker-compose.yml` (rename to `docker-compose.yml`)
   - `.env`

#### Method B: Using Network Share

1. **Set up SMB/NFS share** on TrueNAS Scale
2. **Mount the share** on your local machine
3. **Copy files** to the mounted share

### Step 3: Deploy via TrueNAS Scale Shell

1. **Access TrueNAS Scale Web Interface**: `http://*************`
2. **Navigate to**: System Settings → Shell
3. **Run these commands**:

```bash
# Navigate to app directory
cd /mnt/tank/appdata/dash-admin

# Load Docker image
docker load -i dash-admin-dashboard.tar

# Create logs directory
mkdir -p logs

# Start the application
docker-compose up -d

# Check if container is running
docker ps | grep dash-admin-dashboard

# View logs
docker-compose logs -f
```

## 🔧 Option 3: TrueNAS Scale Apps (Custom App)

### Step 1: Prepare for Custom App

1. **Upload Docker image** to a registry (Docker Hub, etc.) or
2. **Use local image** after loading it manually

### Step 2: Create Custom App

1. **Access TrueNAS Scale**: `http://*************`
2. **Navigate to**: Apps
3. **Click**: "Launch Docker Image" or "Custom App"
4. **Configure**:
   - **Image**: `dash-admin-dashboard:latest`
   - **Port**: `3000:3000`
   - **Environment Variables**: Copy from `.env` file
   - **Storage**: Mount `/mnt/tank/appdata/dash-admin/logs` to `/app/logs`

## 🌐 Network Configuration

After deployment, configure local network access:

### Router DNS Configuration

1. **Access your router's admin panel**
2. **Navigate to DNS settings**
3. **Add DNS entry**:
   - **Hostname**: `management.dashfinanceapp.com`
   - **IP Address**: `*************`

### Local Hosts File

Add this line to your hosts file:

```
************* management.dashfinanceapp.com
```

**Hosts file locations:**

- **Windows**: `C:\Windows\System32\drivers\etc\hosts`
- **macOS/Linux**: `/etc/hosts`

## ✅ Verification

### Check Deployment

```bash
# Test direct IP access
curl http://*************:3000/health

# Test subdomain access (after DNS setup)
curl http://management.dashfinanceapp.com:3000/health
```

### Access URLs

- **Direct IP**: `http://*************:3000`
- **Subdomain**: `http://management.dashfinanceapp.com:3000`

## 🔧 Troubleshooting

### Container Not Starting

1. **Check logs**: `docker-compose logs dash-admin`
2. **Check environment**: Verify `.env` file is correct
3. **Check ports**: Ensure port 3000 is not in use

### Can't Access Dashboard

1. **Check firewall**: Ensure port 3000 is open
2. **Check container**: `docker ps | grep dash-admin`
3. **Check TrueNAS Scale network**: Verify IP address is correct

### DNS Not Working

1. **Test DNS**: `nslookup management.dashfinanceapp.com`
2. **Clear DNS cache**: Restart router or flush local DNS
3. **Try hosts file**: As fallback option

## 📞 Next Steps

1. **Choose deployment method** based on your preference
2. **Enable SSH** for easier future management (recommended)
3. **Set up DNS** for subdomain access
4. **Consider reverse proxy** for SSL/HTTPS access
5. **Set up monitoring** and automated backups

## 🔒 Security Recommendations

1. **Use SSH keys** instead of password authentication
2. **Disable SSH** after deployment if not needed regularly
3. **Configure firewall** to restrict access to necessary ports
4. **Use strong passwords** for all accounts
5. **Keep TrueNAS Scale updated**

For detailed configuration options, see `CONFIGURATION.md` and `TRUENAS-DEPLOYMENT-GUIDE.md`.
