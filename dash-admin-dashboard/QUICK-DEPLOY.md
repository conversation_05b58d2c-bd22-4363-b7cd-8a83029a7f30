# Quick Deploy to TrueNAS Scale

## 🚀 One-Command Deployment

```bash
cd dash-admin-dashboard && ./deploy-to-truenas.sh
```

## 📋 What You'll Need

- TrueNAS Scale IP address
- TrueNAS Scale username
- Desired subdomain name
- Local domain name

## 🔧 Manual Steps Summary

```bash
# 1. Build and save image
docker build -t dash-admin-dashboard:latest .
docker save dash-admin-dashboard:latest -o dash-admin-dashboard.tar

# 2. Copy to TrueNAS Scale
scp dash-admin-dashboard.tar user@truenas-ip:/tmp/
scp truenas-docker-compose.yml user@truenas-ip:/tmp/
scp .env user@truenas-ip:/tmp/

# 3. Deploy on TrueNAS Scale
ssh user@truenas-ip
docker load -i /tmp/dash-admin-dashboard.tar
sudo mkdir -p /mnt/tank/appdata/dash-admin/logs
mv /tmp/truenas-docker-compose.yml /mnt/tank/appdata/dash-admin/docker-compose.yml
mv /tmp/.env /mnt/tank/appdata/dash-admin/.env
cd /mnt/tank/appdata/dash-admin
docker-compose up -d
```

## 🌐 Access Setup

### Add to hosts file:

```
************* management.dashfinanceapp.com
```

### Or configure router DNS:

- Hostname: `management.dashfinanceapp.com`
- IP: `*************`

## ✅ Verify Deployment

```bash
# Check container
docker ps | grep dash-admin-dashboard

# View logs
docker-compose logs -f

# Test access
curl http://truenas-ip:3000/health
```

## 🔗 Access URLs

- Direct: `http://*************:3000`
- Subdomain: `http://management.dashfinanceapp.com:3000`

## 🆘 Quick Troubleshooting

```bash
# Container not starting?
docker-compose logs dash-admin

# Port conflict?
netstat -tulpn | grep :3000

# DNS not working?
nslookup management.dashfinanceapp.com
```

For detailed instructions, see `TRUENAS-DEPLOYMENT-GUIDE.md`
