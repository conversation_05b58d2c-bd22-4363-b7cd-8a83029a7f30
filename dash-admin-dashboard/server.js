const express = require('express');
const https = require('https');
const fs = require('fs');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();

// Import routes
const authRoutes = require('./src/routes/auth');
const dashboardRoutes = require('./src/routes/dashboard');
const usersRoutes = require('./src/routes/users');
const securityRoutes = require('./src/routes/security');
const ticketsRoutes = require('./src/routes/tickets');

// Import middleware
const rateLimiter = require('./src/middleware/rateLimiter');
const errorHandler = require('./src/middleware/errorHandler');
const logger = require('./src/middleware/logger');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware with HTTP-compatible settings
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://fonts.gstatic.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://fonts.googleapis.com", "data:"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://www.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https://api.resend.com", "https://dashfinanceapp.freshdesk.com", "https://identitytoolkit.googleapis.com", "https://securetoken.googleapis.com"],
      frameSrc: ["'self'", "https://dashfinanceapp.freshdesk.com"],
      mediaSrc: ["'self'", "data:", "blob:"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"]
    }
  },
  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: false,
  crossOriginResourcePolicy: false,
  originAgentCluster: false,
  hsts: false  // Disable HTTPS enforcement
}));

app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? true  // Allow all origins in production for now
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

// Rate limiting
app.use(rateLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(logger);

// Explicit CSS route for Safari compatibility
app.get('/styles.css', (req, res) => {
  res.setHeader('Content-Type', 'text/css; charset=utf-8');
  res.sendFile(path.join(__dirname, 'src/public/styles.css'));
});

// Explicit JS route for Safari compatibility
app.get('/app.js', (req, res) => {
  res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
  res.sendFile(path.join(__dirname, 'src/public/app.js'));
});

// Static files with proper MIME types
app.use(express.static(path.join(__dirname, 'src/public'), {
  setHeaders: (res, filePath) => {
    if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
      res.setHeader('Cache-Control', 'public, max-age=31536000');
    }
    if (filePath.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
      res.setHeader('Cache-Control', 'public, max-age=31536000');
    }
    if (filePath.endsWith('.png') || filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.setHeader('Cache-Control', 'public, max-age=31536000');
    }
  }
}));

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/users', usersRoutes);
app.use('/api/security', securityRoutes);
app.use('/api/tickets', ticketsRoutes);

// Serve main dashboard page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'src/public/index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Debug endpoint for static files
app.get('/debug/static', (req, res) => {
  const fs = require('fs');
  const staticPath = path.join(__dirname, 'src/public');
  
  try {
    const files = fs.readdirSync(staticPath);
    res.json({
      staticPath: staticPath,
      files: files,
      cssExists: fs.existsSync(path.join(staticPath, 'styles.css')),
      jsExists: fs.existsSync(path.join(staticPath, 'app.js')),
      htmlExists: fs.existsSync(path.join(staticPath, 'index.html'))
    });
  } catch (error) {
    res.status(500).json({
      error: 'Failed to read static directory',
      message: error.message,
      staticPath: staticPath
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

// HTTPS Configuration
const useHTTPS = process.env.ENABLE_HTTPS === 'true';
const HTTPS_PORT = process.env.HTTPS_PORT || 3443;

if (useHTTPS) {
  // Generate self-signed certificate if none exists (use writable directory)
  const certPath = '/app/server.crt';
  const keyPath = '/app/server.key';

  // Check if certificates exist, if not create self-signed ones
  if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
    console.log('🔐 Generating self-signed SSL certificate...');
    const { execSync } = require('child_process');
    try {
      execSync(`openssl req -x509 -newkey rsa:4096 -keyout ${keyPath} -out ${certPath} -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost" -addext "subjectAltName=DNS:localhost,DNS:*.local,IP:127.0.0.1,IP:*************,IP:*************"`);
      console.log('✅ Self-signed certificate generated');
    } catch (error) {
      console.error('❌ Failed to generate certificate:', error.message);
      console.log('🔄 Falling back to HTTP mode');
      startHTTPServer();
      return;
    }
  }

  // HTTPS Server
  const httpsOptions = {
    key: fs.readFileSync(keyPath),
    cert: fs.readFileSync(certPath)
  };

  https.createServer(httpsOptions, app).listen(HTTPS_PORT, () => {
    console.log(`🚀 Dash Admin Dashboard running on HTTPS port ${HTTPS_PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 Access: https://localhost:${HTTPS_PORT}`);
  });

  // Optional: Redirect HTTP to HTTPS
  const httpApp = express();
  httpApp.use((req, res) => {
    res.redirect(`https://${req.headers.host.split(':')[0]}:${HTTPS_PORT}${req.url}`);
  });
  httpApp.listen(PORT, () => {
    console.log(`🔄 HTTP redirect server running on port ${PORT} -> HTTPS ${HTTPS_PORT}`);
  });

} else {
  startHTTPServer();
}

function startHTTPServer() {
  app.listen(PORT, () => {
    console.log(`🚀 Dash Admin Dashboard running on HTTP port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 Access: http://localhost:${PORT}`);
  });
}

module.exports = app;
