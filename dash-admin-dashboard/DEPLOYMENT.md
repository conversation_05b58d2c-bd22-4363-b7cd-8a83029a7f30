# Dash Admin Dashboard - Deployment Guide

## Overview

This is a secure admin dashboard for the Dash Finance App with the following features:

- **Apple-inspired UI Design** - Clean, modern interface
- **Firebase Authentication** - Secure login with domain restrictions
- **Two-Factor Authentication** - TOTP-based 2FA for enhanced security
- **User Management** - View and manage app users
- **Security Dashboard** - Monitor security incidents and logs
- **Support Ticket Integration** - FreshDesk integration (configurable)
- **Docker Support** - Easy deployment with Docker containers

## Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose
- Firebase project with Admin SDK
- Domain email account for admin access

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd dash-admin-dashboard
npm install
```

### 2. Configure Environment

Copy the example environment file and configure:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

```env
# Server Configuration
PORT=3001
NODE_ENV=production

# JWT Configuration (generate a secure secret)
JWT_SECRET=your-super-secure-jwt-secret-here

# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>
ALLOWED_EMAIL_DOMAIN=yourdomain.com

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----
FIREBASE_CLIENT_EMAIL=<EMAIL>

# FreshDesk Configuration (optional)
FRESHDESK_DOMAIN=your-domain.freshdesk.com
FRESHDESK_API_KEY=your-freshdesk-api-key

# Email Configuration (optional)
RESEND_API_KEY=your-resend-api-key
FROM_EMAIL=<EMAIL>

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SESSION_TIMEOUT_HOURS=24
```

### 3. Setup Firebase Admin User

Run the admin setup script to grant admin privileges to your user:

```bash
npm run setup-admin
```

This will:

- Find or create your user account in Firebase
- Set admin custom claims
- Display setup confirmation

### 4. Start the Application

```bash
# Development
npm run dev

# Production
npm start
```

The dashboard will be available at `http://localhost:3001`

## Docker Deployment

### Build and Run with Docker

```bash
# Build the image
docker build -t dash-admin-dashboard .

# Run the container
docker run -d \
  --name dash-admin \
  -p 3001:3001 \
  --env-file .env \
  dash-admin-dashboard
```

### Using Docker Compose

```bash
# Start the service
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the service
docker-compose down
```

## TrueNAS Scale Deployment

### Method 1: Using TrueNAS Scale Apps (Recommended)

1. **Create App Dataset**

   ```bash
   # SSH into TrueNAS Scale
   sudo zfs create tank/apps/dash-admin
   ```

2. **Upload Files**
   - Copy the entire `dash-admin-dashboard` folder to `/mnt/tank/apps/dash-admin/`
   - Ensure `.env` file is properly configured

3. **Create Custom App**
   - Go to TrueNAS Scale Web UI → Apps
   - Click "Launch Docker Image"
   - Configure as follows:

   **Application Name:** `dash-admin-dashboard`

   **Container Configuration:**
   - Image Repository: `node`
   - Image Tag: `18-alpine`
   - Container Command: `["sh", "-c", "cd /app && npm install --production && npm start"]`

   **Storage:**
   - Host Path: `/mnt/tank/apps/dash-admin`
   - Mount Path: `/app`

   **Networking:**
   - Port: `3001` (Host) → `3001` (Container)

   **Environment Variables:**
   - Add all variables from your `.env` file

4. **Deploy**
   - Click "Install" to deploy the application
   - Monitor logs in the Apps section

### Method 2: Using Docker Compose in TrueNAS Scale

1. **Enable Docker Compose**

   ```bash
   # SSH into TrueNAS Scale
   sudo apt update && sudo apt install docker-compose
   ```

2. **Deploy Application**

   ```bash
   cd /mnt/tank/apps/dash-admin
   sudo docker-compose up -d
   ```

3. **Create Systemd Service** (Optional)

   ```bash
   sudo nano /etc/systemd/system/dash-admin.service
   ```

   ```ini
   [Unit]
   Description=Dash Admin Dashboard
   After=docker.service
   Requires=docker.service

   [Service]
   Type=oneshot
   RemainAfterExit=yes
   WorkingDirectory=/mnt/tank/apps/dash-admin
   ExecStart=/usr/bin/docker-compose up -d
   ExecStop=/usr/bin/docker-compose down

   [Install]
   WantedBy=multi-user.target
   ```

   ```bash
   sudo systemctl enable dash-admin.service
   sudo systemctl start dash-admin.service
   ```

## Security Considerations

### Network Security

1. **VPN Access Only**
   - Configure your VPN to allow access to port 3001
   - Block external access to this port in firewall

2. **Reverse Proxy** (Recommended)
   ```nginx
   server {
       listen 443 ssl;
       server_name admin.yourdomain.com;

       ssl_certificate /path/to/cert.pem;
       ssl_certificate_key /path/to/key.pem;

       location / {
           proxy_pass http://localhost:3001;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

### Application Security

1. **Strong JWT Secret**

   ```bash
   # Generate a secure JWT secret
   openssl rand -base64 64
   ```

2. **Environment Variables**
   - Never commit `.env` files to version control
   - Use strong, unique passwords
   - Rotate secrets regularly

3. **Firebase Security**
   - Restrict Firebase API keys to specific domains
   - Use Firebase Security Rules
   - Monitor Firebase usage

## Monitoring and Maintenance

### Health Checks

The application includes health check endpoints:

```bash
# Check application health
curl http://localhost:3001/health

# Check detailed status
curl http://localhost:3001/api/dashboard/overview
```

### Log Monitoring

```bash
# View application logs
docker logs dash-admin

# Follow logs in real-time
docker logs -f dash-admin

# View TrueNAS Scale app logs
# Go to Apps → dash-admin-dashboard → View Logs
```

### Backup Strategy

1. **Configuration Backup**

   ```bash
   # Backup environment and config
   tar -czf dash-admin-backup-$(date +%Y%m%d).tar.gz \
     .env docker-compose.yml package.json
   ```

2. **Database Backup**
   - Firebase data is automatically backed up by Google
   - Export user data periodically via Firebase Console

### Updates

1. **Application Updates**

   ```bash
   # Pull latest code
   git pull origin main

   # Rebuild and restart
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

2. **Dependency Updates**
   ```bash
   npm audit
   npm update
   ```

## Troubleshooting

### Common Issues

1. **Port Already in Use**

   ```bash
   # Change port in .env file
   PORT=3002

   # Or kill existing process
   sudo lsof -ti:3001 | xargs kill -9
   ```

2. **Firebase Authentication Errors**
   - Verify Firebase credentials in `.env`
   - Check Firebase project settings
   - Ensure admin custom claims are set

3. **2FA Setup Issues**
   - Clear browser localStorage
   - Verify TOTP app time synchronization
   - Check server time accuracy

4. **Docker Issues**

   ```bash
   # View container logs
   docker logs dash-admin

   # Restart container
   docker restart dash-admin

   # Rebuild image
   docker build --no-cache -t dash-admin-dashboard .
   ```

### Support

For technical support:

1. Check application logs
2. Verify configuration settings
3. Test Firebase connectivity
4. Review TrueNAS Scale system logs

## Performance Optimization

### Production Settings

1. **Node.js Optimization**

   ```env
   NODE_ENV=production
   NODE_OPTIONS="--max-old-space-size=512"
   ```

2. **Docker Optimization**

   ```dockerfile
   # Use multi-stage build for smaller images
   # Enable health checks
   # Set resource limits
   ```

3. **Caching**
   - Enable browser caching for static assets
   - Use Redis for session storage (optional)

This deployment guide provides comprehensive instructions for running the Dash Admin Dashboard securely on your home server with TrueNAS Scale.
