# TrueNAS Scale Custom App Deployment Guide
## Dash Admin Dashboard

This guide will walk you through deploying your Dash Admin Dashboard as a **Custom App** in TrueNAS Scale, making it fully integrated with the TrueNAS Scale Apps interface.

## Prerequisites

- TrueNAS Scale 22.12 or later
- Docker Hub account (or other container registry)
- Your Firebase service account file: `/Volumes/vault2/AppData/dash-admin/dashfinanceapp-51a69-firebase-adminsdk-fbsvc-971c2a5132.json`
- Basic understanding of Docker and environment variables

## Step 1: Prepare Your Firebase Configuration

First, extract the Firebase credentials from your service account JSON file:

```bash
# View your Firebase service account file
cat /Volumes/vault2/AppData/dash-admin/dashfinanceapp-51a69-firebase-adminsdk-fbsvc-971c2a5132.json
```

You'll need these values from the JSON file:
- `project_id`
- `private_key_id` 
- `private_key`
- `client_email`
- `client_id`

## Step 2: Build and Push Docker Image

1. **Navigate to your project directory:**
   ```bash
   cd dash-admin-dashboard
   ```

2. **Build the Docker image:**
   ```bash
   docker build -t dash-admin-dashboard:latest .
   ```

3. **Tag for Docker Hub** (replace `yourusername` with your Docker Hub username):
   ```bash
   docker tag dash-admin-dashboard:latest yourusername/dash-admin:latest
   ```

4. **Push to Docker Hub:**
   ```bash
   docker login
   docker push yourusername/dash-admin:latest
   ```

   **Alternative: Use GitHub Container Registry:**
   ```bash
   docker tag dash-admin-dashboard:latest ghcr.io/yourusername/dash-admin:latest
   docker push ghcr.io/yourusername/dash-admin:latest
   ```

## Step 3: Access TrueNAS Scale Apps

1. **Open TrueNAS Scale web interface:**
   - Navigate to `http://your-truenas-ip`
   - Login with your admin credentials

2. **Go to Apps section:**
   - Click "Apps" in the left sidebar
   - If this is your first time, click "Discover Apps"

## Step 4: Create Custom App

1. **Click "Custom App" button** (usually in the top right)

2. **Fill in Application Details:**

### Basic Configuration
- **Application Name:** `dash-admin-dashboard`
- **Version:** `1.0.0`
- **Description:** `Dash Finance Admin Dashboard`

### Container Images
- **Image Repository:** `yourusername/dash-admin` (or your registry URL)
- **Image Tag:** `latest`
- **Image Pull Policy:** `Always`

### Container Configuration
- **Container Port:** `3000`
- **Protocol:** `TCP`

### Networking
- **Node Port:** `30300` (choose any available port between 30000-32767)
- **Host Network:** `Disabled`

## Step 5: Configure Environment Variables

Add each of these environment variables (click "Add" for each one):

### Required Environment Variables

| Name | Value |
|------|-------|
| `NODE_ENV` | `production` |
| `PORT` | `3000` |
| `JWT_SECRET` | `your-super-secure-jwt-secret-change-this-in-production` |
| `JWT_EXPIRES_IN` | `24h` |

### Firebase Configuration
Extract these from your JSON file:

| Name | Value |
|------|-------|
| `FIREBASE_PROJECT_ID` | `dashfinanceapp-51a69` |
| `FIREBASE_PRIVATE_KEY_ID` | `[from your JSON file]` |
| `FIREBASE_PRIVATE_KEY` | `[from your JSON file - include the full key with \n characters]` |
| `FIREBASE_CLIENT_EMAIL` | `[from your JSON file]` |
| `FIREBASE_CLIENT_ID` | `[from your JSON file]` |
| `FIREBASE_AUTH_URI` | `https://accounts.google.com/o/oauth2/auth` |
| `FIREBASE_TOKEN_URI` | `https://oauth2.googleapis.com/token` |

### Email Configuration (Optional)

| Name | Value |
|------|-------|
| `RESEND_API_KEY` | `your-resend-api-key` |

### FreshDesk Configuration (Optional)

| Name | Value |
|------|-------|
| `FRESHDESK_DOMAIN` | `dashfinanceapp.freshdesk.com` |
| `FRESHDESK_API_KEY` | `your-freshdesk-api-key` |

### Security Configuration

| Name | Value |
|------|-------|
| `ALLOWED_EMAIL_DOMAIN` | `dashfinanceapp.com` |
| `ADMIN_EMAILS` | `<EMAIL>,<EMAIL>` |
| `RATE_LIMIT_WINDOW_MS` | `900000` |
| `RATE_LIMIT_MAX_REQUESTS` | `100` |

## Step 6: Configure Storage

1. **Add Host Path Volume:**
   - **Host Path:** `/mnt/tank/appdata/dash-admin/logs`
   - **Mount Path:** `/app/logs`
   - **Type:** `Host Path`

2. **Create the directory on TrueNAS Scale** (via SSH):
   ```bash
   ssh root@your-truenas-ip
   mkdir -p /mnt/tank/appdata/dash-admin/logs
   chown -R 1001:1001 /mnt/tank/appdata/dash-admin/logs
   exit
   ```

## Step 7: Advanced Configuration (Optional)

### Resource Limits
- **CPU Limit:** `1000m` (1 CPU core)
- **Memory Limit:** `512Mi`
- **CPU Request:** `100m`
- **Memory Request:** `128Mi`

### Health Checks
- **Liveness Probe:** 
  - Type: `HTTP`
  - Path: `/health`
  - Port: `3000`
  - Initial Delay: `30s`
  - Period: `30s`

- **Readiness Probe:**
  - Type: `HTTP` 
  - Path: `/health`
  - Port: `3000`
  - Initial Delay: `5s`
  - Period: `10s`

## Step 8: Deploy the Application

1. **Review your configuration**
2. **Click "Install"**
3. **Wait for deployment** (this may take a few minutes)
4. **Check the status** in the Apps dashboard

## Step 9: Verify Deployment

1. **Check App Status:**
   - Go to TrueNAS Scale Apps dashboard
   - Verify your app shows as "Running"

2. **View Logs:**
   - Click on your app in the dashboard
   - Click "Logs" to see application output
   - Look for "🚀 Dash Admin Dashboard running on port 3000"

3. **Test Access:**
   - **Direct IP:** `http://your-truenas-ip:30300`
   - **Health Check:** `http://your-truenas-ip:30300/health`

## Step 10: Set Up Domain Access (Optional)

### Option 1: Router DNS Configuration
1. Access your router's admin panel
2. Add DNS entry:
   - **Hostname:** `management.dashfinanceapp.com`
   - **IP Address:** `your-truenas-ip`

### Option 2: Local Hosts File
**Windows:** Edit `C:\Windows\System32\drivers\etc\hosts`
**macOS/Linux:** Edit `/etc/hosts`

Add:
```
your-truenas-ip management.dashfinanceapp.com
```

### Option 3: Reverse Proxy (Recommended for Production)
1. Install **Nginx Proxy Manager** as another TrueNAS Scale app
2. Create proxy host:
   - **Domain:** `management.dashfinanceapp.com`
   - **Forward to:** `your-truenas-ip:30300`
   - **Enable SSL** if desired

## Troubleshooting

### App Won't Start
1. **Check environment variables** - ensure all required Firebase variables are set
2. **View logs** in TrueNAS Scale Apps dashboard
3. **Verify image** exists in your container registry
4. **Check port conflicts** - ensure port 30300 isn't used by another app

### Can't Access Application
1. **Test health endpoint:** `http://your-truenas-ip:30300/health`
2. **Check firewall rules** on TrueNAS Scale
3. **Verify app is running** in Apps dashboard
4. **Check logs** for startup errors

### Firebase Authentication Issues
1. **Verify Firebase credentials** are correctly extracted from JSON file
2. **Check private key format** - ensure `\n` characters are preserved
3. **Test with demo mode** by removing Firebase environment variables

## Managing Your App

### Updating the Application
1. **Build and push new image:**
   ```bash
   docker build -t yourusername/dash-admin:latest .
   docker push yourusername/dash-admin:latest
   ```

2. **Update in TrueNAS Scale:**
   - Go to Apps dashboard
   - Click on your app
   - Click "Update" 
   - Confirm update

### Backup Configuration
1. **Export app configuration:**
   - Click on your app in TrueNAS Scale
   - Click "Export"
   - Save the configuration file

2. **Backup data:**
   ```bash
   tar -czf dash-admin-backup.tar.gz /mnt/tank/appdata/dash-admin
   ```

## Security Best Practices

1. **Use strong JWT secrets** in production
2. **Configure proper firewall rules**
3. **Use SSL/TLS certificates** for external access
4. **Regularly update** the application image
5. **Monitor access logs** for suspicious activity
6. **Limit admin email domains** appropriately

Your Dash Admin Dashboard is now running as a proper TrueNAS Scale Custom App!
