{"name": "dash-admin-dashboard", "version": "1.0.0", "description": "Admin dashboard for Dash Finance App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dashboard", "admin", "finance", "dash"], "author": "Dash Finance Team", "license": "ISC", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "firebase-admin": "^13.4.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "qrcode": "^1.5.4", "resend": "^4.6.0", "speakeasy": "^2.0.0"}, "devDependencies": {"nodemon": "^3.1.9"}}